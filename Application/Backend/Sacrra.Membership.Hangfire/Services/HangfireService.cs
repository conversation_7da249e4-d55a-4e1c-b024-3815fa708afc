using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Database;
using Sacrra.Membership.Hangfire.DTOs;
using Sacrra.Membership.Hangfire.HangfireWrapper;

namespace Sacrra.Membership.Hangfire.Services
{
    public class HangfireService
    {
        private readonly AppDbContext _dbContext;

        public HangfireService(AppDbContext dbContext)
        {
            this._dbContext = dbContext;
        }

        public void AddScheduledJob(HangFireDailyJobInputDTO hangFireDailyJobInputDto)
        {
            HangfireHelper.AddCodeHangfireJob(hangFireDailyJobInputDto);

            HangfireHelper.AddCodeJobToDatabase(_dbContext, hangFireDailyJobInputDto);

        }
        
        public void RemoveScheduledJob(string jobName)
        {
            HangfireHelper.RemoveCodeHangfireJob(jobName);
            
            HangfireHelper.RemoveCodeJobToDatabase(_dbContext, jobName);
        }


        public ScheduledJobDTO[] GetRecurringJobs()
        {
            return HangfireHelper.GetRecurringJobs(_dbContext);
    
        }

        public void RunRecurringJobManually(string jobName)
        {
            HangfireHelper.RunRecurringJobManually(jobName);

        }

        public ScheduledJobHistoryDTO[] GetJobHistory()
        {
            var dbLogs = _dbContext.HangfireJobLogs
                .OrderByDescending(h => h.Id)
                .Take(100)
                .Result.Select(h => new ScheduledJobHistoryDTO() {
                    Id = h.Id,
                    JobName = h.JobName.Replace("\"", ""),
                    Status = h.Status,
                    Details = h.Details,
                    Time = h.Time.ToString("yyyy-MM-dd HH:mm")
                }).AsNoTracking().ToArray();
            return dbLogs;
    
        }
    }
}
