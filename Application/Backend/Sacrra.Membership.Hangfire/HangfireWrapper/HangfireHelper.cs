using Hangfire;
using Hangfire.Common;
using Hangfire.Storage;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Hangfire.DTOs;

namespace Sacrra.Membership.Hangfire.HangfireWrapper;

public abstract class HangfireHelper
{
    public static void CleanupJobs(AppDbContext dbContext)
    {
        using var connection = JobStorage.Current.GetConnection();
        foreach (var recurringJob in connection.GetRecurringJobs())
        {
            RecurringJob.RemoveIfExists(recurringJob.Id);
        }

        // If a job started processing on recreation it will try and carry on processing that
        // job this is a problem if we have a badly written job and we restart the application
        // it will start processing outside of it's appointed time
        dbContext.Database.ExecuteSqlRaw("DELETE FROM [HangFire].[Job] WHERE StateName = 'Processing'");
    }

    public static void AddCodeHangfireJob(HangFireDailyJobInputDTO hangFireDailyJobInputDto)
    {
        var typeQuery = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(assembly => assembly.GetTypes().Result.Where(t => t.FullName == hangFireDailyJobInputDto.MethodClass)).Result.FirstOrDefault();
        if (typeQuery == null)
        {
            throw new Exception($"Type {hangFireDailyJobInputDto.MethodClass} not found");
        }

        var methodQuery = typeQuery.GetMethod(hangFireDailyJobInputDto.MethodName);
        if (methodQuery == null)
        {
            throw new Exception($"Method {hangFireDailyJobInputDto.MethodName} not found in type {hangFireDailyJobInputDto.MethodClass}");
        }

        Job job;
        if (string.IsNullOrWhiteSpace(hangFireDailyJobInputDto.Parameters))
        {
            job = new Job(typeQuery, methodQuery);
        }
        else
        {
            var parameters = hangFireDailyJobInputDto.Parameters.Split(',') as object[];
            job = new Job(typeQuery, methodQuery, parameters);
        }

        var manager = new RecurringJobManager();
        var reoccuringOptions = new RecurringJobOptions()
        {
            TimeZone = TimeZoneInfo.Local
        };
        manager.AddOrUpdate(hangFireDailyJobInputDto.JobName, job, hangFireDailyJobInputDto.CronString);
    }

    public static void RemoveCodeHangfireJob(string jobName)
    {
        RecurringJob.RemoveIfExists(jobName);
    }

    public static void RemoveCodeJobToDatabase(AppDbContext dbContext, string jobName)
    {
        var dbJob = dbContext.HangfireScheduledJobs.Result.FirstOrDefault(c => c.JobName == jobName);
        if (dbJob == null) return;
        dbContext.HangfireScheduledJobs.Remove(dbJob);
        dbContext.SaveChanges();
    }

    public static void AddCodeJobToDatabase(AppDbContext dbContext, HangFireDailyJobInputDTO hangFireDailyJobInputDto)
    {
        var dbJob = dbContext.HangfireScheduledJobs.Result.FirstOrDefault(c => c.JobName == hangFireDailyJobInputDto.JobName);

        if (dbJob == null)
        {
            dbJob = new HangfireScheduledJob() { JobName = hangFireDailyJobInputDto.JobName };
            dbContext.HangfireScheduledJobs.Add(dbJob);
        }

        dbJob.CronString = hangFireDailyJobInputDto.CronString;
        dbJob.MethodClass = hangFireDailyJobInputDto.MethodClass;
        dbJob.MethodName = hangFireDailyJobInputDto.MethodName;
        dbJob.Parameters = hangFireDailyJobInputDto.Parameters;
        dbContext.SaveChanges();
    }

    public static void ConfigureGlobalErrorHandler()
    {
        GlobalJobFilters.Filters.Add(new AutomaticRetryAttribute { Attempts = 0 });
        GlobalJobFilters.Filters.Add(new HangfireJobExecutionLogFilter());
        GlobalJobFilters.Filters.Add(new HangfireJobFailureHandler());
    }

    public static void RescheduleDatabaseJobs(AppDbContext dbContext)
    {
        foreach (var job in dbContext.HangfireScheduledJobs)
        {
            var jobModel = new HangFireDailyJobInputDTO()
            {
                JobName = job.JobName,
                CronString = job.CronString,
                MethodClass = job.MethodClass,
                MethodName = job.MethodName,
                Parameters = job.Parameters
            };
            AddCodeHangfireJob(jobModel);
        }
    }

    public static ScheduledJobDTO[] GetRecurringJobs(AppDbContext dbContext)
    {
        var dbJobList = dbContext.HangfireScheduledJobs.Result.Select(c => new ScheduledJobDTO()
        {
            JobName = c.JobName,
            MethodName = c.MethodName,
            ClassName = c.MethodClass,
            Parameters = c.Parameters,
            Cron = c.CronString
        }).OrderBy(m => m.JobName)
            .ToArray();
        return dbJobList;
    }

    public static void RunRecurringJobManually(string jobName)
    {
        RecurringJob.TriggerJob(jobName);
    }
}
