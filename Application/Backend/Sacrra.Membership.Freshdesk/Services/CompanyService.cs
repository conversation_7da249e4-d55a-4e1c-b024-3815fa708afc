using Hangfire;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Freshdesk.DTOs;
using Sacrra.Membership.Business.Exceptions;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hangfire.Server;

namespace Sacrra.Membership.Freshdesk.Services
{
    public class CompanyService
    {
        private readonly IConfiguration _configuration;
        private readonly MembersService _membersService;
        public CompanyService(IConfiguration configuration, MembersService membersService)
        {
            _configuration = configuration;
            _membersService = membersService;
        }
        public void CreateCompanies()
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                var dbMembers = _membersService.GetAllMembersForFreshdesk();

                foreach (var member in dbMembers)
                {
                    //Check if company exists in Freshdesk first
                    var freshdeskCompany = SeachCompany(member.Id);

                    if (freshdeskCompany == null)
                    {
                        var newCompany = new
                        {
                            name = member.RegisteredName,
                            custom_fields = new
                            {
                                member_id = member.Id,
                                stakeholder_manager_email = (member.StakeholderManager != null) ? (string.IsNullOrEmpty(member.StakeholderManager.Email)) ? "" : member.StakeholderManager.Email : ""
                            },
                            domains = (member.Domains.Count > 0) ? member.Domains.Select(i => i.Name).ToArray() : Array.Empty<string>(),
                        };

                        RestRequest request = new()
                        {
                            Resource = "api/v2/companies",
                            Method = Method.Post,
                        };

                        request.AddJsonBody(newCompany);
                        var response = client.Execute(request);
                    }
                    else if(freshdeskCompany != null)
                    {
                        UpdateCompany(freshdeskCompany, member);
                    }
                }
            }
            catch(Exception ex)
            {
                Log.Error(ex, "Unable to create companies in Freshdesk");
            }
        }

        public async Task<List<CompanyDTO>> GetCompanies()
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = "api/v2/companies",
                    Method = Method.Get
                };
                var response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<List<CompanyDTO>>(response.Content);

                    return data;
                }

                return new List<CompanyDTO>();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Unable to get companies from Freshdesk");
                return new List<CompanyDTO>();
            }
        }
        public void CreateContacts()
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                var dbContacts = _membersService.GetAllMemberContactsForFreshdesk();

                foreach (var contact in dbContacts)
                {
                    var freshdeskContact = SeachContact(contact.Email);
                    if (freshdeskContact == null)
                    {
                        //Get the company from Freshdesk so it can be linked to the contact
                        var contactCompany = SeachCompany(contact.MemberId);

                        if(contactCompany != null)
                        {
                            RestRequest request = new()
                            {
                                Resource = "api/v2/contacts",
                                Method = Method.Post,
                            };

                            //Create the contact in Freshdesk in link it to the associated company
                            var newContact = new
                            {
                                email = contact.Email,
                                name = contact.FirstName + " " + contact.Surname,
                                phone = contact.OfficeTelNumber,
                                mobile = contact.CellNumber,
                                /*unique_external_id = contact.Id.ToString(), /*This field is not supported in Growth plan.
                                                                                I have created 'contact_id' as custom field to store the contact Id*/
                                custom_fields = new
                                {
                                    contact_id = contact.Id
                                },
                                company_id = contactCompany.Id,
                                view_all_tickets = true,
                                address = contact.HeadOfficePhysicalAddress,
                                job_title = contact.JobTitle,
                                description = contact.ContactType
                            };

                            request.AddJsonBody(newContact);
                            var response = client.Execute(request);
                        }
                    }
                    else if(freshdeskContact != null)
                    {
                        UpdateContact(freshdeskContact, contact);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Unable to create companies in Freshdesk");
            }
        }
        public async Task<List<ContactDTO>> GetContacts(string companyId)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"api/v2/contacts?company_id{companyId}",
                    Method = Method.Get
                };
                var response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<List<ContactDTO>>(response.Content);

                    return data;
                }

                return new List<ContactDTO>();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Unable to get contacts from Freshdesk for company Id " + companyId);
                return new List<ContactDTO>();
            }
        }
        public async Task<List<ContactDTO>> GetContacts()
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"api/v2/contacts",
                    Method = Method.Get
                };
                var response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<List<ContactDTO>>(response.Content);

                    return data;
                }

                return new List<ContactDTO>();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Unable to get contacts from Freshdesk");
                return new List<ContactDTO>();
            }
        }

        public CompanyDTO SeachCompany(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                return null;
            }

            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"api/v2/companies/autocomplete?name={name}",
                    Method = Method.Get
                };
                var response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<SearchCompanyDTO>(response.Content);

                    if (data != null)
                    {
                        if (data.Companies != null)
                        {
                            if (data.Companies.Count > 0)
                            {
                                return data.Companies[0];
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get company with name {name} from Freshdesk");
                return null;
            }
        }
        public CompanyDTO SeachCompany(int memberId)
        {
            if (memberId <= 0)
            {
                return null;
            }

            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = "api/v2/search/companies?query=" + '"' + "member_id:" + memberId + '"',
                    Method = Method.Get
                };
                var response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<FilterCompanyDTO>(response.Content);

                    if (data != null)
                    {
                        if (data.Results != null)
                        {
                            if (data.Results.Count > 0)
                            {
                                return data.Results[0];
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get company with member Id {memberId} from Freshdesk");
                return null;
            }
        }
        public ContactDTO SeachContact(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return null;
            }

            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"api/v2/contacts?email={email}",
                    Method = Method.Get
                };
                var response = client.Execute(request);

                if(response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<List<ContactDTO>>(response.Content);

                    if (data != null)
                    {
                        if (data.Count > 0)
                        {
                            return data[0];
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get contact with email {email} from Freshdesk");
                return null;
            }
        }
        public void UpdateCompany(CompanyDTO company, Member member)
        {
            if (company == null)
            {
                return;
            }
            if(member.Domains.Count > 0)
            {

            }

            var client = Helpers.GetRestClient(_configuration);

            RestRequest request = new()
            {
                Resource = $"api/v2/companies/{company.Id}",
                Method = Method.Put,
            };

            var updateCompany = new
            {
                name = company.Name,
                custom_fields = new
                {
                    member_id = member.Id,
                    stakeholder_manager_email = (member.StakeholderManager != null) ? (string.IsNullOrEmpty(member.StakeholderManager.Email))? "" : member.StakeholderManager.Email : ""
                },
                note = $"Last updated on {string.Format("{0:yyyy-MM-dd hh:mm}", DateTime.Now)}",
                domains = (member.Domains.Count > 0)? member.Domains.Select(i => i.Name).ToArray() : Array.Empty<string>(),
            };

            request.AddJsonBody(updateCompany);
            var response = client.Execute(request);
        }
        public void UpdateContact(ContactDTO contact, FreshdeskMemberContactDTO memberContact)
        {
            if (contact == null)
            {
                return;
            }

            var client = Helpers.GetRestClient(_configuration);

            RestRequest request = new()
            {
                Resource = $"api/v2/contacts/{contact.Id}",
                Method = Method.Put,
            };

            //Create the contact in Freshdesk in link it to the associated company
            var updateContact = new
            {
                email = contact.Email,
                name = memberContact.FirstName + " " + memberContact.Surname,
                phone = memberContact.OfficeTelNumber,
                mobile = memberContact.CellNumber,
                /*unique_external_id = contact.Id.ToString(), /*This field is not supported in Growth plan.
                                                                I have created 'contact_id' as custom field to store the contact Id*/
                custom_fields = new
                {
                    contact_id = memberContact.Id
                },
                company_id = contact.CompanyId,
                view_all_tickets = true,
                address = memberContact.HeadOfficePhysicalAddress,
                job_title = memberContact.JobTitle,
                description = memberContact.ContactType
            };

            request.AddJsonBody(updateContact);
            var response = client.Execute(request);
        }

        public CompanyDTO GetCompanyById(long id)
        {
            if (id <= 0)
            {
                return null;
            }

            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"/api/v2/companies/{id}",
                    Method = Method.Get
                };
                var response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<CompanyDTO>(response.Content);

                    return data;
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get company with id {id} from Freshdesk");
                return null;
            }
        }
    }
}
