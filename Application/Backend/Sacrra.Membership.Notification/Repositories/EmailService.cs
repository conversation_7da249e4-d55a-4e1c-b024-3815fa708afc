using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Text.RegularExpressions;

namespace Sacrra.Membership.Notification.Repositories
{
    public class EmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly StringHelper _stringHelper;
        private readonly IHostingEnvironment _env;
        private readonly AppDbContext _dbContext;

        public EmailService(IOptions<EmailSettings> emailSettings, IHostingEnvironment env, AppDbContext dbContext)
        {
            _emailSettings = emailSettings.Value;
            _stringHelper = new StringHelper();
            _env = env;
            _dbContext = dbContext;
        }

        public void SendEmail(string recepientAddress, string recepientName, string subject, string templateName, 
            List<KeyValuePair<string, string>> placeholders, List<string> ccAddressList = null, string ccAddress = "", string bCCAddress = "", 
            int entityId = 0, WorkflowEnum workflow = WorkflowEnum.NotApplicable,
            EmailReasonEnum emailReason = EmailReasonEnum.NotSpecified, EmailRecipientTypeEnum recipientType = EmailRecipientTypeEnum.NotSpecified)
        {
            if (!string.IsNullOrWhiteSpace(recepientAddress))
            {
                SendAndSave(recepientAddress, recepientName, subject, templateName, placeholders, ccAddressList, ccAddress, bCCAddress,
                    entityId, workflow, emailReason, recipientType);
            }
        }

        public void SendEmail(string recepientAddress, string recepientName, string subject, string messageBody, string ccAddress = "", string bCCAddress = "")
        {
            if (!IsValidEmail(recepientAddress))
                throw new Exception("Unable to send mail to " + recepientAddress + ". Invalid email address");

            if (IsMailDetailsValid(recepientAddress, messageBody, subject))
            {
                try
                {
                    var mailMessage = MailMessageBuilder(subject, messageBody, recepientAddress, ccAddress, bCCAddress, null);

                    if (!string.IsNullOrEmpty(_emailSettings.SmtpUsername))
                    {
                        if (_emailSettings.SendEmailOnEnv)
                        {
                            var smtpClient = SmtpClientBuilder(SmtpDeliveryMethod.Network);

                            smtpClient.Send(mailMessage);
                        }
                        else
                        {
                            SaveEmail(recepientAddress, recepientName, subject, "", null, mailBody: messageBody, ccAddress: ccAddress, bCCAddress: bCCAddress);
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Unable to send mail to: " + recepientAddress + ". Error: " + ex.Message);
                }
            }
        }

        private void SendAndSave(string recepientAddress, string recepientName, string subject, string templateName, 
            List<KeyValuePair<string, string>> placeholders, List<string> ccAddressList = null,
            string ccAddress = "", string bCCAddress = "", int entityId = 0, WorkflowEnum workflow = WorkflowEnum.NotApplicable,
            EmailReasonEnum emailReason = EmailReasonEnum.NotSpecified, EmailRecipientTypeEnum recipientType = EmailRecipientTypeEnum.NotSpecified)
        {
            if (!CanSendEmail(recepientAddress, entityId, workflow, emailReason, recipientType))
            {
                throw new Exception("Email retry limit exceeded");
            }

            if (!IsValidEmail(recepientAddress) && CanSendEmail(recepientAddress, entityId, workflow, emailReason, recipientType))
            {
                UpdateEmailQueue(recepientAddress, entityId, workflow, emailReason, recipientType);

                throw new Exception("Unable to send mail to " + recepientAddress + ". Invalid email address");
            }
                

            string templateFilePath = _emailSettings.EmailTemplateFilePath + templateName;
            var fullTemplateFilePath = GetFullEmailTemplateFilePath(templateFilePath);

            if (!string.IsNullOrWhiteSpace(fullTemplateFilePath))
            {
                string messageBody = _stringHelper.GetContentTemplateFromFile(fullTemplateFilePath);

                if (IsMailDetailsValid(recepientAddress, messageBody, subject))
                {
                    
                    messageBody = messageBody.Replace("[RecepientAddress]", recepientAddress)
                        .Replace("[RecepientName]", recepientName);

                    if (messageBody.Contains("[Member]"))
                    {
                        messageBody = messageBody.Replace("[Member]", recepientName);
                    }

                    if (placeholders != null)
                    {
                        foreach (KeyValuePair<string, string> placeholder in placeholders)
                        {
                            messageBody = messageBody.Replace(placeholder.Key, placeholder.Value);
                        }
                    }


                    try
                    {
                        var mailMessage = MailMessageBuilder(subject, messageBody, recepientAddress, ccAddress, bCCAddress, ccAddressList);
                        if (_emailSettings.SendEmailOnEnv)
                        {
                            if (_emailSettings.SmtpPort != 0)
                            {
                                var smtpClient = SmtpClientBuilder(SmtpDeliveryMethod.Network);

                                smtpClient.Send(mailMessage);

                                EmailQueue emailQueue = new EmailQueue
                                {
                                    Email = recepientAddress,
                                    DateSent = DateTime.Now,
                                    Status = EmailStatusEnum.Sent,
                                    LastActioned = DateTime.Now,
                                    Workflow = workflow,
                                    EntityId = entityId,
                                    Reason = emailReason,
                                    RecipientType = recipientType
                                };

                                CreateQueue(emailQueue);
                                SaveEmail(recepientAddress, recepientName, subject, templateName, placeholders,
                                    ccAddressList, ccAddress, bCCAddress, entityId, messageBody, workflow, emailReason, recipientType);
                            }
                        }
                        else
                        {
                            SaveEmail(recepientAddress, recepientName, subject, templateName, placeholders,
                                    ccAddressList, ccAddress, bCCAddress, entityId, messageBody, workflow, emailReason, recipientType);
                        }
                    }
                    catch (Exception ex)
                    {
                        UpdateEmailQueue(recepientAddress, entityId, workflow, emailReason, recipientType);

                        throw new Exception("Unable to send mail to: " + recepientAddress + ". Error: " + ex.Message);
                    }
                }
            }
        }

        private void SaveEmail(string recepientAddress, string recepientName, string subject, string templateName,
            List<KeyValuePair<string, string>> placeholders, List<string> ccAddressList = null,
            string ccAddress = "", string bCCAddress = "", int entityId = 0, string mailBody = "", WorkflowEnum workflow = WorkflowEnum.NotApplicable,
            EmailReasonEnum emailReason = EmailReasonEnum.NotSpecified, EmailRecipientTypeEnum recipientType = EmailRecipientTypeEnum.NotSpecified)
        {
            _dbContext.EmailAuditLogs.Add(new EmailAuditLog()
            {
                RecipientAddress = recepientAddress,
                RecipientName = recepientName,
                Subject = subject,
                TemplateName = templateName,
                Placeholders = JsonConvert.SerializeObject(placeholders),
                CcAddressList = JsonConvert.SerializeObject(ccAddressList),
                CcAddress = ccAddress,
                BccAddress = bCCAddress,
                SrnId = entityId,
                MailBody = mailBody,
                Workflow = workflow.ToString(),
                Reason = emailReason.ToString(),
                RecipientType = recipientType.ToString(),
                DateSent = DateTime.Now
            });
            _dbContext.SaveChanges();
        }

        private bool IsMailDetailsValid(string recipientAddress, string messageBody, string subject)
        {
            return (
                 !string.IsNullOrWhiteSpace(recipientAddress)
                 && !string.IsNullOrWhiteSpace(messageBody)
                 && !string.IsNullOrWhiteSpace(subject)
                 && !string.IsNullOrWhiteSpace(_emailSettings.SmtpServer)
                 );
        }

        private bool IsValidEmail(string emailAddress)
        {
            Regex regex = new Regex(@"^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$",
            RegexOptions.CultureInvariant | RegexOptions.Singleline);
            bool isValidEmail = regex.IsMatch(emailAddress);

            return isValidEmail;
        }

        private void CreateQueue(EmailQueue emailQueue)
        {
            if(emailQueue != null)
            {
                _dbContext.EmailQueues.Add(emailQueue);
                _dbContext.SaveChanges();
            }
        }

        private int GetRetryCount(string email, int entityId, WorkflowEnum workflow, EmailReasonEnum emailReason,
            EmailRecipientTypeEnum recipientType)
        {
            int retryCount = 0;

            var emailQueue = GetPendingQueue(email, entityId, workflow, emailReason, recipientType);

            if (emailQueue != null)
                retryCount = emailQueue.RetryCount;

            return retryCount;
        }

        private bool CanSendEmail(string email, int entityId, WorkflowEnum workflow, EmailReasonEnum emailReason,
            EmailRecipientTypeEnum recipientType)
        {
            bool canSend = false;

            int retryCount = GetRetryCount(email, entityId, workflow, emailReason, recipientType);

            if(retryCount < _emailSettings.RetryLimit)
            {
                canSend = true;
            }

            return canSend;
        }

        private EmailQueue GetPendingQueue(string email, int entityId, WorkflowEnum workflow, EmailReasonEnum emailReason,
            EmailRecipientTypeEnum recipientType)
        {
            var emailQueue = _dbContext.EmailQueues.FirstOrDefault(i => i.Email == email && i.EntityId == entityId
                    && i.Workflow == workflow && i.Reason == emailReason && i.RecipientType == recipientType
                    && i.Status == EmailStatusEnum.Pending);

            if(emailQueue == null)
            {
                emailQueue = _dbContext.EmailQueues.FirstOrDefault(i => i.Email == email && i.EntityId == entityId
                    && i.Workflow == workflow && i.Reason == emailReason && i.RecipientType == recipientType
                    && i.Status == EmailStatusEnum.Failed);
            }

            return emailQueue;
        }

        private void UpdateEmailQueue(EmailQueue emailQueue)
        {
            if(emailQueue != null)
            {
                if(emailQueue.Id > 0)
                {
                    _dbContext.Update(emailQueue);
                    _dbContext.SaveChanges();
                }
            }
        }
        private void UpdateEmailQueue(string recepientAddress, int entityId, WorkflowEnum workflow, EmailReasonEnum emailReason,
            EmailRecipientTypeEnum recipientType)
        {
            var emailQueue = GetPendingQueue(recepientAddress, entityId, workflow, emailReason, recipientType);

            if (emailQueue != null)
            {
                if (emailQueue.RetryCount >= _emailSettings.RetryLimit)
                {
                    emailQueue.Status = EmailStatusEnum.Failed;
                }
                else
                {
                    emailQueue.Status = EmailStatusEnum.Pending;
                    emailQueue.RetryCount++;
                }

                emailQueue.LastActioned = DateTime.Now;

                UpdateEmailQueue(emailQueue);
            }

            else
            {
                emailQueue = new EmailQueue
                {
                    Email = recepientAddress,
                    Status = EmailStatusEnum.Pending,
                    LastActioned = DateTime.Now,
                    Workflow = workflow,
                    EntityId = entityId,
                    Reason = emailReason,
                    RecipientType = recipientType
                };

                CreateQueue(emailQueue);
            }
        }

        private MailMessage MailMessageBuilder(string subject, string messageBody, string recepientAddress, string ccAddress, string bccAddress, List<string> ccAddressList)
        {
            MailMessage mailMessage = new MailMessage
            {
                From = new MailAddress(_emailSettings.EmailFromAddress, _emailSettings.EmailFromName),
                Subject = subject,
                Body = messageBody,
                IsBodyHtml = true,
                Priority = MailPriority.Normal
            };
            mailMessage.To.Add(recepientAddress);

            if (!string.IsNullOrWhiteSpace(ccAddress)) mailMessage.CC.Add(ccAddress);
            if (!string.IsNullOrWhiteSpace(bccAddress)) mailMessage.Bcc.Add(bccAddress);

            if (ccAddressList != null)
            {
                ccAddressList.ForEach(ccAddress =>
                {
                    mailMessage.CC.Add(ccAddress);
                });
            }

            return mailMessage;
        }

        private SmtpClient SmtpClientBuilder(SmtpDeliveryMethod deliveryMethod)
        {
            System.Net.NetworkCredential objCredentials = new()
            {
                UserName = _emailSettings.SmtpUsername,
                Password = _emailSettings.SmtpPassword
            };

            return new SmtpClient() { 
                Credentials = objCredentials,
                Host = _emailSettings.SmtpServer,
                Port = _emailSettings.SmtpPort,
                EnableSsl = _emailSettings.SmtpEnableSsl,
                DeliveryMethod = deliveryMethod
            };
        }

        private string GetFullEmailTemplateFilePath(string templateFilePath)
        {
            string fullTemplateFilePath;

            if (_env != null)
            {
                if (!string.IsNullOrEmpty(_env.WebRootPath))
                {
                    fullTemplateFilePath = Path.Combine(_env.WebRootPath, templateFilePath);
                }
                else
                {
                    fullTemplateFilePath = Path.Combine(Directory.GetCurrentDirectory(), templateFilePath);
                }
            }
            else
            {
                fullTemplateFilePath = Path.Combine(Directory.GetCurrentDirectory(), templateFilePath);
            }

            return fullTemplateFilePath;
        }
    }
}
