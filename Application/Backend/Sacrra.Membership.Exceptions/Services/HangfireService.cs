using AutoMapper;
using Hangfire;
using Hangfire.Storage;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System;
using System.Linq;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Exceptions.Services
{
    public class HangfireService
    {
        private readonly AppDbContext _dbContext;
        private readonly ExceptionsRepository _exceptionsService;
        private readonly CamundaRepository _camundaRepository;
        public IMapper _mapper { get; }

        public HangfireService(AppDbContext dbContext, ExceptionsRepository exceptionsService,
            CamundaRepository camundaRepository, IMapper mapper )
        {
            _dbContext = dbContext;
            _exceptionsService = exceptionsService;
            _camundaRepository = camundaRepository;
            _mapper = mapper;
        }

        public HangfireJobGetResource GetJob(int id)
        {
            var selectRecord = _dbContext.Set<HangfireJob>()
                    .AsNoTracking()
                .FirstOrDefault(s => s.Id == id);

            var returnRecord = _mapper.Map<HangfireJobGetResource>(selectRecord);

            return returnRecord;
        }

        public void CreateCamundaTaskForNewDWExceptions()
        {
            var dwFilter = new ExceptionFilterResource();

            dwFilter.IsSentToPortal = "NO";

            var exceptions = _exceptionsService.GetAllExternal(dwFilter);

            _camundaRepository.CreateTaskForDWException(exceptions);
        }
    }
}
