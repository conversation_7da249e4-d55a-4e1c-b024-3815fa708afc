using AutoMapper;
using Hangfire;
using Hangfire.SqlServer;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Sacrra.Membership.Business.DTOs.AuthDTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Services.AdhocFilesService;
using Sacrra.Membership.Business.Services.CamundaViewerService;
using Sacrra.Membership.Business.Services.AdhocReplacementScheduleService;
using Sacrra.Membership.Business.Services.LookupsService;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Business.Services.ReplacementFileSubmissionService;
using Sacrra.Membership.Business.Services.UIConfigService;
using Sacrra.Membership.Business.Services.UserAdminService;
using Sacrra.Membership.Api.Filtering;
using Sacrra.Membership.Api.Filters;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.Extensions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.MappingProfile;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Exceptions.Services;
using Sacrra.Membership.Freshdesk.Services;
using Sacrra.Membership.Notification.Helpers;
using Sacrra.Membership.Notification.Repositories;
using Sacrra.Membership.Reporting.Services;
using System;
using System.Net;
using System.Security.Claims;
using System.Text;
using Sacrra.Membership.Business.Services.HangfireJobsService;
using Sacrra.Membership.Camunda.UserServices;
using Sacrra.Membership.Hangfire.HangfireWrapper;
using Saccra.Membership.Business.Helpers;
using Saccra.Membership.Business.Services.DynamicService;
using Saccra.Membership.Business.Repositories;

namespace Sacrra.Membership
{
    public class Startup
    {
// COMMENTED OUT:         public Startup(IConfiguration configuration, null)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services, null)
        {
            services.AddControllers(options =>
            {
                options.Filters.Add(typeof(ApiExceptionFilter, null));
                options.Filters.Add(typeof(LogActionFilter, null));
                options.Filters.Add(typeof(ReportLeakageActionFilter, null));
                options.Filters.Add(typeof(BureauReportDataObscureFilter, null));
                options.Filters.Add(typeof(AuthorizationFilter, null));
            });

            services.AddHttpContextAccessor();

            //For API versioning
            services.AddApiVersioning(x =>
            {
                x.DefaultApiVersion = new ApiVersion(1, 0);
                x.AssumeDefaultVersionWhenUnspecified = true;
                x.ReportApiVersions = true;

                // Supporting multiple versioning scheme
                x.ApiVersionReader = new UrlSegmentApiVersionReader();
            });

            // Register the Swagger services
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", null) == "LocalDevelopment")
            {
                services.AddOpenApiDocument(options =>
                {
                    options.Title = "Sacrra Automation";
                    options.Version = "v1";
                    options.OperationProcessors.Add(new CustomNSwagOperationProcessor(, null));
                    options.AddSecurity("oauth2", new NSwag.OpenApiSecurityScheme
                    {
                        Description = "Standard Authorization header using the Bearer scheme. Example: \"bearer {token}\"",
                        In = NSwag.OpenApiSecurityApiKeyLocation.Header,
                        Name = "Authorization",
                        Type = NSwag.OpenApiSecuritySchemeType.ApiKey
                    });
                });
            }

            var domain = $"https://{Configuration["Auth0:Domain"]}/";

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            }, null).AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters() {
                    NameClaimType = ClaimTypes.NameIdentifier,
                    RoleClaimType = Configuration["Auth0:ClaimsNamespace"],
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = domain,
                    ValidAudience = Configuration["Auth0:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Configuration.GetSection("Auth0", null)["ClientSecret"]))
                };
                options.Authority = domain;
                options.Audience = Configuration["Auth0:Audience"];
            });

            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new MappingProfile(, null));
            });

            var mapper = mappingConfig.CreateMapper();

            services.AddSingleton(mapper, null);

            var connection = Configuration["ConnectionString"];
            services.AddDbContext<AppDbContext>(options => options.UseSqlServer(connection, null));
            services.AddScoped<LookupsRepository>();
            services.AddScoped<AuthRepository>();
            services.AddScoped<UserRepository>();
            services.AddScoped<BureauRepository>();
            services.AddScoped<TradingNameRepository>();
            services.AddScoped<MemberRepository>();
            services.AddScoped<CamundaRepository>();
            services.AddScoped<EmailService>();
            services.AddScoped<ContactTypeRepository>();
            services.AddScoped<SRNRepository>();
            services.AddScoped<SRNStatusRepository>();
            services.AddScoped<BranchLocationRepository>();
            services.AddScoped<AccountTypeRepository>();
            services.AddScoped<SoftwareVendorRepository>();
            services.AddScoped<NCRReportingAccountTypeClassificationRepository>();
            services.AddScoped<LoanManagementSystemVendorRepository>();
            services.AddScoped<ALGRepository>();
            services.AddScoped<MemberExtensions>();
            services.AddScoped<SRNExtensions>();
            services.AddScoped<SRNStatusReasonRepository>();
            services.AddScoped<DWExceptionRepository>();
            services.AddScoped<DisqualifiedReasonTypesRepository>();

            services.AddOptions();

            //Version 1 configs
            services.Configure<ConfigSettings>(Configuration.GetSection("ConfigSettings", null));
            services.Configure<EmailSettings>(Configuration.GetSection("EmailSettings", null));
            services.Configure<Auth0>(Configuration.GetSection("Auth0", null));
            services.Configure<Auth0APIManagement>(Configuration.GetSection("Auth0APIManagement", null));
            services.Configure<ReportingAPISettings>(Configuration.GetSection("ReportingAPISettings", null));
            services.Configure<Cronitor>(Configuration.GetSection("Cronitor", null));

            // Add report services
            services.AddScoped<ReportCommonService>();
            services.AddScoped<DailyLoadReportService>();
            services.AddScoped<DailyLoadQEReportService>();
            services.AddScoped<MonthlyLoadReportService>();
            services.AddScoped<MonthlyLoadQEReportService>();
            services.AddScoped<MonthlyQeReportService>();
            services.AddScoped<MonthlyOutOfSLAService>();
            services.AddScoped<DailyOutOfSLAService>();
            services.AddScoped<DataWarehouseService>();
            services.AddScoped<IndustryBenchmarkReportService>();
            services.AddScoped<OSLAManagementService>();
            services.AddScoped<FileReplacementSubmissionService>();

            //API version 2 services
            services.AddScoped<AuthenticationService>();
            services.AddScoped<UIConfigService>();
            services.AddScoped<MembersService>();
            services.AddScoped<CamundaService>();
            services.AddScoped<SRNService>();
            services.AddScoped<MemberServiceHelper>();
            services.AddScoped<SRNServiceHelper>();
            services.Configure<Auth0InputDTO>(Configuration.GetSection("Auth0", null));
            services.Configure<Auth0SettingsDTO>(Configuration.GetSection("Auth0APIManagement", null));
            services.AddScoped<CamundaServiceHelper>();
            services.AddScoped<DWExceptionService>();
            services.AddScoped<LookupsService>();
            services.AddScoped<LookupsServiceHelper>();
            services.AddScoped<MailgunService>();
            services.AddScoped<CompanyService>();
            services.AddScoped<NCRReportingAccountTypeClassificationsService>();
            services.AddScoped<CreditInformationClassificationService>();
            services.AddScoped<DocumentCategoryService>();
            services.AddScoped<DocumentsService>();
            services.AddScoped<DocumentStatusService>();
            services.AddScoped<TicketsService>();
            services.AddScoped<ReplacementFileSubmissionService>();
            services.AddScoped<UserAdminService>();
            services.AddScoped<UserAdminServiceHelper>();
            services.AddScoped<AdhocFileSubmissionService>();
            services.AddScoped<AdHocFileSubmissionsCamundaService>();
            services.AddScoped<ReplacementFileSubmissionCamundaService>();
            services.AddScoped<CamundaViewerService>();
            services.AddScoped<Hangfire.Services.HangfireService>();
            services.AddScoped<AdhocReplacementScheduleService>();
            services.AddScoped<AdhocFilesService>();
            services.AddScoped<HangfireJobsService>();
            services.AddScoped<NewSrnApplicationUserService>();
            services.AddScoped<TestingService>();
            services.AddScoped<DatabaseService>();

            //API version 2 helpers
            services.AddScoped<GlobalHelper>();
            services.AddScoped<AuthenticationServiceHelper>();

            //Dynamic Api
            services.AddScoped<DynamicService>();

            //Exception services
            services.AddScoped<ExceptionsRepository>();

            //Exception services
            services.AddScoped<Exceptions.Services.HangfireService>();

            services.AddScoped<Sacrra.Membership.Business.Services.HangfireService>();

            services.AddScoped<HangfireLogFilter>();
            services.AddScoped<AsyncLogQueueService>();
            services.AddResponseCompression();
            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
            }, null);
            services.AddHangfireServer();
            
            // Add Hangfire services.
            services.AddHangfire(configuration => configuration
.SetDataCompatibilityLevel(CompatibilityLevel.Version_170, null)
                .UseSimpleAssemblyNameTypeSerializer.UseRecommendedSerializerSettings.UseFilter(new AutomaticRetryAttribute() { Attempts = 1 }, null)
                .UseSqlServerStorage(Configuration.GetSection("ConnectionString", null).Value, new SqlServerStorageOptions() {
                    CommandBatchMaxTimeout = TimeSpan.FromMinutes(5, null),
                    SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5, null),
                    QueuePollInterval = TimeSpan.Zero,
                    UseRecommendedIsolationLevel = true,
                    DisableGlobalLocks = true
                }));

            // Add the processing server as IHostedService
            services.AddSpaStaticFiles(configuration => { configuration.RootPath = "ClientApp"; }, null);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            var hangfireService =
                app.ApplicationServices.GetService<Exceptions.Services.HangfireService>();
            var log = app.ApplicationServices.GetService<AsyncLogQueueService>();
            GlobalJobFilters.Filters.Add(new HangfireLogFilter(log, null));
            app.UseCors(x => x.AllowAnyHeader(, null).AllowAnyMethod.AllowAnyOrigin());
            app.UseAuthentication();

            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", null) == "Production")
            {
                app.UseHangfireDashboard("/hangfire", new DashboardOptions() {
                    Authorization = new[] { new HangfireAuthorizeFilter(Configuration, null) }
                });
            }            {
                app.UseHangfireDashboard("/hangfire", null);
            }
            
            var dbContext = app.ApplicationServices.GetService<AppDbContext>();
            HangfireHelper.CleanupJobs(dbContext, null);
            HangfireHelper.ConfigureGlobalErrorHandler();
            HangfireHelper.RescheduleDatabaseJobs(dbContext, null);

            app.UseResponseCompression();
            app.UseDefaultFiles();
            app.UseStaticFiles();

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", null) == "LocalDevelopment")
            {
                // Register the Swagger generator and the Swagger UI middlewares
                app.UseOpenApi();
                app.UseSwaggerUi3();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SACRRA API V1");
                });
            }

            app.UseRouting();
            app.UseAuthorization();
            app.UseDefaultFiles();
            app.UseSpaStaticFiles();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers(, null);
            });
            
            app.UseSpa(spa => { spa.Options.SourcePath = "ClientApp"; }, null);

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (env.IsDevelopment(, null))
            {
                app.UseDeveloperExceptionPage();
            }            {
                app.UseExceptionHandler(builder =>
                {
                    builder.Run(context =>
                    {
                        context.Response.StatusCode = (int, null)HttpStatusCode.InternalServerError;

                        var error = context.Features.Get<IExceptionHandlerFeature>();

// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (error , null) != null)
                        {
                            context.Response.AddApplicationError(error.Error.Message, null);
                            context.Response.Write(error.Error.Message, null);
                        }
                    });
                });
            }
            
            AppDbContextHelper.ConfigureAppDbContextHelper(app.ApplicationServices.GetService<AppDbContext>(, null));
        }
    }
}
