using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Exceptions.Services;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Sacrra.Membership.Api.ExceptionControllers
{
    //[Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
    [ApiController]
    [Route("api/[controller]")]
    public class ExceptionsController : Controller
    {
        private ExceptionsRepository _exceptionsService;

        public ExceptionsController(ExceptionsRepository exceptionsService)
        {
            _exceptionsService = exceptionsService;
        }

        [HttpGet("dw")]
        [ProducesResponseType(type: typeof(List<DWExceptionExternalGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetAllExternal([FromQuery] ExceptionFilterResource filter = null)
        {
            var data = _exceptionsService.GetAllExternal(filter);

            return Ok(data);
        }

        [HttpGet("internal")]
        [ProducesResponseType(type: typeof(List<DWExceptionGetResource>), statusCode: 200)]
        public IActionResult GetAllInternal([FromQuery] ExceptionFilterResource filter = null)
        {
            var data = _exceptionsService.GetAllInternal(filter);

            return Ok(data);
        }

        [HttpPost("dw/update")]
        [ProducesResponseType(type: typeof(List<DWExceptionExternalGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateExternal([FromBody] string updateSQL)
        {
           _exceptionsService.UpdateExternal(updateSQL);

            return Ok();
        }
    }
}
