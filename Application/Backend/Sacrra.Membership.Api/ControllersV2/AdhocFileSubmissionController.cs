using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RestSharp;
using Sacrra.Membership.Business.DTOs.AdHocFIlesDTO;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTO;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Services.AdhocFilesService;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
namespace Sacrra.Membership.Api.ControllersV2
{

    [Authorize(Roles = "Member, ALG Leader, Stakeholder Manager, Bureau")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]

    public class AdhocFileSubmissionController : Controller
    {

        private AdhocFileSubmissionService _adhocFileSubmissionService;
        private AdHocFileSubmissionsCamundaService _adhocFileSubmissionsCamundaService;

        public AdhocFileSubmissionController(AdhocFileSubmissionService adhocFilSubmissionService, AdHocFileSubmissionsCamundaService adHocFileSubmissionsCamundaService)
        {
            _adhocFileSubmissionService = adhocFilSubmissionService;
            _adhocFileSubmissionsCamundaService = adHocFileSubmissionsCamundaService;
        }

        public RestResponse RequestAdhocFileSubmission([FromBody] AdhocFileSubmissionInputDTO inputDTO)
        {
            return _adhocFileSubmissionService.RequestAdhocFileSubmission(inputDTO);
        }

        [HttpPost]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        public IActionResult AdhocFileSubmissionsDueRecheck()
        {
            try
            {
                _adhocFileSubmissionsCamundaService.AdhocFileSubmissionsDueRecheck(); // void method
                return NoContent(); // HTTP 204
            }
            catch (Exception ex)
            {
                // Log the error if needed
                return BadRequest("Failed to recheck ad hoc submissions.");
            }
        }


        [Authorize(Roles = "Bureau")]
        [HttpGet]
        [ProducesResponseType(typeof(List<AdhocFileSubmissionOutputDTO>), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<AdhocFileSubmissionOutputDTO> GetApprovedAdhocFiles()
        {
            return _adhocFileSubmissionService.GetApprovedAdhocFiles();
        }

        public void SubmitUnsuccessfulAdhocLoad([FromBody] BureauUnsuccessfulLoadInputDTO inputDTO)
        {
            _adhocFileSubmissionService.SubmitUnsuccessfulAdhocLoad(inputDTO, User);
        }

        public void SubmitAdhocLoadStatsAsync([FromBody] BureauLoadStatsInputDTO inputDTO)
        {
            _adhocFileSubmissionService.SubmitAdhocLoadStats(inputDTO, User);
        }

        //[HttpGet]
        //[ProducesResponseType(typeof(List<AdhocFileSubmissionOutputDTO>), 204)]        
        //[ProducesResponseType(typeof(void), 400)]
        //public List<AdhocFileSubmissionOutputDTO> GetAdhocFileNames()
        //{
        //    return _adhocFileSubmissionService.GetAdhocFileNames();
        //}


        [HttpGet("{adhocFileSubmissionId}")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public bool DoesAdHocSubmissionHaveLoadStats(int adhocFileSubmissionId)
        {
            return _adhocFileSubmissionService.DoesAdHocSubmissionHaveLoadStats(adhocFileSubmissionId);
        }

        [HttpGet("{adhocFileSubmissionId}/{bureauName}")]
        [ProducesResponseType(typeof(BureauLoadStatsOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public BureauLoadStatsOutputDTO GetBureauLoadStats(int adhocFileSubmissionId, string? bureauName = null)
        {
            return _adhocFileSubmissionService.GetBureauLoadStats(adhocFileSubmissionId, User, bureauName);
        }

        [Authorize(Roles = "Bureau")]
        [HttpPut]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public void UpdateAdhocLoadStatsAsync([FromBody] BureauLoadStatsInputDTO inputDTO)
        {
            _adhocFileSubmissionService.UpdateAdhocLoadStats(inputDTO, User);
        }
    }
}
