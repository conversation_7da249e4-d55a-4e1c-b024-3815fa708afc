using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.LoanManagementSystemVendor;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class LoanManagementSystemVendorsController : Controller
    {
        private readonly LoanManagementSystemVendorRepository _repository;

        public LoanManagementSystemVendorsController(LoanManagementSystemVendorRepository LoanManagementSystemVendorRepository)
        {
            _repository = LoanManagementSystemVendorRepository;
        }

        [Authorize]
        [HttpGet("{id}", Name = "GetLoanManagementSystemVendor")]
        public IActionResult Get(int id)
        {
            var entity = _repository.Get(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        public IActionResult List([FromQuery]NameListParams listParams)
        {
            var LoanManagementSystemVendors = _repository.List(listParams);

            Response.AddPagination(LoanManagementSystemVendors.CurrentPage, LoanManagementSystemVendors.PageSize, LoanManagementSystemVendors.TotalCount, LoanManagementSystemVendors.TotalPages);

            return Ok(LoanManagementSystemVendors);
        }

        public IActionResult Create([FromBody]LoanManagementSystemVendorCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                var errorModel = new ApiErrorResource
                {
                    Id = 0,
                    Message = "Loan Management System Vendor Name is required",
                    StatusCode = 400
                };
                return BadRequest(errorModel);
            }

            var id = _repository.Create(modelForCreate);

            return CreatedAtRoute("GetLoanManagementSystemVendor", new { controller = "LoanManagementSystemVendors", id }, id);
        }

        [Authorize(Roles = "Group Stakeholder Manager,Financial Administrator,User,Member,Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody]LoanManagementSystemVendorUpdateResource modelForUpdate)
        {
            if (!ModelState.IsValid)
            {
                var errorModel = new ApiErrorResource
                {
                    Id = 0,
                    Message = "Loan Management System Vendor Name is required",
                    StatusCode = 400
                };
                return BadRequest(errorModel);
            }

            return Ok(_repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager,Financial Administrator,User,Member,Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }
    }
}
