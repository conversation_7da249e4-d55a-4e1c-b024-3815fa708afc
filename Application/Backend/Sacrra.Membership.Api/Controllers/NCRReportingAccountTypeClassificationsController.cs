using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.NCRReportingAccountTypeClassification;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class NCRReportingAccountTypeClassificationsController : Controller
    {
        private readonly NCRReportingAccountTypeClassificationRepository _repository;

        public NCRReportingAccountTypeClassificationsController(NCRReportingAccountTypeClassificationRepository NCRReportingAccountTypeClassificationRepository)
        {
            _repository = NCRReportingAccountTypeClassificationRepository;
        }

        [Authorize]
        [HttpGet("{id}", Name = "GetNCRReportingAccountTypeClassification")]
        public IActionResult Get(int id)
        {
            var entity = _repository.Get(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        public IActionResult List([FromQuery]NameListParams listParams)
        {
            var NCRReportingAccountTypeClassifications = _repository.List(listParams);

            Response.AddPagination(NCRReportingAccountTypeClassifications.CurrentPage, NCRReportingAccountTypeClassifications.PageSize, NCRReportingAccountTypeClassifications.TotalCount, NCRReportingAccountTypeClassifications.TotalPages);

            return Ok(NCRReportingAccountTypeClassifications);
        }

        public IActionResult Create([FromBody]NCRReportingAccountTypeClassificationCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.Create(modelForCreate);

            return CreatedAtRoute("GetNCRReportingAccountTypeClassification", new { controller = "NCRReportingAccountTypeClassifications", id }, id);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody]NCRReportingAccountTypeClassificationUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }
    }
}
