using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.AccountType;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class AccountTypesController : Controller
    {
        private readonly AccountTypeRepository _repository;

        public AccountTypesController(AccountTypeRepository AccountTypeRepository)
        {
            _repository = AccountTypeRepository;
        }

        [Authorize]
        [HttpGet("{id}", Name = "GetAccountType")]
        public IActionResult Get(int id)
        {
            var entity = _repository.Get(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        public IActionResult List([FromQuery]NameListParams listParams)
        {
            var AccountTypes = _repository.List(listParams);

            Response.AddPagination(AccountTypes.CurrentPage, AccountTypes.PageSize, AccountTypes.TotalCount, AccountTypes.TotalPages);

            return Ok(AccountTypes);
        }

        public IActionResult Create([FromBody]AccountTypeCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.Create(modelForCreate);

            return CreatedAtRoute("GetAccountType", new { controller = "AccountTypes", id }, id);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody]AccountTypeUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }
    }
}
