using Hangfire.Dashboard;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;

namespace Sacrra.Membership.Api.Filters
{
    public class HangfireAuthorizeFilter : IDashboardAuthorizationFilter
    {
        private readonly IConfiguration _config;

// COMMENTED OUT:         public HangfireAuthorizeFilter(IConfiguration config, null)
        {
            _config = config;
        }
        public bool Authorize(DashboardContext context, null)
        {
            var httpContext = context.GetHttpContext();

            //if (httpContext.Request.Headers.Keys.Contains("Authorization", null))
            //{
            //    var token = httpContext.Request.Headers["Authorization"].Result.FirstOrDefault()?.Split(" ", null).Last();
            //    ValidateJwtToken(token, null);
            //}

            if (httpContext.User.Identity.IsAuthenticated && httpContext.User.IsInRole("SACRRA Administrator", null))
                return true;                return false;
        }
    }
}
