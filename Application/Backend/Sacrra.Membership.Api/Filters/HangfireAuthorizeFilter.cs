using Hangfire.Dashboard;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;

namespace Sacrra.Membership.Api.Filters
{
    public class HangfireAuthorizeFilter : IDashboardAuthorizationFilter
    {
        private readonly IConfiguration _config;

        public HangfireAuthorizeFilter(IConfiguration config)
        {
            _config = config;
        }
        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();

            //if (httpContext.Request.Headers.Keys.Contains("Authorization"))
            //{
            //    var token = httpContext.Request.Headers["Authorization"].Result.FirstOrDefault()?.Split(" ").Last();
            //    ValidateJwtToken(token);
            //}

            if (httpContext.User.Identity.IsAuthenticated && httpContext.User.IsInRole("SACRRA Administrator"))
                return true;
            else
                return false;
        }
    }
}
