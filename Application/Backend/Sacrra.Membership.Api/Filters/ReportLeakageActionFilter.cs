using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Sacrra.Membership.Database;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Exceptions;

namespace Sacrra.Membership.Api.Filters
{
    public class ReportLeakageActionFilter : ActionFilterAttribute
    {
        private static string[] CONTROLLERS_TO_FILTER = new string[] {
                "DailyLoadReportController",
                "DailyLoadQEReportController",
                "DailySrnReportController",
                "MonthlyLoadReportController",
                "MonthlyLoadQEReportController",
                "MonthlyQeReportController",
                "MonthlySrnReportController",
                "ReportCommonController",
                "MonthlyOutOfSLAReportController",
                "DailyOutOfSLAReportController"
        };

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var actionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;
            var controllerName = actionDescriptor.ControllerTypeInfo.Name;

            // If this isn't one of the reporting controllers carry on as usual
            // Note that this isn't the best way to do this but... the recommended way with a filter on the controllers didn't work
            if (!CONTROLLERS_TO_FILTER.Contains(controllerName))
            {
                base.OnActionExecuting(context);
                return;
            }

            // Now we want to check that a valid MemberName was provided in the parameters
            // That is we want to check if the member that was logged is actually requesting MemberName data that it is allowed to see...
            // Alternatively you could create a helper method and put this logic in every single controller but good luck maintaining that.
            if (context.ActionArguments.ContainsKey("parameters"))
            {
                var parameters = context.ActionArguments["parameters"] as RejectionReportParameters;

                // Now check if the user is requesting report information for a member that he actually has access to
                string auth0UserId = Util.UserUtil.GetAuth0UserName(context.HttpContext.User);
                var dbContext = context.HttpContext.RequestServices.GetService<AppDbContext>();
                var user = dbContext.Users.First(f => f.Auth0Id == auth0UserId);
                int userId = user.Id;
                var role = user.RoleId;
                if (role == Database.Enums.UserRoles.Member)
                {
                    var selectedMembers = parameters.MemberName;
                    var availableMembers = dbContext.MemberUsers
                        .Include(x => x.Member)
                        .Result.Where(u => u.UserId == userId)
                        .Result.Select(m => m.Member.RegisteredName)
                        .ToArray();

                    //var availableMembers = dbContext.Members.Result.Where(u => u.UserId == userId).Result.Select(m => m.RegisteredName).ToArray();
                    foreach (var selectedMember in selectedMembers)
                    {
                        if (!availableMembers.Contains(selectedMember))
                            throw new InvalidReportParametersException();
                    }
                }

                // TODO: Add checks for when member role is Bureau or ALG but we can only do this once we can log in as an ALG or a Bureau
            }

            base.OnActionExecuting(context);
        }
    }
}
