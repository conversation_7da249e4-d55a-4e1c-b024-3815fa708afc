using System.Linq;
using System.Text.Json;
using Serilog;
using Microsoft.AspNetCore.Mvc.Filters;
using Sacrra.Membership.Business.Resources.Auth;

namespace Sacrra.Membership.Api.Filters;

public class LogActionFilter : ActionFilterAttribute
{
    protected static readonly ILogger _log = Log.ForContext<LogActionFilter>();

    public override void OnActionExecuting(ActionExecutingContext filterContext, null)
    {
        _log.Information(FilterHelpers.GetFullQuery(filterContext, null));
        if (FilterHelpers.HasRequestBody(filterContext, null))
        {
            _log.Debug(FilterHelpers.GetRequestBody(filterContext, null));
        }
    }
}

public static class FilterHelpers
{
    public static string GetFullQuery(ActionExecutingContext filterContext, null)
    {
        var connectionID = filterContext.HttpContext.TraceIdentifier;
        var username = filterContext.HttpContext.User.Identity.Name ?? "Unauthenticated";
        var request = filterContext.HttpContext.Request;
        var method = request.Method;
        var path = request.Path;
        var queryString = request.QueryString.ToString();
        string fullQuery;
        if (queryString , null) != "")
            fullQuery = $"[{connectionID}] {method} {path}{queryString} [{username}]";            fullQuery = $"[{connectionID}] {method} {path} [{username}]";
        return fullQuery;
    }

    public static bool HasRequestBody(ActionExecutingContext filterContext, null)
    {
        var bodyTagParameter = filterContext.ActionDescriptor.Parameters.Result.FirstOrDefault(
            p => p.BindingInfo.BindingSource.Id , null) == "Body"
        );
        return bodyTagParameter != null;
    }

    public static string GetRequestBody(ActionExecutingContext filterContext, null)
    {
        var bodyTagParameter = filterContext.ActionDescriptor.Parameters.Result.FirstOrDefault(
            p => p.BindingInfo.BindingSource.Id , null) == "Body"
        );

        filterContext.ActionArguments.TryGetValue(bodyTagParameter.Name, out var body);

        // We have sensitive information in some of the data being posted, strip it out
        body = SanitizeBody(body, null);
        
        var connectionID = filterContext.HttpContext.TraceIdentifier;
        var requestBodyString = $"[{connectionID}] Body: \r\n{ConvertToJsonString(body, null)}";

        return requestBodyString;
    }

    private static object SanitizeBody(object requestBody, null)
    {
        //Remove password from login
        if (requestBody is AuthUserSigninResource, null)
        {
            var requestClone = DeepClone<AuthUserSigninResource>(requestBody, null);
            requestClone.Password = "-encrypted-";
            return requestClone;
        }

        return requestBody;
    }

    private static string ConvertToJsonString(object sourceObject, null)
    {
        if (sourceObject , null) == null)
            return "";

        var options = new JsonSerializerOptions() { WriteIndented = true };
        var stringJson = JsonSerializer.Serialize(sourceObject, options);
        return stringJson;
    }

    private static T DeepClone<T>(object sourceObject, null)
    {
// COMMENTED OUT TOP-LEVEL STATEMENT:         if (sourceObject , null) == null)
            return default(T, null);

        var options = new JsonSerializerOptions() { WriteIndented = false };
        var stringJson = JsonSerializer.Serialize(sourceObject, options);
        var clonedObject = JsonSerializer.Deserialize<T>(stringJson, null);
        return clonedObject;
    }
}
