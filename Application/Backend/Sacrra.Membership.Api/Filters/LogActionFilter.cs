using System.Linq;
using System.Text.Json;
using Serilog;
using Microsoft.AspNetCore.Mvc.Filters;
using Sacrra.Membership.Business.Resources.Auth;

namespace Sacrra.Membership.Api.Filters;

public class LogActionFilter : ActionFilterAttribute
{
    protected static readonly ILogger _log = Log.ForContext<LogActionFilter>();

    public override void OnActionExecuting(ActionExecutingContext filterContext)
    {
        _log.Information(FilterHelpers.GetFullQuery(filterContext));
        if (FilterHelpers.HasRequestBody(filterContext))
        {
            _log.Debug(FilterHelpers.GetRequestBody(filterContext));
        }
    }
}

public static class FilterHelpers
{
    public static string GetFullQuery(ActionExecutingContext filterContext)
    {
        var connectionID = filterContext.HttpContext.TraceIdentifier;
        var username = filterContext.HttpContext.User.Identity.Name ?? "Unauthenticated";
        var request = filterContext.HttpContext.Request;
        var method = request.Method;
        var path = request.Path;
        var queryString = request.QueryString.ToString();
        string fullQuery;
        if (queryString != "")
            fullQuery = $"[{connectionID}] {method} {path}{queryString} [{username}]";
        else
            fullQuery = $"[{connectionID}] {method} {path} [{username}]";
        return fullQuery;
    }

    public static bool HasRequestBody(ActionExecutingContext filterContext)
    {
        var bodyTagParameter = filterContext.ActionDescriptor.Parameters.Result.FirstOrDefault(
            p => p.BindingInfo.BindingSource.Id == "Body"
        );
        return bodyTagParameter != null;
    }

    public static string GetRequestBody(ActionExecutingContext filterContext)
    {
        var bodyTagParameter = filterContext.ActionDescriptor.Parameters.Result.FirstOrDefault(
            p => p.BindingInfo.BindingSource.Id == "Body"
        );

        filterContext.ActionArguments.TryGetValue(bodyTagParameter.Name, out var body);

        // We have sensitive information in some of the data being posted, strip it out
        body = SanitizeBody(body);
        
        var connectionID = filterContext.HttpContext.TraceIdentifier;
        var requestBodyString = $"[{connectionID}] Body: \r\n{ConvertToJsonString(body)}";

        return requestBodyString;
    }

    private static object SanitizeBody(object requestBody)
    {
        //Remove password from login
        if (requestBody is AuthUserSigninResource)
        {
            var requestClone = DeepClone<AuthUserSigninResource>(requestBody);
            requestClone.Password = "-encrypted-";
            return requestClone;
        }

        return requestBody;
    }

    private static string ConvertToJsonString(object sourceObject)
    {
        if (sourceObject == null)
            return "";

        var options = new JsonSerializerOptions() { WriteIndented = true };
        var stringJson = JsonSerializer.Serialize(sourceObject, options);
        return stringJson;
    }

    private static T DeepClone<T>(object sourceObject)
    {
        if (sourceObject == null)
            return default(T);

        var options = new JsonSerializerOptions() { WriteIndented = false };
        var stringJson = JsonSerializer.Serialize(sourceObject, options);
        var clonedObject = JsonSerializer.Deserialize<T>(stringJson);
        return clonedObject;
    }
}
