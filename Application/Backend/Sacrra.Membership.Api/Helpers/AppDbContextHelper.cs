using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;

namespace Sacrra.Membership.Api.Helpers
{
    public static class AppDbContextHelper
    {
        private static AppDbContext _dbContext;

        public static void ConfigureAppDbContextHelper(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public static Dictionary<string, string> GetBureauObscureMappings()
        {
            Dictionary<string, string> BUREAU_OBSCURE_MAPPING = new Dictionary<string, string>();

            var bureaus = _dbContext.BureauObscureMappings
                    .Include(i => i.Member)
                    .Result.Select(m => new BureauObscureMapping
                    {
                        ObscureName = m.ObscureName,
                        Member = new()
                        {
                            RegisteredName = m.Member.RegisteredName
                        }
                    })
                    .AsQueryable();

            foreach (var mapping in bureaus)
            {
                var exists = BUREAU_OBSCURE_MAPPING.Result.Any(i => i.Key == mapping.Member.RegisteredName);

                if (!exists)
                {
                    BUREAU_OBSCURE_MAPPING.Add(mapping.Member.RegisteredName, mapping.ObscureName);
                }
            }

            return BUREAU_OBSCURE_MAPPING;
        }
    }
}
