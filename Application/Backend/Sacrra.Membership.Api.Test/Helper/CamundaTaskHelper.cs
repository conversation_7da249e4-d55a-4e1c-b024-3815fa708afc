using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Camunda.Api.Client.UserTask;

namespace Sacrra.Membership.Api.Test.Helper;

public class CamundaTaskHelper
{
    public static List<UserTaskInfo> GetUserTaskByTaskDefinitionKey(CamundaClient camundaClient, string taskDefinitionKey)
    {
        var taskQuery = new TaskQuery() { Active = true, TaskDefinitionKey = taskDefinitionKey};
        var userTasks = camundaClient.UserTasks.Query(taskQuery).List();
        return userTasks;
    }

    public static T GetUserTaskVariable<T>(CamundaClient camundaClient, UserTaskInfo userTask, string variableName)
    {
        var variables = camundaClient.UserTasks[userTask.Id].Variables;
        var variableValue = variables.Get(variableName);
        if (variableValue == null)
            throw new Exception($"{variableName} variable not found");
        return (T)variableValue.Value;
    }
    
    public static UserTaskInfo GetUserTaskWithMatchingSrnId(CamundaClient camundaClient, long srnId,
        string taskDefinitionKey)
    {
        var userTasks = GetUserTaskByTaskDefinitionKey(camundaClient, taskDefinitionKey);
        foreach (var userTask in userTasks)
        {
            var taskSrnId = GetUserTaskVariable<long>(camundaClient, userTask,"SRNId");
            if (taskSrnId == srnId)
                return userTask;
        }
        throw new Exception($"No taskDefinition {taskDefinitionKey} with SRNId {srnId} found");
    }
    
    public static List<ExternalTaskResource> FetchAndLockExternalTasks(CamundaClient camundaClient, string topicName,
        string workerId)
    {
        var tasks = new List<ExternalTaskResource>(); 
        var fetchExternalTasks = new FetchExternalTasks()
        {
            MaxTasks = 10000,
            WorkerId = workerId,
            Topics = new List<FetchExternalTaskTopic>()
            {
                new(topicName, 10 * 1000)
            }
        };
        var lockedTasks = camundaClient.ExternalTasks.FetchAndLock(fetchExternalTasks);

        foreach (var lockedTask in lockedTasks)
        {
            var task = camundaClient.ExternalTasks[lockedTask.Id];
            tasks.Add(task);
        }
            
        return tasks;
    }
}