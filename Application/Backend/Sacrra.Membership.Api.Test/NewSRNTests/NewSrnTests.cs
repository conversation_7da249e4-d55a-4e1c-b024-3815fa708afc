using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Api.Test.Helper;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Camunda.CamundaAgents;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Serilog;

namespace Sacrra.Membership.Api.Test.NewSRNTests;

public class NewSrnTests : BaseIntegrationTest
{
    private SRNService _srnService;
    private NewSrnApplicationCamundaAgent _newSrnApplicationCamundaAgent;
    private CamundaClient _camundaClient;
    private CamundaRepository _camundaRepository;
    private readonly string _newDailySrn = "./NewSRNTests/data/NewDailySrn.json";
    private readonly string _newMonthlySrn = "./NewSRNTests/data/NewMonthlySrn.json";
    private readonly string _newDailyAndMonthlySrn = $"./NewSRNTests/data/NewDailyAndMonthlySrn.json";
    private SrnStatusUpdateToTestCamundaAgent _srnStatusUpdateToTestCamundaAgent;

    [SetUp]
    public void Setup()
    {
        BaseSetup();
        _srnService = GetService<SRNService>(_scope);
        _camundaRepository = GetService<CamundaRepository>(_scope);
        
        var configSettigs = _scope.ServiceProvider.GetService<IOptions<ConfigSettings>>();
        var httpClient = new HttpClient();
        httpClient.BaseAddress = new Uri(configSettigs!.Value.CamundaBaseAddress);
        _camundaClient = CamundaClient.Create(httpClient);
        _newSrnApplicationCamundaAgent = new NewSrnApplicationCamundaAgent(_camundaClient);
        _srnStatusUpdateToTestCamundaAgent = new SrnStatusUpdateToTestCamundaAgent(_camundaClient);
    }
    
    [Test]
    [Ignore("Comment out for Scratchpad")]
    public void Scratchpad()
    {
        //var srnId = 409587;
        // MakeItLiveOnDTH(srnId);
    }

    [Test, Description("Reject Daily SRN at first review")]
    public void TestRejectSrn_Daily()
    {
        TestRejectSrn(_newDailySrn);    
    }
    
    [Test, Description("Reject Monthly SRN at first review")]
    public void TestRejectSrn_Monthly()
    {
        TestRejectSrn(_newMonthlySrn);    
    }
    
    [Test, Description("Reject Daily & Monthly SRN at first review")]
    public void TestRejectSrn_DailyAndMonthly()
    {
        TestRejectSrn(_newDailyAndMonthlySrn);    
    }

    [Test, Description("Make Daily SRN live, no testing Member")]
    public void TestMakeSrnLiveNoTesting_Daily()
    {
        TestMakeSrnLiveNoTesting(_newDailySrn);  
    }
    
    [Test, Description("Make Monthly SRN live, no testing Member")]
    public void TestMakeSrnLiveNoTesting_Monthly()
    {
        TestMakeSrnLiveNoTesting(_newMonthlySrn);  
    }
    
    [Test, Description("Make Daily & Monthly SRN live, no testing Member")]
    public void TestMakeSrnLiveNoTesting_DailyAndMonthly()
    {
        TestMakeSrnLiveNoTesting(_newDailyAndMonthlySrn);  
    }
    
    [Test, Description("Make Daily SRN live, with testing required Member")]
    public void TestMakeSrnLiveWithTestingRequired_Daily()
    {
        TestMakeSrnLiveWithTestingRequired(_newDailySrn);  
    }
    
    [Test, Description("Make Monthly SRN live, with testing required Member")]
    public void TestMakeSrnLiveWithTestingRequired_Monthly()
    {
        TestMakeSrnLiveWithTestingRequired(_newMonthlySrn);  
    }
    
    [Test, Description("Make Daily And Monthly SRN live, with testing required Member")]
    public void TestMakeSrnLiveWithTestingRequired_DailyAndMonthly()
    {
        TestMakeSrnLiveWithTestingRequired(_newMonthlySrn);  
    }
    
    private void TestRejectSrn(string jsonFilePath)
    {
        var user = GetUserWithStakeholderManagerRole();
        var srnRequestInputDto = JsonHelper.DeserializeFile<SRNRequestInputDTO>(jsonFilePath);
        srnRequestInputDto.SRNDisplayName = "TestSRN-Reject" + Guid.NewGuid();
        
        var srnId = _srnService.CreateSrnEntries(new List<SRNRequestInputDTO>() { srnRequestInputDto}, user)[0];
        
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "allocate-stake-holder-manager");
        CompleteStakeHolderManagerSrnFirstReviewTask(srnId, isVerified: "no", rejectionReason: "Testing first review rejection");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "notify-applicant-of-srn-application-rejection");

        var dbContext = GetService<AppDbContext>(_scope);
        var dbSrn = dbContext.SRNs.FirstOrDefault(s => s.Id == srnId);
        Assert.That(dbSrn, Is.Not.Null);
        Assert.That(dbSrn.Id, Is.EqualTo(srnId));
        Assert.That(dbSrn.SRNNumber, Is.Null);
        Assert.That(dbSrn.SRNStatusId, Is.EqualTo(7));
        Assert.That(dbSrn.FirstReviewRejectReason, Is.EqualTo("Testing first review rejection"));
    }

    private void TestMakeSrnLiveNoTesting(string jsonFilePath)
    {
        var user = GetUserWithStakeholderManagerRole();
        var srnRequestInputDto = JsonHelper.DeserializeFile<SRNRequestInputDTO>(jsonFilePath);
        srnRequestInputDto.SRNDisplayName = "TestSRN-No-Testing" + Guid.NewGuid();
        
        var srnId = _srnService.CreateSrnEntries(new List<SRNRequestInputDTO>() {srnRequestInputDto}, user)[0];
        
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "allocate-stake-holder-manager");
        CompleteStakeHolderManagerSrnFirstReviewTask(srnId, isVerified: "yes");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "create-srn-number");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "email-bureaus-before-srn-testing");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "email-member");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "update-srn-status-to-live");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "email-bureaus-on-srn-go-live");
        MakeItLiveOnDTH(srnId);

        var dbContext = GetService<AppDbContext>(_scope);
        var dbSrn = dbContext.SRNs.FirstOrDefault(s => s.Id == srnId);
        Assert.That(dbSrn, Is.Not.Null);
        Assert.That(dbSrn.Id, Is.EqualTo(srnId));
        Assert.That(dbSrn.SRNNumber, Is.Not.Null);
        Assert.That(dbSrn.SRNStatusId, Is.EqualTo(4)); // Srn is live
    }
    
    private void TestMakeSrnLiveWithTestingRequired(string jsonFilePath)
    {
        var user = GetUserWithStakeholderManagerRole();
        var srnRequestInputDto = JsonHelper.DeserializeFile<SRNRequestInputDTO>(jsonFilePath);

        srnRequestInputDto.MemberId = 17; // This Member requires testing
        srnRequestInputDto.SRNDisplayName = "TestSRN-Testing-Required" + Guid.NewGuid();
        
        var srnId = _srnService.CreateSrnEntries(new List<SRNRequestInputDTO>() {srnRequestInputDto}, user)[0];
        
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "allocate-stake-holder-manager");
        CompleteStakeHolderManagerSrnFirstReviewTask(srnId, isVerified: "yes");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "create-srn-number");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "email-bureaus-before-srn-testing");
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "email-member");
        CompleteSacrraAdminSrnTestAddedToDth(srnId);
        ProcessExternalTask(_newSrnApplicationCamundaAgent, "call_file_test_subprocess");
        ProcessExternalTask(_srnStatusUpdateToTestCamundaAgent, "update-srn-and-file-statuses");
        WaitForTimerInMilliseconds(100);
        CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(srnId, ConfirmSRNTestingCompleteEnum.Yes);
        ProcessExternalTask(_srnStatusUpdateToTestCamundaAgent, "email-bureaus-after-srn-testing-srn-status-update");
        WaitForTimerInMilliseconds(100);
        CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(srnId);
        ProcessExternalTask(_srnStatusUpdateToTestCamundaAgent, "email-bureaus-on-srn-go-live");
        MakeItLiveOnDTH(srnId);
       
        var dbContext = GetService<AppDbContext>(_scope);
        var dbSrn = dbContext.SRNs.FirstOrDefault(s => s.Id == srnId);
        Assert.That(dbSrn, Is.Not.Null);
        Assert.That(dbSrn.Id, Is.EqualTo(srnId));
        Assert.That(dbSrn.SRNStatusId, Is.EqualTo(4)); // Srn is live
        
    }

    private void CompleteSacrraAdminSrnTestAddedToDth(int srnId)
    {
        var userTask = CamundaTaskHelper.GetUserTaskWithMatchingSrnId(_camundaClient, srnId, "Task_AddTestSRNToDTH");
        var taskId = userTask.Id;
        _camundaRepository.CompleteSACRRAAdminSRNTestAddedToDTH(taskId);
    }

    private void CompleteStakeHolderManagerSrnFirstReviewTask(int srnId, string isVerified, string? rejectionReason = null)
    {
        var userTask = CamundaTaskHelper.GetUserTaskWithMatchingSrnId(_camundaClient, srnId, "Task_SHMReview");
        var taskCompleteSrnReviewResource = new TaskCompleteSRNReviewResource()
        {
            IsVerified = isVerified,
            RejectReason = rejectionReason
            
        };
        _camundaRepository.CompleteStakeHolderManagerSRNFirstReviewTask(userTask.Id, taskCompleteSrnReviewResource);
    }
    
    private void MakeItLiveOnDTH(int srnId)
    {
        var userTask = CamundaTaskHelper.GetUserTaskWithMatchingSrnId(_camundaClient, srnId, "Task_CompleteAndUpdateDTH");
        _camundaRepository.CompleteSACRRAAdminSRNTakeOnUpdateDTHTask(userTask.Id);
    }
    
    private void CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(int srnId,
        ConfirmSRNTestingCompleteEnum isConfirmed)
    {
        var model = new TaskConfirmSRNTestingCompleteResource()
        {
            IsTestingComplete = isConfirmed
        };
        var userTask = CamundaTaskHelper.GetUserTaskWithMatchingSrnId(_camundaClient, srnId, "UserTask_ConfirmMigrationTesting");
        var taskId = userTask.Id;
        _camundaRepository.CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(taskId, model);
    }
    
    private void CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(int srnId)
    {
        var userTask = CamundaTaskHelper.GetUserTaskWithMatchingSrnId(_camundaClient, srnId, "Task_IsSRNLive");
        var taskId = userTask.Id;
        var model = new TaskConfirmSRNGoLiveResource()
        {
            IsLiveComplete = ConfirmSRNGoLiveEnum.Yes,
            GoLiveDate = DateTime.Now,
            DevelopmentStartDate = DateTime.Now,
            DevelopmentEndDate = DateTime.Now,
            TestEndDate = DateTime.Now,
            TestStartDate = DateTime.Now,
        };
        // The task is still moving, so I'm going to ignore this exception
        // TODO: Figure out why we are getting an exception, this might be a big issue
        try
        {
            _camundaRepository.CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(taskId, model);
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "Unhandled exception");
        }
    }
    
    // Run system task to all tasks for provided topic
    private void ProcessExternalTask(ICamundaAgent camundaAgent, string topicName)
    {
        var workerId = "NUnit"+Guid.NewGuid();
        var externalTaskList = CamundaTaskHelper.FetchAndLockExternalTasks(_camundaClient, topicName, workerId);
        foreach (var externalTask in externalTaskList)
        {
            var completeExternalTask = new CompleteExternalTask() { WorkerId = workerId };
            camundaAgent.Process(topicName, externalTask, completeExternalTask, _scope);
        }
    }
    
    private void WaitForTimerInMilliseconds(int i)
    {
        Thread.Sleep(i);
    }
}