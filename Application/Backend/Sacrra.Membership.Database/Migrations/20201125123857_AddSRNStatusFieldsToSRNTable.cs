using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddSRNStatusFieldsToSRNTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BureauInstruction",
                table: "SRNs",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Comment",
                table: "SRNs",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "FileType",
                table: "SRNs",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "StatusType",
                table: "SRNs",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BureauInstruction",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "Comment",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "FileType",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "StatusType",
                table: "SRNs");
        }
    }
}
