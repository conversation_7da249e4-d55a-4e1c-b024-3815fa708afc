using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class RemovedSRNChangeRequestModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SRNChangeRequests");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SRNChangeRequests",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    DateCreated = table.Column<DateTime>(nullable: false),
                    OldDetailsBlob = table.Column<string>(nullable: true),
                    ReviewComments = table.Column<string>(nullable: true),
                    SRNId = table.Column<int>(nullable: false),
                    Status = table.Column<int>(nullable: false),
                    UpdatedDetailsBlob = table.Column<string>(nullable: true),
                    UserId = table.Column<int>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SRNChangeRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SRNChangeRequests_SRNs_SRNId",
                        column: x => x.SRNId,
                        principalTable: "SRNs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SRNChangeRequests_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNChangeRequests_SRNId",
                table: "SRNChangeRequests",
                column: "SRNId");

            migrationBuilder.CreateIndex(
                name: "IX_SRNChangeRequests_UserId",
                table: "SRNChangeRequests",
                column: "UserId");
        }
    }
}
