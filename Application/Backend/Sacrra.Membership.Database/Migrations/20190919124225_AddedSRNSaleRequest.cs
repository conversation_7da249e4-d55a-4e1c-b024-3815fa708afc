using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNSaleRequest : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SRNSaleRequests",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    SRNId = table.Column<int>(nullable: false),
                    SellerMemberId = table.Column<int>(nullable: false),
                    BuyerMemberId = table.Column<int>(nullable: false),
                    RequestDate = table.Column<DateTime>(nullable: false),
                    Status = table.Column<int>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SRNSaleRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SRNSaleRequests_Members_BuyerMemberId",
                        column: x => x.BuyerMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_SRNSaleRequests_SRNs_SRNId",
                        column: x => x.SRNId,
                        principalTable: "SRNs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_SRNSaleRequests_Members_SellerMemberId",
                        column: x => x.SellerMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNSaleRequests_BuyerMemberId",
                table: "SRNSaleRequests",
                column: "BuyerMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_SRNSaleRequests_SRNId",
                table: "SRNSaleRequests",
                column: "SRNId");

            migrationBuilder.CreateIndex(
                name: "IX_SRNSaleRequests_SellerMemberId",
                table: "SRNSaleRequests",
                column: "SellerMemberId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SRNSaleRequests");
        }
    }
}
