using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class ChangedDeleteBehaviourForMemberContactsToCascade : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MemberContacts_Members_MemberId",
                table: "MemberContacts");

            migrationBuilder.AddForeignKey(
                name: "FK_MemberContacts_Members_MemberId",
                table: "MemberContacts",
                column: "MemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MemberContacts_Members_MemberId",
                table: "MemberContacts");

            migrationBuilder.AddForeignKey(
                name: "FK_MemberContacts_Members_MemberId",
                table: "MemberContacts",
                column: "MemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
