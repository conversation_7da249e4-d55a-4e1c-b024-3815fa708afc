using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddFieldsToMemberTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Comment",
                table: "SRNs",
                newName: "StatusComment");

            migrationBuilder.AlterColumn<int>(
                name: "StatusType",
                table: "SRNs",
                nullable: true,
                oldClrType: typeof(int));

            migrationBuilder.AlterColumn<int>(
                name: "FileType",
                table: "SRNs",
                nullable: true,
                oldClrType: typeof(int));

            migrationBuilder.AddColumn<string>(
                name: "StatusComment",
                table: "PartialMembers",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StatusComment",
                table: "Members",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StatusComment",
                table: "PartialMembers");

            migrationBuilder.DropColumn(
                name: "StatusComment",
                table: "Members");

            migrationBuilder.RenameColumn(
                name: "StatusComment",
                table: "SRNs",
                newName: "Comment");

            migrationBuilder.AlterColumn<int>(
                name: "StatusType",
                table: "SRNs",
                nullable: false,
                oldClrType: typeof(int),
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "FileType",
                table: "SRNs",
                nullable: false,
                oldClrType: typeof(int),
                oldNullable: true);
        }
    }
}
