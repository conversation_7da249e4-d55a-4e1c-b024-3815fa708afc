using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class RenamedSRNStatusReasonTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_StatusReason_StatusReasonId",
                table: "SRNs");

            migrationBuilder.DropTable(
                name: "StatusReason");

            migrationBuilder.CreateTable(
                name: "SRNStatusReasons",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SRNStatusReasons", x => x.Id);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SRNStatusReasons_StatusReasonId",
                table: "SRNs",
                column: "StatusReasonId",
                principalTable: "SRNStatusReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SRNStatusReasons_StatusReasonId",
                table: "SRNs");

            migrationBuilder.DropTable(
                name: "SRNStatusReasons");

            migrationBuilder.CreateTable(
                name: "StatusReason",
                columns: table => new
                {
                    StatusReasonId = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StatusReason", x => x.StatusReasonId);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_StatusReason_StatusReasonId",
                table: "SRNs",
                column: "StatusReasonId",
                principalTable: "StatusReason",
                principalColumn: "StatusReasonId",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
