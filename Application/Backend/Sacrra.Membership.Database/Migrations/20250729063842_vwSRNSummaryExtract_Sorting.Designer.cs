// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Sacrra.Membership.Database;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250729063842_vwSRNSummaryExtract_Sorting")]
    partial class vwSRNSummaryExtract_Sorting
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AccountType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("AccountTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSchedule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("AdhocFileBureauStatusId")
                        .HasColumnType("int");

                    b.Property<string>("AdhocFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("AdhocFileSubmissionId")
                        .HasColumnType("int");

                    b.Property<int?>("BureauId")
                        .HasColumnType("int");

                    b.Property<int?>("UnnsuccessfulLoadReasonId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AdhocFileSubmissionId");

                    b.HasIndex("BureauId");

                    b.HasIndex("UnnsuccessfulLoadReasonId");

                    b.ToTable("AdhocFileSchedule");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSubmission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime?>("ActualSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("AdhocFileSubmissionDeclineReasonId")
                        .HasColumnType("bigint");

                    b.Property<int>("AdhocFileSubmissionReasonId")
                        .HasColumnType("int");

                    b.Property<int>("AdhocFileSubmissionStatusId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FileSubmissionTypeId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<long>("NumberOfRecords")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("PlannedSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReasonForDeletion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SACRRAAccountType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SACRRAIndustry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SPId")
                        .HasColumnType("int");

                    b.Property<int?>("SRNId")
                        .HasColumnType("int");

                    b.Property<DateTime>("SubmissionStatusDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AdhocFileSubmissionReasonId");

                    b.HasIndex("MemberId");

                    b.HasIndex("SRNId");

                    b.ToTable("AdhocFileSubmissions");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSubmissionCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("AdhocFileSubmissionCategory");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSubmissionReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("FileSubmissionCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("FileSubmissionReasonConstId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("FileSubmissionCategoryId");

                    b.ToTable("AdhocFileSubmissionReason");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALG", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ALGs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGClientLeader", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int>("LeaderId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("LeaderId");

                    b.ToTable("ALGClientLeaders");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGMemberDetails", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("LoanManagementSystemName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfClients")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.ToTable("ALGMemberDetails");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ApiError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ApiErrors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BranchLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SRNId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SRNId");

                    b.ToTable("BranchLocations");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Bureau", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Bureaus");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BureauLoadStats", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("AdHocFileSubmissionId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateNewQE1ExtractSharedPostCleanup")
                        .HasColumnType("datetime2");

                    b.Property<int?>("MemberId")
                        .HasColumnType("int");

                    b.Property<int?>("NumberOfDuplicatesRemovedFromDBBasedOnExtract")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRecordsMatched")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRecordsMatchedAndUpdated")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRecordsMatchedButNotUpdated")
                        .HasColumnType("int");

                    b.Property<int?>("NumberOfRecordsMatchedSuccessfullyConverted")
                        .HasColumnType("int");

                    b.Property<int?>("NumberOfRecordsMergedAcrossSRNs")
                        .HasColumnType("int");

                    b.Property<int?>("NumberOfRecordsMergedWithinSRN")
                        .HasColumnType("int");

                    b.Property<int?>("NumberOfRecordsMigrated")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRecordsReceived")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRecordsUnmatched")
                        .HasColumnType("int");

                    b.Property<int?>("TotalNumberOfQE1RecordRemainingOnDBPostCleanup")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AdHocFileSubmissionId");

                    b.HasIndex("MemberId");

                    b.ToTable("BureauLoadStats");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BureauObscureMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("ObscureName")
                        .IsRequired.HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.HasIndex("ObscureName")
                        .IsUnique();

                    b.ToTable("BureauObscureMappings");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.CamundaError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CamundaErrors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.CamundaErrorRecipient", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Email")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CamundaErrorRecipients");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ChangeRequestStaging", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int>("ObjectId")
                        .HasColumnType("int");

                    b.Property<string>("OldDetailsBlob")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReviewComments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StagingDetailsBlob")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("UpdatedDetailsBlob")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("MemberChangeRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ContactType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ApplicableTo")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ContactTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.CreditInformationClassification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CreditInformationClassifications");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DisqualifiedReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DisqualifiedReasonTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DisqualifiedReasonMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("FreeTextReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ReasonId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("DisqualifiedReasonMapping");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Document", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("BlobName")
                        .IsRequired.HasColumnType("VARCHAR(200)");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("VARCHAR(1000)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("VARCHAR(400)");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlobName")
                        .IsUnique();

                    b.HasIndex("CategoryId");

                    b.HasIndex("StatusId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DocumentCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("VARCHAR(200)");

                    b.HasKey("Id");

                    b.ToTable("DocumentCategories");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DocumentStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("VARCHAR(200)");

                    b.HasKey("Id");

                    b.ToTable("DocumentStatuses");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DocumentUserAccess", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("AccessedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("DocumentId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("UserId");

                    b.ToTable("DocumentUserAccess");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DWException", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("BureauName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CamundaTaskId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DatePulled")
                        .HasColumnType("datetime2");

                    b.Property<string>("Exception")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExceptionCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ExceptionDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExceptionDesc")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExceptionInfo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExceptionStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FctWarehouseExceptionID")
                        .HasColumnType("bigint");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IsSentToPortal")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SHM")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("DWExceptions");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EmailAuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("BccAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BccAddressList")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CcAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CcAddressList")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateSent")
                        .HasColumnType("datetime2");

                    b.Property<string>("MailBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Placeholders")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecipientAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecipientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecipientType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SrnId")
                        .HasColumnType("int");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Workflow")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("EmailAuditLogs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EmailQueue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateSent")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastActioned")
                        .HasColumnType("datetime2");

                    b.Property<int>("Reason")
                        .HasColumnType("int");

                    b.Property<int>("RecipientType")
                        .HasColumnType("int");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int?>("Workflow")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("EmailQueues");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EntityType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("EntityTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EventLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ChangeBlob")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("ChangeType")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("EntityBlob")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<int?>("EntityId")
                        .HasColumnType("int");

                    b.Property<string>("EntityName")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<int>("EntityTypeId")
                        .HasColumnType("int");

                    b.Property<string>("User")
                        .HasColumnType("VARCHAR(MAX)");

                    b.HasKey("Id");

                    b.HasIndex("EntityTypeId");

                    b.ToTable("EventLogs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.FreshdeskExcludedDomain", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("FreshdeskExcludedDomains");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.HangfireJob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Day")
                        .HasColumnType("int");

                    b.Property<int>("DayOfWeek")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Frequency")
                        .HasColumnType("int");

                    b.Property<int?>("Hour")
                        .HasColumnType("int");

                    b.Property<int?>("Interval")
                        .HasColumnType("int");

                    b.Property<int?>("Minute")
                        .HasColumnType("int");

                    b.Property<int?>("Month")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.ToTable("HangfireJobs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.HangfireLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JobName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("HangfireJobLogs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.HangfireScheduledJob", b =>
                {
                    b.Property<string>("JobName")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CronString")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MethodClass")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MethodName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Parameters")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("JobName");

                    b.ToTable("HangfireScheduledJobs");

                    b.HasData(
                        new
                        {
                            JobName = "Cleanup-API-Errors",
                            CronString = "0 21 * * * *",
                            MethodClass = "Sacrra.Membership.Business.Services.HangfireService",
                            MethodName = "CleanupApiErrors"
                        },
                        new
                        {
                            JobName = "Cleanup-Camunda-Errors",
                            CronString = "0 21 * * * *",
                            MethodClass = "Sacrra.Membership.Business.Services.HangfireService",
                            MethodName = "CleanupCamundaErrors"
                        },
                        new
                        {
                            JobName = "Create-DW-Camunda-Exception-Tasks",
                            CronString = "15 * * * * *",
                            MethodClass = "Sacrra.Membership.Business.Services.HangfireService",
                            MethodName = "CreateCamundaTaskForNewDWExceptions"
                        },
                        new
                        {
                            JobName = "Sync-Freshdesk-Companies",
                            CronString = "0 18 * * * *",
                            MethodClass = "Sacrra.Membership.Freshdesk.Services.CompanyService",
                            MethodName = "CreateCompanies"
                        },
                        new
                        {
                            JobName = "Sync-Freshdesk-Contacts",
                            CronString = "0 19 * * * *",
                            MethodClass = "Sacrra.Membership.Freshdesk.Services.CompanyService",
                            MethodName = "CreateContacts"
                        });
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.InvoiceRate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateFrom")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateTo")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("VARCHAR(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("InvoiceRates");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.LoanManagementSystemVendor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("LoanManagementSystemVendors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ContentType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailgunMessageId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Size")
                        .HasColumnType("int");

                    b.Property<string>("URL")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MailgunMessageId");

                    b.ToTable("MailAttachments", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailColumn", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("FriendlyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailSettingId")
                        .HasColumnType("int");

                    b.Property<string>("PropertyName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MailSettingId");

                    b.ToTable("MailColumns", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailDeliveryStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("AttemptNo")
                        .HasColumnType("int");

                    b.Property<int>("Code")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailItemId")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("SessionSeconds")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("MailItemId")
                        .IsUnique();

                    b.ToTable("MailDeliveryStatuses", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailEnvelop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("MailItemId")
                        .HasColumnType("int");

                    b.Property<string>("Sender")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SendingIp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Targets")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Transport")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MailItemId")
                        .IsUnique();

                    b.ToTable("MailEnvelops", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("MailEvents", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailFlag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<bool>("IsAuthenticated")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRouted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSystemTest")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTestMode")
                        .HasColumnType("bit");

                    b.Property<int>("MailItemId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MailItemId")
                        .IsUnique();

                    b.ToTable("MailFlags", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailgunMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("MailItemId")
                        .HasColumnType("int");

                    b.Property<int>("Size")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MailItemId")
                        .IsUnique();

                    b.ToTable("MailgunMessages", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailHeader", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("From")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailgunMessageId")
                        .HasColumnType("int");

                    b.Property<string>("MessageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("To")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MailgunMessageId")
                        .IsUnique();

                    b.ToTable("MailHeaders", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Event")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LogLevel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailEventId")
                        .HasColumnType("int");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Recipient")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecipientDomain")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Severity")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Timestamp")
                        .HasColumnType("float");

                    b.Property<string>("UniqueId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MailEventId");

                    b.ToTable("MailItems", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailPaging", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("First")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Last")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailEventId")
                        .HasColumnType("int");

                    b.Property<string>("Next")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Previous")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MailEventId")
                        .IsUnique();

                    b.ToTable("MailPagings", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailRecipient", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Email")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<int>("MailSettingId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MailSettingId");

                    b.ToTable("MailRecipients", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("FilterEmailsFromSender")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEmailEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MailSettings", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailStorage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Key")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailItemId")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MailItemId")
                        .IsUnique();

                    b.ToTable("MailStorages", "Mailgun");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Member", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AnalyticsCompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("AnnualTurnover")
                        .HasColumnType("bigint");

                    b.Property<int>("ApplicationStatusId")
                        .HasColumnType("int");

                    b.Property<int?>("ChangeRequestId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateActivated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateCancelled")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisqualificationReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("DisqualifiedReasonMappingId")
                        .HasColumnType("int");

                    b.Property<string>("HeadOfficePhysicalAddress")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("HeadOfficePostalAddress")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("IdNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("IndustryClassificationId")
                        .HasColumnType("int");

                    b.Property<bool>("IsNcrRegistrant")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSoleProp")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVatRegistrant")
                        .HasColumnType("bit");

                    b.Property<int?>("MemberStatusReasonId")
                        .HasColumnType("int");

                    b.Property<int>("MembershipTypeId")
                        .HasColumnType("int");

                    b.Property<string>("NcrCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("NcrReportingPrimaryBusinessClassificationId")
                        .HasColumnType("int");

                    b.Property<string>("NcrcpNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PrimaryBureauId")
                        .HasColumnType("int");

                    b.Property<int?>("PrincipleDebtRangeId")
                        .HasColumnType("int");

                    b.Property<string>("RegisteredName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<string>("RegisteredNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("RequireSRNTesting")
                        .HasColumnType("bit");

                    b.Property<int?>("SecondaryBureauId")
                        .HasColumnType("int");

                    b.Property<int?>("StakeholderManagerId")
                        .HasColumnType("int");

                    b.Property<string>("StatusComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VatNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ChangeRequestId");

                    b.HasIndex("IdNumber")
                        .IsUnique.HasFilter("[IdNumber] IS NOT NULL");

                    b.HasIndex("MemberStatusReasonId");

                    b.HasIndex("PrimaryBureauId");

                    b.HasIndex("RegisteredNumber")
                        .IsUnique.HasFilter("[RegisteredNumber] IS NOT NULL");

                    b.HasIndex("SecondaryBureauId");

                    b.HasIndex("StakeholderManagerId");

                    b.HasIndex("VatNumber")
                        .IsUnique.HasFilter("[VatNumber] IS NOT NULL");

                    b.ToTable("Members");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ContactTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("OfficeTelNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Surname")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ContactTypeId");

                    b.HasIndex("MemberId");

                    b.ToTable("MemberContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberDocument", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AuditedFinancialBlob")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("IDDocumentBlob")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("NcrCertificateBlob")
                        .HasColumnType("VARCHAR(MAX)");

                    b.HasKey("Id");

                    b.HasIndex("MemberId")
                        .IsUnique();

                    b.ToTable("MemberDocuments");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberDomain", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired.HasColumnType("VARCHAR(500)");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("MemberDomains");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberInvoice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<DateTime>("BillingDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ClientsCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("InvoiceRateId")
                        .HasColumnType("int");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceRateId");

                    b.HasIndex("MemberId");

                    b.ToTable("MemberInvoices");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberStatusReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MemberStatusReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberUsers", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.HasIndex("UserId");

                    b.ToTable("MemberUsers");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MonthlyOSLAReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MonthlyOSLAReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.NCRReportingAccountTypeClassification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("NCRReportingAccountTypeClassifications");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.PartialMember", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AnalyticsCompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("AnnualTurnover")
                        .HasColumnType("int");

                    b.Property<int>("ApplicationStatusId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisqualificationReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HeadOfficePhysicalAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HeadOfficePostalAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IDDocumentBlob")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IdNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("IndustryClassificationId")
                        .HasColumnType("int");

                    b.Property<bool>("IsComplete")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNcrRegistrant")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSoleProp")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVatRegistrant")
                        .HasColumnType("bit");

                    b.Property<int>("MembershipTypeId")
                        .HasColumnType("int");

                    b.Property<string>("NcrCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NcrCertificateBlob")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NcrReportingPrimaryBusinessClassificationId")
                        .HasColumnType("int");

                    b.Property<int>("NcrcpNumber")
                        .HasColumnType("int");

                    b.Property<int>("PrimaryBureauId")
                        .HasColumnType("int");

                    b.Property<int>("PrincipleDebtRangeId")
                        .HasColumnType("int");

                    b.Property<string>("RegisteredName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegisteredNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("RequireSRNTesting")
                        .HasColumnType("bit");

                    b.Property<int>("SecondaryBureauId")
                        .HasColumnType("int");

                    b.Property<string>("StatusComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("VatNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("PartialMembers");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSchedule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("BureauId")
                        .HasColumnType("int");

                    b.Property<int?>("ReplacementFileBureauStatusId")
                        .HasColumnType("int");

                    b.Property<string>("ReplacementFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ReplacementFileSubmissionId")
                        .HasColumnType("int");

                    b.Property<int?>("UnnsuccessfulLoadReasonId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BureauId");

                    b.HasIndex("ReplacementFileSubmissionId");

                    b.HasIndex("UnnsuccessfulLoadReasonId");

                    b.ToTable("ReplacementFileSchedule");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSubmission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime?>("ActualSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DWDateCreated")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FileSubmissionTypeId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<long>("NumberOfFiles")
                        .HasColumnType("bigint");

                    b.Property<long>("NumberOfRecords")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("PlannedSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReasonForCancellation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReasonForDeletion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReplacementFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ReplacementFileSubmissionDeclineReasonId")
                        .HasColumnType("bigint");

                    b.Property<int>("ReplacementFileSubmissionReasonId")
                        .HasColumnType("int");

                    b.Property<int>("ReplacementFileSubmissionStatusId")
                        .HasColumnType("int");

                    b.Property<string>("SACRRAAccountType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SACRRAIndustry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SPId")
                        .HasColumnType("int");

                    b.Property<int?>("SRNId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SubmissionLastCheckedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("SubmissionStatusDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.HasIndex("ReplacementFileSubmissionReasonId");

                    b.HasIndex("SPId");

                    b.HasIndex("SRNId");

                    b.ToTable("ReplacementFileSubmissions");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSubmissionCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ReplacementFileSubmissionCategories");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSubmissionReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("FileSubmissionCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("FileSubmissionCategoryId");

                    b.ToTable("ReplacementFileSubmissionReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.RolloutStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RolloutStatuses");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SoftwareVendor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SoftwareVendors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SPGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("SPNumber")
                        .IsRequired.HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("MemberId", "SPNumber")
                        .IsUnique();

                    b.ToTable("SPGroups");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRN", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("AccountStatusDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("AccountTypeId")
                        .HasColumnType("int");

                    b.Property<int>("BillingCycleDay")
                        .HasColumnType("int");

                    b.Property<string>("BureauInstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ChangeRequestId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreationDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreditInformationClassificationId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateRequested")
                        .HasColumnType("datetime2");

                    b.Property<int?>("FileType")
                        .HasColumnType("int");

                    b.Property<string>("FirstReviewRejectReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LoanManagementSystemVendorId")
                        .HasColumnType("int");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<int?>("NCRReportingAccountTypeClassificationId")
                        .HasColumnType("int");

                    b.Property<long>("NumberOfActiveAccounts")
                        .HasColumnType("bigint");

                    b.Property<int?>("SPGroupId")
                        .HasColumnType("int");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("SRNStatusId")
                        .HasColumnType("int");

                    b.Property<int?>("SRNStatusReasonId")
                        .HasColumnType("int");

                    b.Property<string>("SecondReviewRejectReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("SignoffDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("SoftwareVendorId")
                        .HasColumnType("int");

                    b.Property<DateTime>("StatusLastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("TradingName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ALGLeaderId");

                    b.HasIndex("AccountTypeId");

                    b.HasIndex("ChangeRequestId");

                    b.HasIndex("CreditInformationClassificationId");

                    b.HasIndex("LoanManagementSystemVendorId");

                    b.HasIndex("MemberId");

                    b.HasIndex("NCRReportingAccountTypeClassificationId");

                    b.HasIndex("SPGroupId");

                    b.HasIndex("SRNNumber")
                        .IsUnique.HasFilter("[SRNNumber] IS NOT NULL");

                    b.HasIndex("SRNStatusId");

                    b.HasIndex("SRNStatusReasonId");

                    b.HasIndex("SoftwareVendorId");

                    b.ToTable("SRNs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ContactTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OfficeTelNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SRNId")
                        .HasColumnType("int");

                    b.Property<string>("Surname")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ContactTypeId");

                    b.HasIndex("SRNId");

                    b.ToTable("SRNContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNFieldUpdateSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FieldUpdateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsApprovalRequired")
                        .HasColumnType("bit");

                    b.Property<bool>("IsUpdatable")
                        .HasColumnType("bit");

                    b.Property<string>("ObjectName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ToBeApprovedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("SRNFieldUpdateSettings");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNMergeRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FromSRNId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("ToSRNId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FromSRNId");

                    b.HasIndex("ToSRNId");

                    b.ToTable("SRNMergeRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNMonthlyOSLAReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("FctDTHExceptionsID")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("FileDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("MonthlyOSLAReasonId")
                        .HasColumnType("int");

                    b.Property<int>("SRNId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MonthlyOSLAReasonId");

                    b.HasIndex("SRNId");

                    b.ToTable("SRNMonthlyOSLAReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSaleRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("BuyerMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("BuyerSRNId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MigrationDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReviewComments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SPGroupId")
                        .HasColumnType("int");

                    b.Property<int>("SRNId")
                        .HasColumnType("int");

                    b.Property<int>("SellerMemberId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BuyerMemberId");

                    b.HasIndex("BuyerSRNId");

                    b.HasIndex("SPGroupId");

                    b.HasIndex("SRNId");

                    b.HasIndex("SellerMemberId");

                    b.ToTable("SRNSaleRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Exclusions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan>("HangfireExcecutionTime")
                        .HasColumnType("time");

                    b.Property<int>("Increment")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastGeneratedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("LastGeneratedNumber")
                        .HasColumnType("bigint");

                    b.Property<long>("MaxGeneratedNumberAllowed")
                        .HasColumnType("bigint");

                    b.Property<int>("NoOfDaysBeforeCreatingLiveConfirmationTask")
                        .HasColumnType("int");

                    b.Property<int>("NoOfDaysBeforeCreatingTestingConfirmationTask")
                        .HasColumnType("int");

                    b.Property<int>("NoOfDigitsAllowed")
                        .HasColumnType("int");

                    b.Property<string>("Prefix")
                        .IsRequired.HasColumnType("VARCHAR(2)");

                    b.HasKey("Id");

                    b.ToTable("SRNSettings");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSplitRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FromSRNId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("ToSRNId")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FromSRNId");

                    b.HasIndex("ToSRNId");

                    b.ToTable("SRNSplitRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActivityAllowedWhileInProcess")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SRNStatuses");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SrnStatusHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime?>("BureauClosureDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("BureauClosureInstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("SrnId")
                        .HasColumnType("bigint");

                    b.Property<string>("StatusComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StatusDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<int?>("StatusReasonId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("SrnStatusHistory");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SRNStatusReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusReasonSRNStatusLink", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("SRNStatusId")
                        .HasColumnType("int");

                    b.Property<int>("SRNStatusReasonId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SRNStatusId");

                    b.HasIndex("SRNStatusReasonId");

                    b.ToTable("SRNStatusReasonSRNStatusLink");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusUpdateHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("BureauInstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateCompleted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int?>("FileType")
                        .HasColumnType("int");

                    b.Property<bool>("IsComple")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDailyFileLive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLiveFileSubmissionsSuspended")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMonthlyFileLive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ProcessInstanceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RolloutStatusId")
                        .HasColumnType("int");

                    b.Property<string>("SRNFileTestingStatusReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SRNId")
                        .HasColumnType("int");

                    b.Property<int>("SRNStatusId")
                        .HasColumnType("int");

                    b.Property<int?>("SRNStatusReasonId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SignoffDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdateNumber")
                        .HasColumnType("VARCHAR(200)");

                    b.Property<int?>("UpdateType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RolloutStatusId");

                    b.HasIndex("SRNId");

                    b.HasIndex("SRNStatusId");

                    b.HasIndex("SRNStatusReasonId");

                    b.ToTable("SRNStatusUpdateHistory");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.TermsConditionsDocument", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("BlobName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("TermsConditionsDocuments");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.TradingName", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.ToTable("TradingNames");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Auth0Id")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired.HasColumnType("nvarchar(450)");

                    b.Property<byte[]>("EmailConfirmationHash")
                        .HasColumnType("varbinary(max)");

                    b.Property<byte[]>("EmailConfirmationSalt")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("FirstName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired.HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastReadTsAndCsAt")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("PasswordHash")
                        .HasColumnType("varbinary(max)");

                    b.Property<byte[]>("PasswordSalt")
                        .HasColumnType("varbinary(max)");

                    b.Property<bool>("RequirePasswordChange")
                        .HasColumnType("bit");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwCalculateMonthlyAlgInvoicing", b =>
                {
                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<double>("ApplicableRate")
                        .HasColumnType("float");

                    b.Property<string>("BillingDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClientsActivated")
                        .HasColumnType("int");

                    b.Property<string>("RegisteredName")
                        .HasColumnType("nvarchar(max)");

                    b.ToView("vwCalculateMonthlyAlgInvoicing");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwSRNComplianceContacts", b =>
                {
                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<string>("ComplianceContactCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactOfficeNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactSurname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("vwSRNComplianceContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwSRNDataContacts", b =>
                {
                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<string>("DataContactCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactOfficeNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactSurname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("vwSRNDataContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwSRNDTHContacts", b =>
                {
                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<string>("DTHContactCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DTHContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DTHContactFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DTHContactJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DTHContactOfficeNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DTHContactSurname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("vwSRNDTHContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwSRNFileExtract", b =>
                {
                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ActualGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrentSRNStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("FileStatusDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("FileTestSignoffDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IsLatestRecord")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PlannedGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("PlannedTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SRNCreationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SRNDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRNFileType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TestFileStatusReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TestingSkipped")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("vwSRNFileExtract");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwSRNManualAmendmentsContacts", b =>
                {
                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<string>("ManualAmendmentsContactCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManualAmendmentsContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManualAmendmentsContactFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManualAmendmentsContactJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManualAmendmentsContactOfficeNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManualAmendmentsContactSurname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("vwSRNManualAmendmentsContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwSRNSummaryExtract", b =>
                {
                    b.Property<string>("AccountType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("BillingCycleDay")
                        .HasColumnType("int");

                    b.Property<string>("BranchLocations")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("BureauClosureDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("BureauClosureInstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreditInformationClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IsLatestHistory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LoanManagementSystemVendor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("MemberName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NCRReportingAccountTypeClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NumberMonthlyRecords")
                        .HasColumnType("int");

                    b.Property<string>("PortfolioManager")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SPNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRNALGLeader")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SRNALGLeaderId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SRNCreationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SRNDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StatusComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StatusDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("StatusReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ThirdPartyVendor")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("vwSRNSummaryExtract");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.vwSRNWithUpdateHistory", b =>
                {
                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("AccountStatusDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("AccountTypeId")
                        .HasColumnType("int");

                    b.Property<int>("BillingCycleDay")
                        .HasColumnType("int");

                    b.Property<int?>("ChangeRequestId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreationDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreditInformationClassificationId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateCompleted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateRequested")
                        .HasColumnType("datetime2");

                    b.Property<string>("FirstReviewRejectReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HistoryBureauInstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HistoryComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("HistoryFileType")
                        .HasColumnType("int");

                    b.Property<int>("HistoryId")
                        .HasColumnType("int");

                    b.Property<string>("HistorySRNFileTestingStatusReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("HistorySRNLastSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("HistorySignoffDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("HistoryStatusId")
                        .HasColumnType("int");

                    b.Property<int?>("HistoryStatusReasonId")
                        .HasColumnType("int");

                    b.Property<bool>("IsComple")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDailyFileLive")
                        .HasColumnType("bit");

                    b.Property<int>("IsLatestHistory")
                        .HasColumnType("int");

                    b.Property<bool>("IsLiveFileSubmissionsSuspended")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMonthlyFileLive")
                        .HasColumnType("bit");

                    b.Property<int?>("LoanManagementSystemVendorId")
                        .HasColumnType("int");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MonthlyFileTestStartDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NCRReportingAccountTypeClassificationId")
                        .HasColumnType("int");

                    b.Property<string>("ProcessInstanceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RolloutStatusId")
                        .HasColumnType("int");

                    b.Property<int?>("SPGroupId")
                        .HasColumnType("int");

                    b.Property<string>("SRNBureauInstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRNComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SRNFileType")
                        .HasColumnType("int");

                    b.Property<int>("SRNId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SRNLastSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("SRNSignoffDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("SRNStatusId")
                        .HasColumnType("int");

                    b.Property<int?>("SRNStatusReasonId")
                        .HasColumnType("int");

                    b.Property<string>("SecondReviewRejectReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SoftwareVendorId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StatusLastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("TradingName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdateNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("UpdateType")
                        .HasColumnType("int");

                    b.ToView("vwSRNWithUpdateHistory");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Views.vwMemberDetails", b =>
                {
                    b.Property<int?>("ALGLeaderId")
                        .HasColumnType("int");

                    b.Property<string>("AnalyticsCompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("AnnualTurnover")
                        .HasColumnType("bigint");

                    b.Property<string>("AuditedFinancials")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CancellationOrDisqualifyReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactContactName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplianceContactOfficeTelNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactContactName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataContactOfficeTelNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DescriptionOfBusiness")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FinancialCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FinancialContactName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FinancialEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FinancialJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FinancialOfficeTelNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HeadOfficePhysicalAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HeadOfficePostalAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("IdentificationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainContactCellNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainContactJobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainContactName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainContactOfficeTelNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MemberALGLeaders")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MemberName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MemberStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("MemberStatusDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MemberStatusSimplified")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MembershipType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NCRCPNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NCRCertificate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NCRRegistrant")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NcrReportingPrimaryBusinessClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PortfolioManager")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryBureau")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrincipleDebtRange")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SACRRAIndustryClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SPNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRNDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SRNNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecondaryBureau")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SoleProprietor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VatNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VatRegistrant")
                        .HasColumnType("nvarchar(max)");

                    b.ToView("vwMemberDetails");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSchedule", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.AdhocFileSubmission", "AdhocFileSubmission")
                        .WithMany.HasForeignKey("AdhocFileSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Bureau")
                        .WithMany.HasForeignKey("BureauId");

                    b.HasOne("Sacrra.Membership.Database.Models.AdhocFileSubmissionReason", "UnnsuccessfulLoadReason")
                        .WithMany.HasForeignKey("UnnsuccessfulLoadReasonId");

                    b.Navigation("AdhocFileSubmission");

                    b.Navigation("Bureau");

                    b.Navigation("UnnsuccessfulLoadReason");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSubmission", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.AdhocFileSubmissionReason", "FileSubmissionReason")
                        .WithMany.HasForeignKey("AdhocFileSubmissionReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany.HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany.HasForeignKey("SRNId");

                    b.Navigation("FileSubmissionReason");

                    b.Navigation("Member");

                    b.Navigation("SRN");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSubmissionReason", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.AdhocFileSubmissionCategory", "FileSubmissionCategory")
                        .WithMany("Reasons")
                        .HasForeignKey("FileSubmissionCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FileSubmissionCategory");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGClientLeader", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Client")
                        .WithMany("Leaders")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Leader")
                        .WithMany("Clients")
                        .HasForeignKey("LeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("Leader");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGMemberDetails", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany.HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BranchLocation", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany("BranchLocations")
                        .HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SRN");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BureauLoadStats", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.AdhocFileSubmission", "AdHocFileSubmission")
                        .WithMany.HasForeignKey("AdHocFileSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany.HasForeignKey("MemberId");

                    b.Navigation("AdHocFileSubmission");

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BureauObscureMapping", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany.HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ChangeRequestStaging", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.User", "User")
                        .WithMany("MemberChangeRequests")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Document", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.DocumentCategory", "Category")
                        .WithMany.HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.DocumentStatus", "Status")
                        .WithMany.HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Status");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DocumentUserAccess", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Document", "Document")
                        .WithMany("AccessedByUsers")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.User", "User")
                        .WithMany("AccessedDocuments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EventLog", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.EntityType", "EntityType")
                        .WithMany.HasForeignKey("EntityTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EntityType");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.HangfireJob", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.User", "CreatedBy")
                        .WithMany.HasForeignKey("CreatedById");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailAttachment", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailgunMessage", "MailgunMessage")
                        .WithMany("Attachments")
                        .HasForeignKey("MailgunMessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailgunMessage");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailColumn", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailSetting", "MailSetting")
                        .WithMany("MailColumns")
                        .HasForeignKey("MailSettingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("MailSetting");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailDeliveryStatus", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailItem", "MailItem")
                        .WithOne("MailDeliveryStatus")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MailDeliveryStatus", "MailItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailItem");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailEnvelop", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailItem", "MailItem")
                        .WithOne("MailEnvelop")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MailEnvelop", "MailItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailItem");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailFlag", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailItem", "MailItem")
                        .WithOne("MailFlag")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MailFlag", "MailItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailItem");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailgunMessage", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailItem", "MailItem")
                        .WithOne("MailgunMessage")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MailgunMessage", "MailItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailItem");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailHeader", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailgunMessage", "MailgunMessage")
                        .WithOne("MailHeader")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MailHeader", "MailgunMessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailgunMessage");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailItem", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailEvent", "MailEvent")
                        .WithMany("MailItems")
                        .HasForeignKey("MailEventId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("MailEvent");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailPaging", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailEvent", "MailEvent")
                        .WithOne("MailPaging")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MailPaging", "MailEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailEvent");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailRecipient", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailSetting", "MailSetting")
                        .WithMany("MailRecipients")
                        .HasForeignKey("MailSettingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("MailSetting");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailStorage", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MailItem", "MailItem")
                        .WithOne("MailStorage")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MailStorage", "MailItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MailItem");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Member", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ChangeRequestStaging", "ChangeRequest")
                        .WithMany.HasForeignKey("ChangeRequestId");

                    b.HasOne("Sacrra.Membership.Database.Models.MemberStatusReason", "MemberStatusReason")
                        .WithMany.HasForeignKey("MemberStatusReasonId");

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "PrimaryBureau")
                        .WithMany.HasForeignKey("PrimaryBureauId");

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "SecondaryBureau")
                        .WithMany.HasForeignKey("SecondaryBureauId");

                    b.HasOne("Sacrra.Membership.Database.Models.User", "StakeholderManager")
                        .WithMany("MembersIManage")
                        .HasForeignKey("StakeholderManagerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ChangeRequest");

                    b.Navigation("MemberStatusReason");

                    b.Navigation("PrimaryBureau");

                    b.Navigation("SecondaryBureau");

                    b.Navigation("StakeholderManager");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberContact", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ContactType", "ContactType")
                        .WithMany.HasForeignKey("ContactTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("Contacts")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ContactType");

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberDocument", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithOne("MemberDocument")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MemberDocument", "MemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberDomain", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("Domains")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberInvoice", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.InvoiceRate", "InvoiceRate")
                        .WithMany.HasForeignKey("InvoiceRateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("Invoices")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("InvoiceRate");

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberUsers", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("Users")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.User", "User")
                        .WithMany("Members")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Member");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSchedule", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Bureau")
                        .WithMany.HasForeignKey("BureauId");

                    b.HasOne("Sacrra.Membership.Database.Models.ReplacementFileSubmission", "ReplacementFileSubmission")
                        .WithMany.HasForeignKey("ReplacementFileSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.ReplacementFileSubmissionReason", "UnnsuccessfulLoadReason")
                        .WithMany.HasForeignKey("UnnsuccessfulLoadReasonId");

                    b.Navigation("Bureau");

                    b.Navigation("ReplacementFileSubmission");

                    b.Navigation("UnnsuccessfulLoadReason");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSubmission", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany.HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.ReplacementFileSubmissionReason", "ReplacementFileSubmissionReason")
                        .WithMany.HasForeignKey("ReplacementFileSubmissionReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SPGroup", "SP")
                        .WithMany.HasForeignKey("SPId");

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany.HasForeignKey("SRNId");

                    b.Navigation("Member");

                    b.Navigation("ReplacementFileSubmissionReason");

                    b.Navigation("SP");

                    b.Navigation("SRN");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSubmissionReason", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ReplacementFileSubmissionCategory", "FileSubmissionCategory")
                        .WithMany("Reasons")
                        .HasForeignKey("FileSubmissionCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FileSubmissionCategory");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SPGroup", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("SPGroups")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRN", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "ALGLeader")
                        .WithMany("ClientSRNs")
                        .HasForeignKey("ALGLeaderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.AccountType", "AccountType")
                        .WithMany.HasForeignKey("AccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.ChangeRequestStaging", "ChangeRequest")
                        .WithMany.HasForeignKey("ChangeRequestId");

                    b.HasOne("Sacrra.Membership.Database.Models.CreditInformationClassification", "CreditInformationClassification")
                        .WithMany.HasForeignKey("CreditInformationClassificationId");

                    b.HasOne("Sacrra.Membership.Database.Models.LoanManagementSystemVendor", "LoanManagementSystemVendor")
                        .WithMany.HasForeignKey("LoanManagementSystemVendorId");

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("SRNs")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.NCRReportingAccountTypeClassification", "NCRReportingAccountTypeClassification")
                        .WithMany.HasForeignKey("NCRReportingAccountTypeClassificationId");

                    b.HasOne("Sacrra.Membership.Database.Models.SPGroup", "SPGroup")
                        .WithMany("SRNs")
                        .HasForeignKey("SPGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatus", "SRNStatus")
                        .WithMany.HasForeignKey("SRNStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatusReason", "SRNStatusReason")
                        .WithMany.HasForeignKey("SRNStatusReasonId");

                    b.HasOne("Sacrra.Membership.Database.Models.SoftwareVendor", "SoftwareVendor")
                        .WithMany.HasForeignKey("SoftwareVendorId");

                    b.Navigation("ALGLeader");

                    b.Navigation("AccountType");

                    b.Navigation("ChangeRequest");

                    b.Navigation("CreditInformationClassification");

                    b.Navigation("LoanManagementSystemVendor");

                    b.Navigation("Member");

                    b.Navigation("NCRReportingAccountTypeClassification");

                    b.Navigation("SPGroup");

                    b.Navigation("SRNStatus");

                    b.Navigation("SRNStatusReason");

                    b.Navigation("SoftwareVendor");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNContact", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ContactType", "ContactType")
                        .WithMany.HasForeignKey("ContactTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany("Contacts")
                        .HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ContactType");

                    b.Navigation("SRN");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNMergeRequest", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "FromSRN")
                        .WithMany.HasForeignKey("FromSRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "ToSRN")
                        .WithMany.HasForeignKey("ToSRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FromSRN");

                    b.Navigation("ToSRN");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNMonthlyOSLAReason", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.MonthlyOSLAReason", "MonthlyOSLAReason")
                        .WithMany.HasForeignKey("MonthlyOSLAReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany.HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MonthlyOSLAReason");

                    b.Navigation("SRN");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSaleRequest", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "BuyerMember")
                        .WithMany.HasForeignKey("BuyerMemberId");

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "BuyerSRN")
                        .WithMany.HasForeignKey("BuyerSRNId");

                    b.HasOne("Sacrra.Membership.Database.Models.SPGroup", "SPGroup")
                        .WithMany.HasForeignKey("SPGroupId");

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany.HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "SellerMember")
                        .WithMany.HasForeignKey("SellerMemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BuyerMember");

                    b.Navigation("BuyerSRN");

                    b.Navigation("SPGroup");

                    b.Navigation("SRN");

                    b.Navigation("SellerMember");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSplitRequest", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "FromSRN")
                        .WithMany.HasForeignKey("FromSRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "ToSRN")
                        .WithMany.HasForeignKey("ToSRNId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FromSRN");

                    b.Navigation("ToSRN");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusReasonSRNStatusLink", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatus", "SRNStatus")
                        .WithMany("SRNStatusReasons")
                        .HasForeignKey("SRNStatusId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatusReason", "SRNStatusReason")
                        .WithMany("SRNStatuses")
                        .HasForeignKey("SRNStatusReasonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("SRNStatus");

                    b.Navigation("SRNStatusReason");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusUpdateHistory", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.RolloutStatus", "RolloutStatus")
                        .WithMany.HasForeignKey("RolloutStatusId");

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany("SRNStatusUpdates")
                        .HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatus", "SRNStatus")
                        .WithMany.HasForeignKey("SRNStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatusReason", "SRNStatusReason")
                        .WithMany.HasForeignKey("SRNStatusReasonId");

                    b.Navigation("RolloutStatus");

                    b.Navigation("SRN");

                    b.Navigation("SRNStatus");

                    b.Navigation("SRNStatusReason");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.TradingName", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("TradingNames")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Member");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AdhocFileSubmissionCategory", b =>
                {
                    b.Navigation("Reasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Document", b =>
                {
                    b.Navigation("AccessedByUsers");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailEvent", b =>
                {
                    b.Navigation("MailItems");

                    b.Navigation("MailPaging");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailgunMessage", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("MailHeader");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailItem", b =>
                {
                    b.Navigation("MailDeliveryStatus");

                    b.Navigation("MailEnvelop");

                    b.Navigation("MailFlag");

                    b.Navigation("MailStorage");

                    b.Navigation("MailgunMessage");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MailSetting", b =>
                {
                    b.Navigation("MailColumns");

                    b.Navigation("MailRecipients");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Member", b =>
                {
                    b.Navigation("ClientSRNs");

                    b.Navigation("Clients");

                    b.Navigation("Contacts");

                    b.Navigation("Domains");

                    b.Navigation("Invoices");

                    b.Navigation("Leaders");

                    b.Navigation("MemberDocument");

                    b.Navigation("SPGroups");

                    b.Navigation("SRNs");

                    b.Navigation("TradingNames");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ReplacementFileSubmissionCategory", b =>
                {
                    b.Navigation("Reasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SPGroup", b =>
                {
                    b.Navigation("SRNs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRN", b =>
                {
                    b.Navigation("BranchLocations");

                    b.Navigation("Contacts");

                    b.Navigation("SRNStatusUpdates");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatus", b =>
                {
                    b.Navigation("SRNStatusReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusReason", b =>
                {
                    b.Navigation("SRNStatuses");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.User", b =>
                {
                    b.Navigation("AccessedDocuments");

                    b.Navigation("MemberChangeRequests");

                    b.Navigation("Members");

                    b.Navigation("MembersIManage");
                });
#pragma warning restore 612, 618
        }
    }
}
