using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class RenamedNcrClassificationIdToIndustryClassificationId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "NcrClassificationId",
                table: "Members",
                newName: "IndustryClassificationId");

            migrationBuilder.RenameColumn(
                name: "NcrClassificationId",
                table: "PartialMembers",
                newName: "IndustryClassificationId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "NcrClassificationId",
                table: "PartialMembers",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NcrClassificationId",
                table: "Members",
                nullable: true);
        }
    }
}
