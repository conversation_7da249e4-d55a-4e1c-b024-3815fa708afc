using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNNavigations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ALGId",
                table: "SRNs",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "BranchLocation",
                table: "SRNs",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LoanManagementSystemVendorId",
                table: "SRNs",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SoftwareVendorId",
                table: "SRNs",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "ALG",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ALG", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LoanManagementSystemVendor",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoanManagementSystemVendor", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SoftwareVendor",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SoftwareVendor", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNs_ALGId",
                table: "SRNs",
                column: "ALGId");

            migrationBuilder.CreateIndex(
                name: "IX_SRNs_LoanManagementSystemVendorId",
                table: "SRNs",
                column: "LoanManagementSystemVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_SRNs_SoftwareVendorId",
                table: "SRNs",
                column: "SoftwareVendorId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_ALG_ALGId",
                table: "SRNs",
                column: "ALGId",
                principalTable: "ALG",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendor_LoanManagementSystemVendorId",
                table: "SRNs",
                column: "LoanManagementSystemVendorId",
                principalTable: "LoanManagementSystemVendor",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SoftwareVendor_SoftwareVendorId",
                table: "SRNs",
                column: "SoftwareVendorId",
                principalTable: "SoftwareVendor",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_ALG_ALGId",
                table: "SRNs");

            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendor_LoanManagementSystemVendorId",
                table: "SRNs");

            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SoftwareVendor_SoftwareVendorId",
                table: "SRNs");

            migrationBuilder.DropTable(
                name: "ALG");

            migrationBuilder.DropTable(
                name: "LoanManagementSystemVendor");

            migrationBuilder.DropTable(
                name: "SoftwareVendor");

            migrationBuilder.DropIndex(
                name: "IX_SRNs_ALGId",
                table: "SRNs");

            migrationBuilder.DropIndex(
                name: "IX_SRNs_LoanManagementSystemVendorId",
                table: "SRNs");

            migrationBuilder.DropIndex(
                name: "IX_SRNs_SoftwareVendorId",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "ALGId",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "BranchLocation",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "LoanManagementSystemVendorId",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "SoftwareVendorId",
                table: "SRNs");
        }
    }
}
