using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class FixMemberDetailsView : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"IF EXISTS
                                    (
                                        SELECT TOP (1) 1 FROM sys.all_objects
                                        WHERE [name] = 'vwMemberDetails'
                                    )
                                    BEGIN
                                        DROP VIEW [dbo].[vwMemberDetails]
                                    END;
                                    GO

                                    CREATE VIEW [dbo].[vwMemberDetails]
                                    AS
                                    WITH algLeaders AS
                                    (
                                        SELECT
                                            srn.[MemberId],
                                            [MemberALGLeaders] = STRING_AGG(alg.[RegisteredName], ',')
                                        FROM [dbo].[SRNs] srn
                                        LEFT OUTER JOIN [dbo].[Members] alg ON alg.[Id] = srn.[ALGLeaderId]
                                        GROUP BY srn.[MemberId]
                                    ),
                                    mainContacts AS (
                                        SELECT MemberId, FirstName, Surname, OfficeTelNumber, CellNumber, Email, JobTitle,
                                               ROW_NUMBER() OVER (PARTITION BY MemberId ORDER BY Id) as rn
                                        FROM [dbo].[MemberContacts] 
                                        WHERE ContactTypeId = 1
                                    ),
                                    financialContacts AS (
                                        SELECT MemberId, FirstName, Surname, OfficeTelNumber, CellNumber, Email, JobTitle,
                                               ROW_NUMBER() OVER (PARTITION BY MemberId ORDER BY Id) as rn
                                        FROM [dbo].[MemberContacts] 
                                        WHERE ContactTypeId = 2
                                    ),
                                    complianceContacts AS (
                                        SELECT MemberId, FirstName, Surname, OfficeTelNumber, CellNumber, Email, JobTitle,
                                               ROW_NUMBER() OVER (PARTITION BY MemberId ORDER BY Id) as rn
                                        FROM [dbo].[MemberContacts] 
                                        WHERE ContactTypeId = 3
                                    ),
                                    dataContacts AS (
                                        SELECT MemberId, FirstName, Surname, OfficeTelNumber, CellNumber, Email, JobTitle,
                                               ROW_NUMBER() OVER (PARTITION BY MemberId ORDER BY Id) as rn
                                        FROM [dbo].[MemberContacts] 
                                        WHERE ContactTypeId = 4
                                    )
                                    SELECT
                                        mem.[Id],
                                        srn.[ALGLeaderId],
                                        srn.[SRNNumber],
                                        srn.[TradingName] AS [SRNDisplayName],
                                        spg.[SPNumber],
                                        mem.[RegisteredName] AS [MemberName],
                                        mem.[RegisteredNumber] AS [CompanyRegistrationNumber],
                                        CASE 
                                            WHEN mem.[ApplicationStatusId] = 1 THEN 'Member Registration: Submitted'
                                            WHEN mem.[ApplicationStatusId] = 2 THEN 'Member Registration: Review'
                                            WHEN mem.[ApplicationStatusId] = 3 THEN 'Member Registration: Disqualified'
                                            WHEN mem.[ApplicationStatusId] = 4 THEN 'Member Registration: Awaiting Payment'
                                            WHEN mem.[ApplicationStatusId] = 5 THEN 'Member Registration: Payment Received'
                                            WHEN mem.[ApplicationStatusId] = 6 THEN 'Member Registration: Approved'
                                            WHEN mem.[ApplicationStatusId] = 7 THEN 'Member Registration: Completed'
                                            WHEN mem.[ApplicationStatusId] = 8 THEN 'Member Registration: Awaiting Generation of Initial Assessment Invoice'
                                            WHEN mem.[ApplicationStatusId] = 9 THEN 'Member Registration: Awaiting Initial Assessment Invoice Payment' 
                                            WHEN mem.[ApplicationStatusId] = 10 THEN 'Member Registration: SHM Final Review'
                                            WHEN mem.[ApplicationStatusId] = 11 THEN 'Member Registration: Cancelled. Initial Assessment Invoice not Paid'
                                            WHEN mem.[ApplicationStatusId] = 12 THEN 'Member Registration: Awaiting Onboarding Invoice Payment'
                                            WHEN mem.[ApplicationStatusId] = 13 THEN 'Member Registration: Waiting for Invoice to be Generated'
                                            WHEN mem.[ApplicationStatusId] = 14 THEN 'Cancelled'
                                            WHEN mem.[ApplicationStatusId] = 15 THEN 'Active' 
                                        END AS [MemberStatus],
                                        CASE 
                                            WHEN mem.[ApplicationStatusId] IN (1,2,4,5,6,8,9,10,11,12,13) THEN 'Requested'
                                            WHEN mem.[ApplicationStatusId] IN (7,15) THEN 'Completed'
                                            WHEN mem.[ApplicationStatusId] IN (3) THEN 'Disqualified'
                                            WHEN mem.[ApplicationStatusId] IN (14) THEN 'Cancelled'
                                        END AS [MemberStatusSimplified],
                                        CASE 
                                            WHEN mem.[ApplicationStatusId] IN (1,2,4,5,8,9,10,11,12,13) THEN mem.[DateCreated]
                                            WHEN mem.[ApplicationStatusId] IN (6,15) THEN mem.[DateActivated]
                                            WHEN mem.[ApplicationStatusId] IN (3,14) THEN mem.[DateCancelled]
                                        END AS [MemberStatusDate],
                                        CASE 
                                            WHEN mem.[ApplicationStatusId] IN (3) THEN mem.[DisqualificationReason]
                                            ELSE NULL
                                        END AS [CancellationOrDisqualifyReason],
                                        mem.[Comments] AS [DescriptionOfBusiness],
                                        NULL AS [NCRCertificate],
                                        NULL AS [AuditedFinancials],
                                        CASE mem.[MembershipTypeId]
                                            WHEN 1 THEN 'Full Member'
                                            WHEN 2 THEN 'Non Member'
                                            WHEN 3 THEN 'Affiliate'
                                            WHEN 4 THEN 'ALG Client'
                                            WHEN 5 THEN 'ALG Leader'
                                            WHEN 6 THEN 'Bureau'
                                        END AS [MembershipType],
                                        algl.[MemberALGLeaders],
                                        CASE 
                                            WHEN mem.[IndustryClassificationId] = 1 THEN 'Furniture Retail'
                                            WHEN mem.[IndustryClassificationId] = 2 THEN 'Insurance'
                                            WHEN mem.[IndustryClassificationId] = 3 THEN 'Retail Apparel'
                                            WHEN mem.[IndustryClassificationId] = 4 THEN 'Secured'
                                            WHEN mem.[IndustryClassificationId] = 5 THEN 'Unsecured'
                                            WHEN mem.[IndustryClassificationId] = 7 THEN 'Other'
                                            WHEN mem.[IndustryClassificationId] = 8 THEN 'Telecommunication'
                                            WHEN mem.[IndustryClassificationId] = 9 THEN 'Secured Banks'
                                            WHEN mem.[IndustryClassificationId] = 10 THEN 'Other - Debt Collectors or Debt Purchasers'
                                            WHEN mem.[IndustryClassificationId] = 11 THEN 'Secured vehicle finance'
                                            WHEN mem.[IndustryClassificationId] = 12 THEN 'Subscription'
                                            WHEN mem.[IndustryClassificationId] = 13 THEN 'Secured other financial institutions' 
                                        END AS [SACRRAIndustryClassification],
                                        pm.[FirstName] + ' ' + pm.[LastName] AS [PortfolioManager],
                                        mem.[AnnualTurnover],
                                        CASE 
                                            WHEN mem.[PrincipleDebtRangeId] = 1 THEN 'R15,000,000,000+' 
                                            WHEN mem.[PrincipleDebtRangeId] = 2 THEN 'R5,000,000,000 - R14,999,999,999'
                                            WHEN mem.[PrincipleDebtRangeId] = 3 THEN 'R1,000,000,000 - R4,999,999,999'
                                            WHEN mem.[PrincipleDebtRangeId] = 4 THEN 'R100,000,000 - R999,999,999'
                                            WHEN mem.[PrincipleDebtRangeId] = 5 THEN 'R5,000,000 - R99,999,999'
                                            WHEN mem.[PrincipleDebtRangeId] = 6 THEN 'R1,000,000 - R4,999,999'
                                            WHEN mem.[PrincipleDebtRangeId] = 7 THEN 'R500,000 - R999,999'
                                            WHEN mem.[PrincipleDebtRangeId] = 8 THEN 'R250,000 - R499,999'
                                            WHEN mem.[PrincipleDebtRangeId] = 9 THEN 'Less than R250,000'
                                        END AS [PrincipleDebtRange],
                                        CASE mem.[IsNcrRegistrant]
                                            WHEN 1 THEN 'Yes'
                                            WHEN 0 THEN 'No'
                                        END AS [NCRRegistrant],
                                        mem.[NcrcpNumber] AS [NCRCPNumber],
                                        CASE 
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 1 THEN '7.2 a Banks' 
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 2 THEN '7,2 b Retailers'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 3 THEN '7.2 c VAF & Other Financial Institutions'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 4 THEN '7.2 d Microlenders'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 5 THEN '7.2 e Telecommunications'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 6 THEN '7.2 f Utility Companies'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 7 THEN '7.2 g Insurance'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 8 THEN '7.2 h On sellers'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 9 THEN '7.2 i Employers or Employment Agencies'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 10 THEN '7.2 j Debt Collectors'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 11 THEN '7.2 k Debt Counsellors'
                                            WHEN mem.[NcrReportingPrimaryBusinessClassificationId] = 12 THEN '7.2 l Other'
                                        END AS [NcrReportingPrimaryBusinessClassification],
                                        CASE
                                            WHEN mem.[IdNumber] IS NULL THEN 'No'
                                            WHEN mem.[IdNumber] IS NOT NULL THEN 'Yes'
                                        END AS [SoleProprietor],
                                        mem.[IdNumber] AS [IdentificationNumber],
                                        CASE mem.[IsVatRegistrant]
                                            WHEN 1 THEN 'Yes'
                                            WHEN 0 THEN 'No'
                                        END AS [VatRegistrant],
                                        mem.[VatNumber],
                                        pri.[RegisteredName] AS [PrimaryBureau],
                                        sec.[RegisteredName] AS [SecondaryBureau],
                                        mem.[AnalyticsCompanyName],
                                        mem.[HeadOfficePhysicalAddress],
                                        mem.[HeadOfficePostalAddress],
                                        mai.[FirstName] + ' ' + mai.[FirstName] AS [MainContactName],
                                        mai.[OfficeTelNumber] AS [MainContactOfficeTelNumber],
                                        mai.[CellNumber] AS [MainContactCellNumber],
                                        mai.[Email] AS [MainContactEmail],
                                        mai.[JobTitle] AS [MainContactJobTitle],
                                        fin.[FirstName] + ' ' + fin.[FirstName] AS [FinancialContactName],
                                        fin.[OfficeTelNumber] AS [FinancialOfficeTelNumber],
                                        fin.[CellNumber] AS [FinancialCellNumber],
                                        fin.[Email] AS [FinancialEmail],
                                        fin.[JobTitle] AS [FinancialJobTitle],
                                        com.[FirstName] + ' ' + com.[FirstName] AS [ComplianceContactContactName],
                                        com.[OfficeTelNumber] AS [ComplianceContactOfficeTelNumber],
                                        com.[CellNumber] AS [ComplianceContactCellNumber],
                                        com.[Email] AS [ComplianceContactEmail],
                                        com.[JobTitle] AS [ComplianceContactJobTitle],
                                        dat.[FirstName] + ' ' + dat.[FirstName] AS [DataContactContactName],
                                        dat.[OfficeTelNumber] AS [DataContactOfficeTelNumber],
                                        dat.[CellNumber] AS [DataContactCellNumber],
                                        dat.[Email] AS [DataContactEmail],
                                        dat.[JobTitle] AS [DataContactJobTitle]
                                    FROM [dbo].[Members] mem
                                    INNER JOIN [dbo].[Users] pm ON mem.[StakeholderManagerId] = pm.[Id]
                                    LEFT OUTER JOIN mainContacts mai ON mai.[MemberId] = mem.[Id] AND mai.rn = 1
                                    LEFT OUTER JOIN financialContacts fin ON fin.[MemberId] = mem.[Id] AND fin.rn = 1
                                    LEFT OUTER JOIN complianceContacts com ON com.[MemberId] = mem.[Id] AND com.rn = 1
                                    LEFT OUTER JOIN dataContacts dat ON dat.[MemberId] = mem.[Id] AND dat.rn = 1
                                    LEFT OUTER JOIN [dbo].[SRNs] srn ON srn.[MemberId] = mem.[Id]
                                    LEFT OUTER JOIN [dbo].[SPGroups] spg ON spg.[Id] = srn.[SPGroupId]
                                    LEFT OUTER JOIN algLeaders algl ON mem.[Id] = algl.[MemberId]
                                    LEFT OUTER JOIN [dbo].[Members] pri ON mem.[PrimaryBureauId] = pri.[Id]
                                    LEFT OUTER JOIN [dbo].[Members] sec ON mem.[PrimaryBureauId] = sec.[Id];");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"IF EXISTS
                                    (
                                        SELECT TOP (1) 1 FROM sys.all_objects
                                        WHERE [name] = 'vwMemberDetails'
                                    )
                                    BEGIN
                                        DROP VIEW [dbo].[vwMemberDetails]
                                    END;");
        }
    }
}
