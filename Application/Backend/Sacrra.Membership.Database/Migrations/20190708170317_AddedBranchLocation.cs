using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedBranchLocation : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BranchLocation",
                table: "SRNs");

            migrationBuilder.AddColumn<int>(
                name: "BranchLocationId",
                table: "SRNs",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "BranchLocations",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BranchLocations", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNs_BranchLocationId",
                table: "SRNs",
                column: "BranchLocationId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_BranchLocations_BranchLocationId",
                table: "SRNs",
                column: "BranchLocationId",
                principalTable: "BranchLocations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_BranchLocations_BranchLocationId",
                table: "SRNs");

            migrationBuilder.DropTable(
                name: "BranchLocations");

            migrationBuilder.DropIndex(
                name: "IX_SRNs_BranchLocationId",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "BranchLocationId",
                table: "SRNs");

            migrationBuilder.AddColumn<string>(
                name: "BranchLocation",
                table: "SRNs",
                nullable: true);
        }
    }
}
