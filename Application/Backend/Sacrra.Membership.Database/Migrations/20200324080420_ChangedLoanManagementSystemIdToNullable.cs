using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class ChangedLoanManagementSystemIdToNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendors_LoanManagementSystemVendorId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "LoanManagementSystemVendorId",
                table: "SRNs",
                nullable: true,
                oldClrType: typeof(int));

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendors_LoanManagementSystemVendorId",
                table: "SRNs",
                column: "LoanManagementSystemVendorId",
                principalTable: "LoanManagementSystemVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendors_LoanManagementSystemVendorId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "LoanManagementSystemVendorId",
                table: "SRNs",
                nullable: false,
                oldClrType: typeof(int),
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendors_LoanManagementSystemVendorId",
                table: "SRNs",
                column: "LoanManagementSystemVendorId",
                principalTable: "LoanManagementSystemVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
