using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddMemberToStats : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MemberId",
                table: "BureauLoadStats",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_BureauLoadStats_MemberId",
                table: "BureauLoadStats",
                column: "MemberId");

            migrationBuilder.AddForeignKey(
                name: "FK_BureauLoadStats_Members_MemberId",
                table: "BureauLoadStats",
                column: "MemberId",
                principalTable: "Members",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BureauLoadStats_Members_MemberId",
                table: "BureauLoadStats");

            migrationBuilder.DropIndex(
                name: "IX_BureauLoadStats_MemberId",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "MemberId",
                table: "BureauLoadStats");
        }
    }
}
