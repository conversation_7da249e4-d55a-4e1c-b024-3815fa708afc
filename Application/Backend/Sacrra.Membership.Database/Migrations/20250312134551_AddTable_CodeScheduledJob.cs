using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddTable_CodeScheduledJob : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HangfireScheduledJobs",
                columns: table => new
                {
                    JobName = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    MethodClass = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MethodName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Parameters = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CronString = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HangfireScheduledJobs", x => x.JobName);
                });

            migrationBuilder.InsertData(
                table: "HangfireScheduledJobs",
                columns: new[] { "JobName", "CronString", "MethodClass", "MethodName", "Parameters" },
                values: new object[,]
                {
                    { "Cleanup-API-Errors", "0 21 * * *", "Sacrra.Membership.Business.Services.HangfireService", "CleanupApiErrors", "5" },
                    { "Cleanup-Camunda-Errors", "0 21 * * *", "Sacrra.Membership.Business.Services.HangfireService", "CleanupCamundaErrors", "5" }
                    // { "Create-DW-Camunda-Exception-Tasks", "15 * * * *", "Sacrra.Membership.Business.Services.HangfireService", "CreateCamundaTaskForNewDWExceptions", null },
                    // { "Sync-Freshdesk-Companies", "0 18 * * *", "Sacrra.Membership.Freshdesk.Services.CompanyService", "CreateCompanies", null },
                    // { "Sync-Freshdesk-Contacts", "0 19 * * *", "Sacrra.Membership.Freshdesk.Services.CompanyService", "CreateContacts", null }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HangfireScheduledJobs");
        }
    }
}
