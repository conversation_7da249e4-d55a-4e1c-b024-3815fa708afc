using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class UpdatedBureauLoadStatsToCorrectFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BureauLoadStats_Users_StakeholderManagerId",
                table: "BureauLoadStats");

            migrationBuilder.DropIndex(
                name: "IX_BureauLoadStats_StakeholderManagerId",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "ALGLeaderId",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "ActualSubmissionDate",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "AdhocReasonId",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "BureauInstruction",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "DataContributorName",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "FileName",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "ProposedSubmissionDate",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "SRNDisplayName",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "SRNNumber",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "StatusDate",
                table: "BureauLoadStats");

            migrationBuilder.RenameColumn(
                name: "SubmissionStatusId",
                table: "BureauLoadStats",
                newName: "NumberOfRecordsUnmatched");

            migrationBuilder.RenameColumn(
                name: "StakeholderManagerId",
                table: "BureauLoadStats",
                newName: "NumberOfRecordsReceived");

            migrationBuilder.RenameColumn(
                name: "SACRRAIndustryId",
                table: "BureauLoadStats",
                newName: "NumberOfRecordsMatchedButNotUpdated");

            migrationBuilder.RenameColumn(
                name: "SACRRAAccountTypeId",
                table: "BureauLoadStats",
                newName: "NumberOfRecordsMatchedAndUpdated");

            migrationBuilder.RenameColumn(
                name: "NumberOfRecords",
                table: "BureauLoadStats",
                newName: "NumberOfRecordsMatched");

            migrationBuilder.AddColumn<DateTime>(
                name: "DateNewQE1ExtractSharedPostCleanup",
                table: "BureauLoadStats",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NumberOfDuplicatesRemovedFromDBBasedOnExtract",
                table: "BureauLoadStats",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NumberOfRecordsMatchedSuccessfullyConverted",
                table: "BureauLoadStats",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NumberOfRecordsMergedAcrossSRNs",
                table: "BureauLoadStats",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NumberOfRecordsMergedWithinSRN",
                table: "BureauLoadStats",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NumberOfRecordsMigrated",
                table: "BureauLoadStats",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TotalNumberOfQE1RecordRemainingOnDBPostCleanup",
                table: "BureauLoadStats",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DateNewQE1ExtractSharedPostCleanup",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "NumberOfDuplicatesRemovedFromDBBasedOnExtract",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "NumberOfRecordsMatchedSuccessfullyConverted",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "NumberOfRecordsMergedAcrossSRNs",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "NumberOfRecordsMergedWithinSRN",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "NumberOfRecordsMigrated",
                table: "BureauLoadStats");

            migrationBuilder.DropColumn(
                name: "TotalNumberOfQE1RecordRemainingOnDBPostCleanup",
                table: "BureauLoadStats");

            migrationBuilder.RenameColumn(
                name: "NumberOfRecordsUnmatched",
                table: "BureauLoadStats",
                newName: "SubmissionStatusId");

            migrationBuilder.RenameColumn(
                name: "NumberOfRecordsReceived",
                table: "BureauLoadStats",
                newName: "StakeholderManagerId");

            migrationBuilder.RenameColumn(
                name: "NumberOfRecordsMatchedButNotUpdated",
                table: "BureauLoadStats",
                newName: "SACRRAIndustryId");

            migrationBuilder.RenameColumn(
                name: "NumberOfRecordsMatchedAndUpdated",
                table: "BureauLoadStats",
                newName: "SACRRAAccountTypeId");

            migrationBuilder.RenameColumn(
                name: "NumberOfRecordsMatched",
                table: "BureauLoadStats",
                newName: "NumberOfRecords");

            migrationBuilder.AddColumn<int>(
                name: "ALGLeaderId",
                table: "BureauLoadStats",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "ActualSubmissionDate",
                table: "BureauLoadStats",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "AdhocReasonId",
                table: "BureauLoadStats",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "BureauInstruction",
                table: "BureauLoadStats",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataContributorName",
                table: "BureauLoadStats",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileName",
                table: "BureauLoadStats",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ProposedSubmissionDate",
                table: "BureauLoadStats",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "SRNDisplayName",
                table: "BureauLoadStats",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SRNNumber",
                table: "BureauLoadStats",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "StatusDate",
                table: "BureauLoadStats",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.CreateIndex(
                name: "IX_BureauLoadStats_StakeholderManagerId",
                table: "BureauLoadStats",
                column: "StakeholderManagerId");

            migrationBuilder.AddForeignKey(
                name: "FK_BureauLoadStats_Users_StakeholderManagerId",
                table: "BureauLoadStats",
                column: "StakeholderManagerId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
