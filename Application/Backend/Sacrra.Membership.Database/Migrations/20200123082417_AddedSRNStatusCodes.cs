using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNStatusCodes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "SRNStatuses",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActivityAllowedWhileInProcess",
                table: "SRNStatuses",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                table: "SRNStatuses");

            migrationBuilder.DropColumn(
                name: "IsActivityAllowedWhileInProcess",
                table: "SRNStatuses");
        }
    }
}
