using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddReplacementFileTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReplacementFileSubmissionCategories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReplacementFileSubmissionCategories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ReplacementFileSubmissionReasons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FileSubmissionCategoryId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReplacementFileSubmissionReasons", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReplacementFileSubmissionReasons_ReplacementFileSubmissionCategories_FileSubmissionCategoryId",
                        column: x => x.FileSubmissionCategoryId,
                        principalTable: "ReplacementFileSubmissionCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ReplacementFileSubmissions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SRNId = table.Column<int>(type: "int", nullable: false),
                    MemberId = table.Column<int>(type: "int", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReplacementFileName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NumberOfRecords = table.Column<long>(type: "bigint", nullable: false),
                    NumberOfFiles = table.Column<long>(type: "bigint", nullable: false),
                    ReplacementFileSubmissionStatusId = table.Column<int>(type: "int", nullable: false),
                    SubmissionStatusDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReplacementFileSubmissionReasonId = table.Column<int>(type: "int", nullable: false),
                    ReplacementFileSubmissionDeclineReasonId = table.Column<long>(type: "bigint", nullable: true),
                    SACRRAIndustry = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SACRRAAccountType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ActualSubmissionDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    ReasonForDeletion = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastUpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PlannedSubmissionDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReplacementFileSubmissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReplacementFileSubmissions_Members_MemberId",
                        column: x => x.MemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReplacementFileSubmissions_ReplacementFileSubmissionReasons_ReplacementFileSubmissionReasonId",
                        column: x => x.ReplacementFileSubmissionReasonId,
                        principalTable: "ReplacementFileSubmissionReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReplacementFileSubmissions_SRNs_SRNId",
                        column: x => x.SRNId,
                        principalTable: "SRNs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ReplacementFileSchedule",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BureauId = table.Column<int>(type: "int", nullable: false),
                    ReplacementFileName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UnnsuccessfulLoadReasonId = table.Column<int>(type: "int", nullable: true),
                    ReplacementFileSubmissionId = table.Column<int>(type: "int", nullable: false),
                    ReplacementFileBureauStatusId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReplacementFileSchedule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReplacementFileSchedule_ReplacementFileSubmissions_ReplacementFileSubmissionId",
                        column: x => x.ReplacementFileSubmissionId,
                        principalTable: "ReplacementFileSubmissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReplacementFileSchedule_ReplacementFileSubmissionId",
                table: "ReplacementFileSchedule",
                column: "ReplacementFileSubmissionId");

            migrationBuilder.CreateIndex(
                name: "IX_ReplacementFileSubmissionReasons_FileSubmissionCategoryId",
                table: "ReplacementFileSubmissionReasons",
                column: "FileSubmissionCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_ReplacementFileSubmissions_MemberId",
                table: "ReplacementFileSubmissions",
                column: "MemberId");

            migrationBuilder.CreateIndex(
                name: "IX_ReplacementFileSubmissions_ReplacementFileSubmissionReasonId",
                table: "ReplacementFileSubmissions",
                column: "ReplacementFileSubmissionReasonId");

            migrationBuilder.CreateIndex(
                name: "IX_ReplacementFileSubmissions_SRNId",
                table: "ReplacementFileSubmissions",
                column: "SRNId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReplacementFileSchedule");

            migrationBuilder.DropTable(
                name: "ReplacementFileSubmissions");

            migrationBuilder.DropTable(
                name: "ReplacementFileSubmissionReasons");

            migrationBuilder.DropTable(
                name: "ReplacementFileSubmissionCategories");
        }
    }
}
