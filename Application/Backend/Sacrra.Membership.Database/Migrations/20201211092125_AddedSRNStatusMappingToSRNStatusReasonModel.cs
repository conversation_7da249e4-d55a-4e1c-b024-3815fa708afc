using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNStatusMappingToSRNStatusReasonModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SRNStatusId",
                table: "SRNStatusReasons",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SRNStatusReasons_SRNStatusId",
                table: "SRNStatusReasons",
                column: "SRNStatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNStatusReasons_SRNStatuses_SRNStatusId",
                table: "SRNStatusReasons",
                column: "SRNStatusId",
                principalTable: "SRNStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNStatusReasons_SRNStatuses_SRNStatusId",
                table: "SRNStatusReasons");

            migrationBuilder.DropIndex(
                name: "IX_SRNStatusReasons_SRNStatusId",
                table: "SRNStatusReasons");

            migrationBuilder.DropColumn(
                name: "SRNStatusId",
                table: "SRNStatusReasons");
        }
    }
}
