using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class SRNFileTestingStatusReason : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SRNFileTestingStatusReason",
                table: "SRNStatusUpdateHistory",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.Sql(@"IF EXISTS
                                    (
	                                    SELECT TOP (1) 1 FROM sys.all_objects
	                                    WHERE [name] = 'vwSRNWithUpdateHistory'
                                    )
                                    BEGIN
	                                    DROP VIEW [dbo].[vwSRNWithUpdateHistory]
                                    END;
                                    GO

									CREATE VIEW [dbo].[vwSRNWithUpdateHistory]
									AS
									SELECT
										[SRNId] = srn.[Id]
										,srn.[MemberId]
										,srn.[AccountTypeId]
										,srn.[BillingCycleDay]
										,srn.[NumberOfActiveAccounts]
										,srn.[SRNNumber]
										,srn.[LoanManagementSystemVendorId]
										,srn.[SoftwareVendorId]
										,[SRNStatusId] = srn.[SRNStatusId]
										,srn.[SecondReviewRejectReason]
										,srn.[NCRReportingAccountTypeClassificationId]
										,srn.[TradingName]
										,srn.[SPGroupId]
										,srn.[ChangeRequestId]
										,srn.[FirstReviewRejectReason]
										,srn.[ALGLeaderId]
										,srn.[StatusLastUpdatedAt]
										,[SRNStatusReasonId] = srn.[SRNStatusReasonId]
										,[SRNLastSubmissionDate] = srn.[LastSubmissionDate]
										,srn.[AccountStatusDate]
										,[SRNFileType] = srn.[FileType]
										,[SRNBureauInstruction] = srn.[BureauInstruction]
										,[SRNComment] = srn.[Comments]
										,srn.[CreationDate]
										,[SRNSignoffDate] = srn.[SignoffDate]
										,srn.[DateRequested]
										,srn.[CreditInformationClassificationId]
										,[HistoryId] = hist.[Id]
										,hist.[DailyFileDevelopmentStartDate]
										,hist.[DailyFileDevelopmentEndDate]
										,hist.[DailyFileTestStartDate]
										,hist.[DailyFileTestEndDate]
										,hist.[DailyFileGoLiveDate]
										,hist.[MonthlyFileDevelopmentStartDate]
										,hist.[MonthlyFileDevelopmentEndDate]
										,hist.[MonthlyFileTestStartDate]
										,hist.[MonthlyFileTestEndDate]
										,hist.[MonthlyFileGoLiveDate]
										,hist.[IsDailyFileLive]
										,hist.[IsMonthlyFileLive]
										,hist.[IsLiveFileSubmissionsSuspended]
										,[HistoryComment] = hist.[Comments]
										,[HistoryBureauInstruction] = hist.[BureauInstruction]
										,[HistorySRNLastSubmissionDate] = hist.[LastSubmissionDate]
										,hist.[DateCreated]
										,hist.[ProcessInstanceId]
										,hist.[IsComple]
										,hist.[DateCompleted]
										,[HistoryFileType] = hist.[FileType]
										,hist.[UpdateType]
										,[HistoryStatusId] = hist.[SRNStatusId]
										,[HistoryStatusReasonId] = hist.[SRNStatusReasonId]
										,hist.[UpdateNumber]
										,hist.[RolloutStatusId]
										,[HistorySignoffDate] = hist.[SignoffDate]
										,[HistorySRNFileTestingStatusReason] = hist.SRNFileTestingStatusReason
										,[IsLatestHistory] = CASE WHEN hist.[MaxId] = hist.[Id] THEN 1 ELSE 0 END
									FROM [dbo].[SRNs] srn
									INNER JOIN 
									(
										SELECT 
											[MaxId] = MAX(Id) OVER (PARTITION BY [SRNId]
											,[FileType])
											,[Id]
											,[DailyFileDevelopmentStartDate]
											,[DailyFileDevelopmentEndDate]
											,[DailyFileTestStartDate]
											,[DailyFileTestEndDate]
											,[DailyFileGoLiveDate]
											,[MonthlyFileDevelopmentStartDate]
											,[MonthlyFileDevelopmentEndDate]
											,[MonthlyFileTestStartDate]
											,[MonthlyFileTestEndDate]
											,[MonthlyFileGoLiveDate]
											,[IsDailyFileLive]
											,[IsMonthlyFileLive]
											,[IsLiveFileSubmissionsSuspended]
											,[Comments]
											,[BureauInstruction]
											,[LastSubmissionDate]
											,[DateCreated]
											,[SRNId]
											,[ProcessInstanceId]
											,[IsComple]
											,[DateCompleted]
											,[FileType]
											,[UpdateType]
											,[SRNStatusId]
											,[SRNStatusReasonId]
											,[UpdateNumber]
											,[RolloutStatusId]
											,[SRNFileTestingStatusReason]
											,[SignoffDate]
										FROM [dbo].[SRNStatusUpdateHistory]
									) hist
									ON hist.[SRNId] = srn.[Id];
									GO

									SELECT * FROM [dbo].[vwSRNWithUpdateHistory];");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SRNFileTestingStatusReason",
                table: "SRNStatusUpdateHistory");
        }
    }
}
