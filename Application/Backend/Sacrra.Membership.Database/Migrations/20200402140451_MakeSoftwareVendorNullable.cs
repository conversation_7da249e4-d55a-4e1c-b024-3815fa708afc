using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class MakeSoftwareVendorNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SoftwareVendors_SoftwareVendorId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "SoftwareVendorId",
                table: "SRNs",
                nullable: true,
                oldClrType: typeof(int));

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SoftwareVendors_SoftwareVendorId",
                table: "SRNs",
                column: "SoftwareVendorId",
                principalTable: "SoftwareVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SoftwareVendors_SoftwareVendorId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "SoftwareVendorId",
                table: "SRNs",
                nullable: false,
                oldClrType: typeof(int),
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SoftwareVendors_SoftwareVendorId",
                table: "SRNs",
                column: "SoftwareVendorId",
                principalTable: "SoftwareVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
