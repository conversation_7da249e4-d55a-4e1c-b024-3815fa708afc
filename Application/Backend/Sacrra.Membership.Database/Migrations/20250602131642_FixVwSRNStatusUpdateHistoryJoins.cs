using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class FixVwSRNStatusUpdateHistoryJoins : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
ALTER VIEW [dbo].[vwSRNSummaryExtract] 
AS
	WITH branchlocations
		AS
		(
			SELECT 
				[SRNId]
				,[BranchLocations] = STRING_AGG(bra.[Name],',')
			FROM [dbo].[BranchLocations] bra
			GROUP BY [SRNId]
		)
		SELECT
			[CompanyRegistrationNumber] = ISNULL(mem.[RegisteredNumber], mem.[IdNumber])
			,[MemberName] = mem.[RegisteredName]
			,[SRN] = srn.[SRNNumber]
			,[SPNumber] = spg.[SPNumber]
			,[SRNDisplayName] = srn.[TradingName]
			,[CreditInformationClassification] = cla.[Name]
			,[PortfolioManager] = shm.[FirstName] + ' ' + shm.[LastName]
			,[AccountType] = acc.[Name]
			,[NCRReportingAccountTypeClassification] = ncr.[Name]
			,[BillingCycleDay] = srn.[BillingCycleDay]
			,[SRNALGLeader] = alg.[RegisteredName]
			,[LoanManagementSystemVendor] = loa.[Name]
			,[BranchLocations] = bra.[BranchLocations]
			,[NumberMonthlyRecords] = 1
			,[SRNCreationDate] = srn.[CreationDate]
			,[DataContactName] = dat.[FirstName] + ' ' + dat.[Surname]
			,[DataContactOfficeNumber] = dat.[OfficeTelNumber]
			,[DataContactCellNumber] = dat.[CellNumber]
			,[DataContactEmail] = dat.[Email]
			,[DataContactJobTitle] = dat.[JobTitle]
			,[ManualAmendmentsName] = man.[FirstName] + ' ' + man.[Surname]
			,[ManualAmendmentsOfficeNumber] = man.[OfficeTelNumber]
			,[ManualAmendmentsCellNumber] = man.[CellNumber]
			,[ManualAmendmentsEmail] = man.[Email]
			,[ManualAmendmentsJobTitle] = man.[JobTitle]
			,[Status] = srnStatuses.[Name]
			,[StatusDate] = srnSH.[StatusDate]
			,[StatusReason] = srnStatusReasons.[Name]
			,[StatusComment] = srnSH.[StatusComment]
			,[LastSubmissionDate] = srnSH.[LastSubmissionDate]
			,[BureauClosureDate] = srnSH.[BureauClosureDate]
			,[BureauClosureInstruction] = srnSH.[BureauClosureInstruction]
		FROM [dbo].[SRNs] srn

		INNER JOIN [dbo].[Members] mem
		ON srn.[MemberId] = mem.[Id]

		INNER JOIN [dbo].[Users] shm
		ON mem.[StakeholderManagerId] = shm.[Id]

		INNER JOIN [dbo].[AccountTypes] acc
		ON srn.[AccountTypeId] = acc.[Id]

		INNER JOIN [dbo].[NCRReportingAccountTypeClassifications] ncr
		ON srn.[NCRReportingAccountTypeClassificationId] = ncr.[Id]

		INNER JOIN [dbo].[SRNContacts] dat
		ON srn.[Id] = dat.[SRNId]
		AND dat.[ContactTypeId] = 5

		INNER JOIN [dbo].[SRNContacts] man
		ON srn.[Id] = man.[SRNId]
		AND man.[ContactTypeId] = 6
						
		LEFT OUTER JOIN [dbo].[SRNStatusHistory] srnSH
		ON srnSH.[SRNId] = srn.[Id]

		LEFT OUTER JOIN [dbo].[SRNStatuses] srnStatuses
		ON srnStatuses.[Id] = srnSH.[StatusId]

		LEFT OUTER JOIN [dbo].[SRNStatusReasons] srnStatusReasons
		ON srnStatuses.[Id] = srnSH.[StatusReasonId]

		LEFT OUTER JOIN [dbo].[CreditInformationClassifications] cla
		ON srn.[CreditInformationClassificationId] = cla.[Id]

		LEFT OUTER JOIN [dbo].[SPGroups] spg
		ON srn.[SPGroupId] = spg.[Id]

		LEFT OUTER JOIN [dbo].[Members] alg
		ON srn.[ALGLeaderId] = alg.[Id]

		LEFT OUTER JOIN [dbo].[LoanManagementSystemVendors] loa
		ON srn.[LoanManagementSystemVendorId] = loa.[Id]

		LEFT OUTER JOIN branchlocations bra
		ON srn.[Id] = bra.[SRNId]
GO");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNSummaryExtract];");
        }
    }
}
