using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class SplitSRNSummaryExtractContacts : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
			CREATE VIEW [dbo].[vwSRNComplianceContacts]
			AS
				SELECT 
					srn.[SRNNumber] AS SRNNumber
					,srn.[MemberId] AS MemberId
					,cc.[FirstName] AS ComplianceContactFirstName
					,cc.[Surname] AS ComplianceContactSurname
					,cc.[Email] AS ComplianceContactEmail
					,cc.[JobTitle] AS ComplianceContactJobTitle
					,cc.[CellNumber] AS ComplianceContactCellNumber
					,cc.[OfficeTelNumber] AS ComplianceContactOfficeNumber
				FROM SRNContacts cc

				INNER JOIN SRNs srn
				ON srn.[Id] = cc.[SRNId]
				WHERE cc.[ContactTypeId] = 4
			GO");

            migrationBuilder.Sql(@"
			CREATE VIEW [dbo].[vwSRNDataContacts]
			AS
				SELECT 
					srn.[SRNNumber] AS SRNNumber
					,srn.[MemberId] AS MemberId
					,dc.[FirstName] AS DataContactFirstName
					,dc.[Surname] AS DataContactSurname
					,dc.[Email] AS DataContactEmail
					,dc.[JobTitle] AS DataContactJobTitle
					,dc.[CellNumber] AS DataContactCellNumber
					,dc.[OfficeTelNumber] AS DataContactOfficeNumber
				FROM SRNContacts dc

				INNER JOIN SRNs srn
				ON srn.[Id] = dc.[SRNId]
				WHERE dc.[ContactTypeId] = 5
			GO");

            migrationBuilder.Sql(@"
			CREATE VIEW [dbo].[vwSRNManualAmendmentsContacts]
			AS
				SELECT 
					srn.[SRNNumber] AS SRNNumber
					,srn.[MemberId] AS MemberId
					,ma.[FirstName] AS ManualAmendmentsContactFirstName
					,ma.[Surname] AS ManualAmendmentsContactSurname
					,ma.[Email] AS ManualAmendmentsContactEmail
					,ma.[JobTitle] AS ManualAmendmentsContactJobTitle
					,ma.[CellNumber] AS ManualAmendmentsContactCellNumber
					,ma.[OfficeTelNumber] AS ManualAmendmentsContactOfficeNumber
				FROM SRNContacts ma

				INNER JOIN SRNs srn
				ON srn.[Id] = ma.[SRNId]
				WHERE ma.[ContactTypeId] = 6
			GO");

            migrationBuilder.Sql(@"
			CREATE VIEW [dbo].[vwSRNDTHContacts]
			AS
				SELECT 
					srn.[SRNNumber] AS SRNNumber
					,srn.[MemberId] AS MemberId
					,dth.[FirstName] AS DTHContactFirstName
					,dth.[Surname] AS DTHContactSurname
					,dth.[Email] AS DTHContactEmail
					,dth.[JobTitle] AS DTHContactJobTitle
					,dth.[CellNumber] AS DTHContactCellNumber
					,dth.[OfficeTelNumber] AS DTHContactOfficeNumber
				FROM SRNContacts dth

				INNER JOIN SRNs srn
				ON srn.[Id] = dth.[SRNId]
				WHERE dth.[ContactTypeId] = 7
			GO");

            migrationBuilder.Sql(@"
			ALTER VIEW [dbo].[vwSRNSummaryExtract] 
			AS
				WITH branchlocations
				AS
				(
					SELECT 
						[SRNId]
						,[BranchLocations] = STRING_AGG(bra.[Name],',')
					FROM [dbo].[BranchLocations] bra
					GROUP BY [SRNId]
				)
				SELECT
					[CompanyRegistrationNumber] = ISNULL(mem.[RegisteredNumber], mem.[IdNumber])
					,[MemberName] = mem.[RegisteredName]
					,[SRN] = srn.[SRNNumber]
					,[MemberId] = srn.[MemberId]
					,[SPNumber] = spg.[SPNumber]
					,[SRNDisplayName] = srn.[TradingName]
					,[CreditInformationClassification] = cla.[Name]
					,[PortfolioManager] = shm.[FirstName] + ' ' + shm.[LastName]
					,[AccountType] = acc.[Name]
					,[NCRReportingAccountTypeClassification] = ncr.[Name]
					,[BillingCycleDay] = srn.[BillingCycleDay]
					,[SRNALGLeader] = alg.[RegisteredName]
					,[LoanManagementSystemVendor] = loa.[Name]
					,[BranchLocations] = bra.[BranchLocations]
					,[NumberMonthlyRecords] = 1
					,[SRNCreationDate] = srn.[CreationDate]
					,[Status] = srnStatuses.[Name]
					,[StatusDate] = srnSH.[StatusDate]
					,[StatusReason] = srnStatusReasons.[Name]
					,[StatusComment] = srnSH.[StatusComment]
					,[LastSubmissionDate] = srnSH.[LastSubmissionDate]
					,[BureauClosureDate] = srnSH.[BureauClosureDate]
					,[BureauClosureInstruction] = srnSH.[BureauClosureInstruction]
					,[ThirdPartyVendor] = sv.[Name]
				FROM [dbo].[SRNs] srn


				INNER JOIN [dbo].[Members] mem
				ON srn.[MemberId] = mem.[Id]

				INNER JOIN [dbo].[Users] shm
				ON mem.[StakeholderManagerId] = shm.[Id]

				INNER JOIN [dbo].[AccountTypes] acc
				ON srn.[AccountTypeId] = acc.[Id]

				INNER JOIN [dbo].[NCRReportingAccountTypeClassifications] ncr
				ON srn.[NCRReportingAccountTypeClassificationId] = ncr.[Id]
												
				LEFT JOIN [dbo].[SRNStatusHistory] srnSH
				ON srnSH.[SRNId] = srn.[Id]

				LEFT OUTER JOIN [dbo].[SRNStatuses] srnStatuses
				ON srnStatuses.[Id] = srnSH.[StatusId]

				LEFT JOIN [dbo].[SRNStatusReasons] srnStatusReasons
				ON srnStatusReasons.[Id] = srnSH.[StatusReasonId]

				LEFT OUTER JOIN [dbo].[CreditInformationClassifications] cla
				ON srn.[CreditInformationClassificationId] = cla.[Id]

				LEFT OUTER JOIN [dbo].[SPGroups] spg
				ON srn.[SPGroupId] = spg.[Id]

				LEFT OUTER JOIN [dbo].[Members] alg
				ON srn.[ALGLeaderId] = alg.[Id]

				LEFT OUTER JOIN [dbo].[LoanManagementSystemVendors] loa
				ON srn.[LoanManagementSystemVendorId] = loa.[Id]

				LEFT OUTER JOIN branchlocations bra
				ON srn.[Id] = bra.[SRNId]

				LEFT OUTER JOIN SoftwareVendors sv
				ON srn.[SoftwareVendorId] = sv.[Id]
			GO");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNComplianceContacts];");
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNDataContacts];");
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNManualAmendmentsContacts];");
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNDTHContacts];");
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNSummaryExtract];");
        }
    }
}
