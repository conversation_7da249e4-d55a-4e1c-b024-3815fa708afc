using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class ChangedNcrClassificationIdAndPrimaryBureauIdToNullables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "NcrClassificationId",
                table: "Members",
                nullable: true,
                oldClrType: typeof(int));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "NcrClassificationId",
                table: "Members",
                nullable: false,
                oldClrType: typeof(int),
                oldNullable: true);
        }
    }
}
