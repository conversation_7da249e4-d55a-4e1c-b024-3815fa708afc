using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedUniqueKeysForMemberIdAndSPNumber : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SPGroups_MemberId",
                table: "SPGroups");

            migrationBuilder.AlterColumn<string>(
                name: "SPNumber",
                table: "SPGroups",
                nullable: false,
                oldClrType: typeof(string));

            migrationBuilder.CreateIndex(
                name: "IX_SPGroups_MemberId_SPNumber",
                table: "SPGroups",
                columns: new[] { "MemberId", "SPNumber" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SPGroups_MemberId_SPNumber",
                table: "SPGroups");

            migrationBuilder.AlterColumn<string>(
                name: "<PERSON>N<PERSON><PERSON>",
                table: "SPGroups",
                nullable: false,
                oldClrType: typeof(string));

            migrationBuilder.CreateIndex(
                name: "IX_SPGroups_MemberId",
                table: "SPGroups",
                column: "MemberId");
        }
    }
}
