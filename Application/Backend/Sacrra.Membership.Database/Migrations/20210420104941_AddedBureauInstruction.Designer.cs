// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Sacrra.Membership.Database;

namespace Sacrra.Membership.Database.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20210420104941_AddedBureauInstruction")]
    partial class AddedBureauInstruction
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "2.2.6-servicing-10079")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALG", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("ALGs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGClientLeader", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("ClientId");

                    b.Property<DateTime>("DateCreated");

                    b.Property<int>("LeaderId");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("LeaderId");

                    b.ToTable("ALGClientLeaders");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGMemberDetails", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("LoanManagementSystemName");

                    b.Property<int>("MemberId");

                    b.Property<int>("NumberOfClients");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.ToTable("ALGMemberDetails");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AccountType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("AccountTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ApiError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("Date");

                    b.Property<string>("Input");

                    b.Property<string>("Message");

                    b.Property<string>("Method");

                    b.Property<string>("Path");

                    b.Property<string>("StackTrace");

                    b.Property<int>("StatusCode");

                    b.Property<string>("UserId");

                    b.HasKey("Id");

                    b.ToTable("ApiErrors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BranchLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.Property<int>("SRNId");

                    b.HasKey("Id");

                    b.HasIndex("SRNId");

                    b.ToTable("BranchLocations");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Bureau", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Email");

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("Bureaus");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.CamundaError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("Date");

                    b.Property<string>("Input");

                    b.Property<string>("Message");

                    b.Property<string>("Method");

                    b.Property<string>("Path");

                    b.Property<string>("StackTrace");

                    b.Property<int>("StatusCode");

                    b.Property<string>("UserId");

                    b.HasKey("Id");

                    b.ToTable("CamundaErrors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ChangeRequestStaging", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("DateCreated");

                    b.Property<int>("ObjectId");

                    b.Property<string>("OldDetailsBlob");

                    b.Property<string>("ReviewComments");

                    b.Property<string>("StagingDetailsBlob");

                    b.Property<int>("Status");

                    b.Property<int>("Type");

                    b.Property<string>("UpdatedDetailsBlob");

                    b.Property<int>("UserId");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("MemberChangeRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ContactType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("ApplicableTo");

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("ContactTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.DWException", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CamundaTaskId");

                    b.Property<string>("Comments");

                    b.Property<DateTime>("DatePulled");

                    b.Property<string>("Exception");

                    b.Property<string>("ExceptionCategory");

                    b.Property<DateTime>("ExceptionDateTime");

                    b.Property<string>("ExceptionDesc");

                    b.Property<string>("ExceptionStatus");

                    b.Property<long>("FctWarehouseExceptionID");

                    b.Property<string>("IsSentToPortal");

                    b.Property<string>("SHM");

                    b.Property<string>("SRNNumber");

                    b.HasKey("Id");

                    b.ToTable("DWExceptions");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EntityType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("EntityTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EventLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ChangeBlob");

                    b.Property<string>("ChangeType");

                    b.Property<DateTime>("Date");

                    b.Property<string>("EntityBlob");

                    b.Property<int?>("EntityId");

                    b.Property<string>("EntityName");

                    b.Property<int>("EntityTypeId");

                    b.Property<string>("User");

                    b.HasKey("Id");

                    b.HasIndex("EntityTypeId");

                    b.ToTable("EventLogs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.HangfireJob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("CreatedById");

                    b.Property<DateTime>("DateCreated");

                    b.Property<int?>("Day");

                    b.Property<int>("DayOfWeek");

                    b.Property<string>("Description");

                    b.Property<int>("Frequency");

                    b.Property<int?>("Hour");

                    b.Property<int?>("Interval");

                    b.Property<int?>("Minute");

                    b.Property<int?>("Month");

                    b.Property<string>("Name")
                        .IsRequired();

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.ToTable("HangfireJobs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.LoanManagementSystemVendor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("LoanManagementSystemVendors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Member", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AnalyticsCompanyName");

                    b.Property<long?>("AnnualTurnover");

                    b.Property<int>("ApplicationStatusId");

                    b.Property<int?>("ChangeRequestId");

                    b.Property<string>("Comments");

                    b.Property<string>("DisqualificationReason");

                    b.Property<string>("HeadOfficePhysicalAddress")
                        .IsRequired();

                    b.Property<string>("HeadOfficePostalAddress")
                        .IsRequired();

                    b.Property<string>("IdNumber");

                    b.Property<int?>("IndustryClassificationId");

                    b.Property<bool>("IsNcrRegistrant");

                    b.Property<bool>("IsSoleProp");

                    b.Property<bool>("IsVatRegistrant");

                    b.Property<int?>("MemberStatusReasonId");

                    b.Property<int>("MembershipTypeId");

                    b.Property<string>("NcrCategory");

                    b.Property<int?>("NcrReportingPrimaryBusinessClassificationId");

                    b.Property<string>("NcrcpNumber");

                    b.Property<int?>("PrimaryBureauId");

                    b.Property<int?>("PrincipleDebtRangeId");

                    b.Property<string>("RegisteredName")
                        .IsRequired();

                    b.Property<string>("RegisteredNumber");

                    b.Property<int?>("SecondaryBureauId");

                    b.Property<int?>("StakeholderManagerId");

                    b.Property<string>("StatusComment");

                    b.Property<string>("VatNumber");

                    b.Property<string>("Website");

                    b.HasKey("Id");

                    b.HasIndex("ChangeRequestId");

                    b.HasIndex("IdNumber")
                        .IsUnique.HasFilter("[IdNumber] IS NOT NULL");

                    b.HasIndex("MemberStatusReasonId");

                    b.HasIndex("PrimaryBureauId")
                        .IsUnique.HasFilter("[PrimaryBureauId] IS NOT NULL");

                    b.HasIndex("RegisteredNumber")
                        .IsUnique.HasFilter("[RegisteredNumber] IS NOT NULL");

                    b.HasIndex("SecondaryBureauId");

                    b.HasIndex("StakeholderManagerId");

                    b.ToTable("Members");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CellNumber");

                    b.Property<int>("ContactTypeId");

                    b.Property<string>("Email");

                    b.Property<string>("FirstName");

                    b.Property<string>("JobTitle");

                    b.Property<int>("MemberId");

                    b.Property<string>("OfficeTelNumber");

                    b.Property<string>("Surname");

                    b.HasKey("Id");

                    b.HasIndex("ContactTypeId");

                    b.HasIndex("MemberId");

                    b.ToTable("MemberContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberDocument", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("IDDocumentBlob");

                    b.Property<int>("MemberId");

                    b.Property<string>("NcrCertificateBlob");

                    b.HasKey("Id");

                    b.HasIndex("MemberId")
                        .IsUnique();

                    b.ToTable("MemberDocuments");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberStatusReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("MemberStatusReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberUsers", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("DateCreated");

                    b.Property<int>("MemberId");

                    b.Property<int>("UserId");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.HasIndex("UserId");

                    b.ToTable("MemberUsers");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.NCRReportingAccountTypeClassification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("NCRReportingAccountTypeClassifications");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.PartialMember", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AnalyticsCompanyName");

                    b.Property<int>("AnnualTurnover");

                    b.Property<int>("ApplicationStatusId");

                    b.Property<string>("Comments");

                    b.Property<string>("DisqualificationReason");

                    b.Property<string>("HeadOfficePhysicalAddress");

                    b.Property<string>("HeadOfficePostalAddress");

                    b.Property<string>("IDDocumentBlob");

                    b.Property<string>("IdNumber");

                    b.Property<int>("IndustryClassificationId");

                    b.Property<bool>("IsComplete");

                    b.Property<bool>("IsNcrRegistrant");

                    b.Property<bool>("IsSoleProp");

                    b.Property<bool>("IsVatRegistrant");

                    b.Property<int>("MembershipTypeId");

                    b.Property<string>("NcrCategory");

                    b.Property<string>("NcrCertificateBlob");

                    b.Property<int>("NcrReportingPrimaryBusinessClassificationId");

                    b.Property<int>("NcrcpNumber");

                    b.Property<int>("PrimaryBureauId");

                    b.Property<int>("PrincipleDebtRangeId");

                    b.Property<string>("RegisteredName");

                    b.Property<string>("RegisteredNumber");

                    b.Property<int>("SecondaryBureauId");

                    b.Property<string>("StatusComment");

                    b.Property<int>("UserId");

                    b.Property<string>("VatNumber");

                    b.Property<string>("Website");

                    b.HasKey("Id");

                    b.ToTable("PartialMembers");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.RolloutStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("RolloutStatuses");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SPGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("DateCreated");

                    b.Property<int>("MemberId");

                    b.Property<string>("SPNumber")
                        .IsRequired();

                    b.HasKey("Id");

                    b.HasIndex("MemberId", "SPNumber")
                        .IsUnique();

                    b.ToTable("SPGroups");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRN", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("ALGLeaderId");

                    b.Property<int>("AccountTypeId");

                    b.Property<int>("BillingCycleDay");

                    b.Property<string>("BureauInstruction");

                    b.Property<int?>("ChangeRequestId");

                    b.Property<int>("FileType");

                    b.Property<string>("FirstReviewRejectReason");

                    b.Property<DateTime?>("LastSubmissionDate");

                    b.Property<int?>("LoanManagementSystemVendorId");

                    b.Property<int>("MemberId");

                    b.Property<int>("NCRReportingAccountTypeClassificationId");

                    b.Property<long>("NumberOfActiveAccounts");

                    b.Property<int?>("SPGroupId");

                    b.Property<string>("SRNNumber");

                    b.Property<int>("SRNStatusId");

                    b.Property<int?>("SRNStatusReasonId");

                    b.Property<string>("SecondReviewRejectReason");

                    b.Property<int?>("SoftwareVendorId");

                    b.Property<DateTime?>("StatusDate");

                    b.Property<DateTime>("StatusLastUpdatedAt");

                    b.Property<string>("TradingName")
                        .IsRequired();

                    b.HasKey("Id");

                    b.HasIndex("ALGLeaderId");

                    b.HasIndex("AccountTypeId");

                    b.HasIndex("ChangeRequestId");

                    b.HasIndex("LoanManagementSystemVendorId");

                    b.HasIndex("MemberId");

                    b.HasIndex("NCRReportingAccountTypeClassificationId");

                    b.HasIndex("SPGroupId");

                    b.HasIndex("SRNNumber")
                        .IsUnique.HasFilter("[SRNNumber] IS NOT NULL");

                    b.HasIndex("SRNStatusId");

                    b.HasIndex("SRNStatusReasonId");

                    b.HasIndex("SoftwareVendorId");

                    b.ToTable("SRNs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CellNumber");

                    b.Property<int>("ContactTypeId");

                    b.Property<string>("Email");

                    b.Property<string>("FirstName");

                    b.Property<string>("JobTitle");

                    b.Property<string>("OfficeTelNumber");

                    b.Property<int>("SRNId");

                    b.Property<string>("Surname");

                    b.HasKey("Id");

                    b.HasIndex("ContactTypeId");

                    b.HasIndex("SRNId");

                    b.ToTable("SRNContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNFieldUpdateSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("FieldName");

                    b.Property<bool>("IsApprovalRequired");

                    b.Property<bool>("IsUpdatable");

                    b.Property<string>("ObjectName");

                    b.Property<int>("ToBeApprovedBy");

                    b.HasKey("Id");

                    b.ToTable("SRNFieldUpdateSettings");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNMergeRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate");

                    b.Property<DateTime?>("DailyFileGoLiveDate");

                    b.Property<DateTime?>("DailyFileTestEndDate");

                    b.Property<DateTime?>("DailyFileTestStartDate");

                    b.Property<int>("FromSRNId");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate");

                    b.Property<DateTime?>("MonthlyFileTestEndDate");

                    b.Property<DateTime?>("MonthlyFileTestStartDate");

                    b.Property<DateTime>("RequestDate");

                    b.Property<int>("Status");

                    b.Property<int>("ToSRNId");

                    b.HasKey("Id");

                    b.HasIndex("FromSRNId");

                    b.HasIndex("ToSRNId");

                    b.ToTable("SRNMergeRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSaleRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("BuyerMemberId");

                    b.Property<int?>("BuyerSRNId");

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate");

                    b.Property<DateTime?>("DailyFileGoLiveDate");

                    b.Property<DateTime?>("DailyFileTestEndDate");

                    b.Property<DateTime?>("DailyFileTestStartDate");

                    b.Property<DateTime?>("MigrationDate");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate");

                    b.Property<DateTime?>("MonthlyFileTestEndDate");

                    b.Property<DateTime?>("MonthlyFileTestStartDate");

                    b.Property<DateTime>("RequestDate");

                    b.Property<string>("ReviewComments");

                    b.Property<int?>("SPGroupId");

                    b.Property<int>("SRNId");

                    b.Property<int>("SellerMemberId");

                    b.Property<int>("Status");

                    b.Property<int>("Type");

                    b.HasKey("Id");

                    b.HasIndex("BuyerMemberId");

                    b.HasIndex("BuyerSRNId");

                    b.HasIndex("SPGroupId");

                    b.HasIndex("SRNId");

                    b.HasIndex("SellerMemberId");

                    b.ToTable("SRNSaleRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Exclusions");

                    b.Property<TimeSpan>("HangfireExcecutionTime");

                    b.Property<int>("Increment");

                    b.Property<DateTime>("LastGeneratedAt");

                    b.Property<long>("LastGeneratedNumber");

                    b.Property<long>("MaxGeneratedNumberAllowed");

                    b.Property<int>("NoOfDaysBeforeCreatingLiveConfirmationTask");

                    b.Property<int>("NoOfDaysBeforeCreatingTestingConfirmationTask");

                    b.Property<int>("NoOfDigitsAllowed");

                    b.Property<string>("Prefix")
                        .IsRequired.HasColumnType("VARCHAR(2)");

                    b.HasKey("Id");

                    b.ToTable("SRNSettings");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSplitRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate");

                    b.Property<DateTime?>("DailyFileGoLiveDate");

                    b.Property<DateTime?>("DailyFileTestEndDate");

                    b.Property<DateTime?>("DailyFileTestStartDate");

                    b.Property<int>("FromSRNId");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate");

                    b.Property<DateTime?>("MonthlyFileTestEndDate");

                    b.Property<DateTime?>("MonthlyFileTestStartDate");

                    b.Property<DateTime>("RequestDate");

                    b.Property<int>("Status");

                    b.Property<int>("ToSRNId");

                    b.Property<int>("Type");

                    b.HasKey("Id");

                    b.HasIndex("FromSRNId");

                    b.HasIndex("ToSRNId");

                    b.ToTable("SRNSplitRequests");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Code");

                    b.Property<bool>("IsActive");

                    b.Property<bool>("IsActivityAllowedWhileInProcess");

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("SRNStatuses");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("SRNStatusReasons");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusReasonSRNStatusLink", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("SRNStatusId");

                    b.Property<int>("SRNStatusReasonId");

                    b.HasKey("Id");

                    b.HasIndex("SRNStatusId");

                    b.HasIndex("SRNStatusReasonId");

                    b.ToTable("SRNStatusReasonSRNStatusLink");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusUpdateHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("BureauInstruction");

                    b.Property<string>("Comments");

                    b.Property<DateTime?>("DailyFileDevelopmentEndDate");

                    b.Property<DateTime?>("DailyFileDevelopmentStartDate");

                    b.Property<DateTime?>("DailyFileGoLiveDate");

                    b.Property<DateTime?>("DailyFileTestEndDate");

                    b.Property<DateTime?>("DailyFileTestStartDate");

                    b.Property<DateTime?>("DateCompleted");

                    b.Property<DateTime>("DateCreated");

                    b.Property<int>("FileType");

                    b.Property<bool>("IsComple");

                    b.Property<bool>("IsDailyFileLive");

                    b.Property<bool>("IsLiveFileSubmissionsSuspended");

                    b.Property<bool>("IsMonthlyFileLive");

                    b.Property<string>("LastSubmissionDate");

                    b.Property<DateTime?>("MonthlyFileDevelopmentEndDate");

                    b.Property<DateTime?>("MonthlyFileDevelopmentStartDate");

                    b.Property<DateTime?>("MonthlyFileGoLiveDate");

                    b.Property<DateTime?>("MonthlyFileTestEndDate");

                    b.Property<DateTime?>("MonthlyFileTestStartDate");

                    b.Property<string>("ProcessInstanceId");

                    b.Property<int?>("RolloutStatusId");

                    b.Property<int>("SRNId");

                    b.Property<int>("SRNStatusId");

                    b.Property<int?>("SRNStatusReasonId");

                    b.Property<string>("UpdateNumber")
                        .HasColumnType("VARCHAR(200)");

                    b.Property<int?>("UpdateType");

                    b.HasKey("Id");

                    b.HasIndex("RolloutStatusId");

                    b.HasIndex("SRNId");

                    b.HasIndex("SRNStatusId");

                    b.HasIndex("SRNStatusReasonId");

                    b.ToTable("SRNStatusUpdateHistory");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SoftwareVendor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("SoftwareVendors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.TradingName", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("MemberId");

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.ToTable("TradingNames");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Auth0Id");

                    b.Property<string>("Email")
                        .IsRequired();

                    b.Property<byte[]>("EmailConfirmationHash");

                    b.Property<byte[]>("EmailConfirmationSalt");

                    b.Property<string>("FirstName")
                        .IsRequired();

                    b.Property<bool>("IsEmailConfirmed");

                    b.Property<string>("LastName")
                        .IsRequired();

                    b.Property<byte[]>("PasswordHash");

                    b.Property<byte[]>("PasswordSalt");

                    b.Property<bool>("RequirePasswordChange");

                    b.Property<int>("RoleId");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGClientLeader", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Client")
                        .WithMany.HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Leader")
                        .WithMany.HasForeignKey("LeaderId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALGMemberDetails", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany.HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.BranchLocation", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany("BranchLocations")
                        .HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ChangeRequestStaging", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.User", "User")
                        .WithMany("MemberChangeRequests")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.EventLog", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.EntityType", "EntityType")
                        .WithMany.HasForeignKey("EntityTypeId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.HangfireJob", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.User", "CreatedBy")
                        .WithMany.HasForeignKey("CreatedById");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Member", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ChangeRequestStaging", "ChangeRequest")
                        .WithMany.HasForeignKey("ChangeRequestId");

                    b.HasOne("Sacrra.Membership.Database.Models.MemberStatusReason", "MemberStatusReason")
                        .WithMany.HasForeignKey("MemberStatusReasonId");

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "PrimaryBureau")
                        .WithOne.HasForeignKey("Sacrra.Membership.Database.Models.Member", "PrimaryBureauId");

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "SecondaryBureau")
                        .WithMany.HasForeignKey("SecondaryBureauId");

                    b.HasOne("Sacrra.Membership.Database.Models.User", "StakeholderManager")
                        .WithMany("MembersIManage")
                        .HasForeignKey("StakeholderManagerId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberContact", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ContactType", "ContactType")
                        .WithMany.HasForeignKey("ContactTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("Contacts")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberDocument", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithOne("MemberDocument")
                        .HasForeignKey("Sacrra.Membership.Database.Models.MemberDocument", "MemberId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberUsers", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("Users")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.User", "User")
                        .WithMany("Members")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SPGroup", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("SPGroups")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRN", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "ALGLeader")
                        .WithMany("ClientSRNs")
                        .HasForeignKey("ALGLeaderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.AccountType", "AccountType")
                        .WithMany.HasForeignKey("AccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.ChangeRequestStaging", "ChangeRequest")
                        .WithMany.HasForeignKey("ChangeRequestId");

                    b.HasOne("Sacrra.Membership.Database.Models.LoanManagementSystemVendor", "LoanManagementSystemVendor")
                        .WithMany.HasForeignKey("LoanManagementSystemVendorId");

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("SRNs")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.NCRReportingAccountTypeClassification", "NCRReportingAccountTypeClassification")
                        .WithMany.HasForeignKey("NCRReportingAccountTypeClassificationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.SPGroup", "SPGroup")
                        .WithMany("SRNs")
                        .HasForeignKey("SPGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatus", "SRNStatus")
                        .WithMany.HasForeignKey("SRNStatusId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatusReason", "SRNStatusReason")
                        .WithMany.HasForeignKey("SRNStatusReasonId");

                    b.HasOne("Sacrra.Membership.Database.Models.SoftwareVendor", "SoftwareVendor")
                        .WithMany.HasForeignKey("SoftwareVendorId");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNContact", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ContactType", "ContactType")
                        .WithMany.HasForeignKey("ContactTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany("Contacts")
                        .HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNMergeRequest", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "FromSRN")
                        .WithMany.HasForeignKey("FromSRNId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "ToSRN")
                        .WithMany.HasForeignKey("ToSRNId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSaleRequest", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "BuyerMember")
                        .WithMany.HasForeignKey("BuyerMemberId");

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "BuyerSRN")
                        .WithMany.HasForeignKey("BuyerSRNId");

                    b.HasOne("Sacrra.Membership.Database.Models.SPGroup", "SPGroup")
                        .WithMany.HasForeignKey("SPGroupId");

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany.HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "SellerMember")
                        .WithMany.HasForeignKey("SellerMemberId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNSplitRequest", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "FromSRN")
                        .WithMany.HasForeignKey("FromSRNId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "ToSRN")
                        .WithMany.HasForeignKey("ToSRNId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusReasonSRNStatusLink", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatus", "SRNStatus")
                        .WithMany("SRNStatusReasons")
                        .HasForeignKey("SRNStatusId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatusReason", "SRNStatusReason")
                        .WithMany("SRNStatuses")
                        .HasForeignKey("SRNStatusReasonId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRNStatusUpdateHistory", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.RolloutStatus", "RolloutStatus")
                        .WithMany.HasForeignKey("RolloutStatusId");

                    b.HasOne("Sacrra.Membership.Database.Models.SRN", "SRN")
                        .WithMany("SRNStatusUpdates")
                        .HasForeignKey("SRNId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatus", "SRNStatus")
                        .WithMany.HasForeignKey("SRNStatusId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.SRNStatusReason", "SRNStatusReason")
                        .WithMany.HasForeignKey("SRNStatusReasonId");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.TradingName", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("TradingNames")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);
                });
#pragma warning restore 612, 618
        }
    }
}
