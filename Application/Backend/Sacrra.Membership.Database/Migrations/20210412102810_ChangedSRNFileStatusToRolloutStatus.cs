using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class ChangedSRNFileStatusToRolloutStatus : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNStatusUpdateHistory_SRNFileStatuses_SRNFileStatusId",
                table: "SRNStatusUpdateHistory");

            migrationBuilder.DropTable(
                name: "SRNFileStatuses");

            migrationBuilder.RenameColumn(
                name: "SRNFileStatusId",
                table: "SRNStatusUpdateHistory",
                newName: "RolloutStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_SRNStatusUpdateHistory_SRNFileStatusId",
                table: "SRNStatusUpdateHistory",
                newName: "IX_SRNStatusUpdateHistory_RolloutStatusId");

            migrationBuilder.CreateTable(
                name: "RolloutStatuses",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RolloutStatuses", x => x.Id);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_SRNStatusUpdateHistory_RolloutStatuses_RolloutStatusId",
                table: "SRNStatusUpdateHistory",
                column: "RolloutStatusId",
                principalTable: "RolloutStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNStatusUpdateHistory_RolloutStatuses_RolloutStatusId",
                table: "SRNStatusUpdateHistory");

            migrationBuilder.DropTable(
                name: "RolloutStatuses");

            migrationBuilder.RenameColumn(
                name: "RolloutStatusId",
                table: "SRNStatusUpdateHistory",
                newName: "SRNFileStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_SRNStatusUpdateHistory_RolloutStatusId",
                table: "SRNStatusUpdateHistory",
                newName: "IX_SRNStatusUpdateHistory_SRNFileStatusId");

            migrationBuilder.CreateTable(
                name: "SRNFileStatuses",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SRNFileStatuses", x => x.Id);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_SRNStatusUpdateHistory_SRNFileStatuses_SRNFileStatusId",
                table: "SRNStatusUpdateHistory",
                column: "SRNFileStatusId",
                principalTable: "SRNFileStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
