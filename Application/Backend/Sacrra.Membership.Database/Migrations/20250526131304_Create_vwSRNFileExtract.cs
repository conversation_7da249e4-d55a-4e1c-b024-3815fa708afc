using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class Create_vwSRNFileExtract : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
            IF EXISTS
			(
				SELECT TOP (1) 1 FROM sys.all_objects
				WHERE [name] = 'vwSRNFileExtract'
			)
			BEGIN
				DROP VIEW [dbo].[vwSRNFileExtract]
			END;

			GO
            CREATE VIEW [dbo].[vwSRNFileExtract] 
			AS
			WITH history
			AS
			(
				SELECT
					[Id]
					,[MaxId] = MAX([Id]) OVER (PARTITION BY [SRNId], [FileType])
					,[DailyFileTestEndDate]
					,[DailyFileGoLiveDate]
					,[MonthlyFileTestEndDate]
					,[MonthlyFileGoLiveDate]
					,[BureauInstruction]
					,[LastSubmissionDate]
					,[DateCreated]
					,[SRNId]
					,[IsComple]
					,[DateCompleted]
					,[FileType]
					,[SRNStatusId]
					,[SRNStatusReasonId]
					,[SignoffDate]
					,[SRNFileTestingStatusReason]
				FROM [dbo].[SRNStatusUpdateHistory]
			)
			SELECT
				srn.[SRNNumber]
				,[SRNDisplayName] = srn.[TradingName]
				,files.[Id]
				,[CurrentSRNStatus] = sta.[Name]
				,[SRNCreationDate] = srn.[CreationDate]
				,[SRNFileType] = CASE srn.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' WHEN 3 THEN 'Daily & Monthly' END 
				,files.[IsLatestRecord]
				,files.[FileType]
				,files.[FileStatus]
				,files.[FileStatusDate]
				,files.[TestFileStatusReason]
				,files.[PlannedTestEndDate]
				,files.[FileTestSignoffDate]
				,files.[PlannedGoLiveDate]
				,files.[ActualGoLiveDate]
				,files.[TestingSkipped]
			FROM
			(
			SELECT
					tst.[Id]
					,[SRNId]
				,[FileType] = CASE tst.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' END
				,[FileStatus] = 'Test'
				,[IsLatestRecord] = CASE WHEN tst.[MaxId] = tst.[Id] AND [IsComple] = 0 THEN 'Yes' ELSE 'No' END
				,[FileStatusDate] = tst.[DateCreated]
				,[TestFileStatusReason] = rea.[Name]
				,[PlannedTestEndDate] = CASE tst.[FileType] WHEN 1 THEN tst.[DailyFileTestEndDate] WHEN 2 THEN tst.[MonthlyFileTestEndDate] END
				,[FileTestSignoffDate] = tst.[SignoffDate]
				,[PlannedGoLiveDate] = CASE tst.[FileType] WHEN 1 THEN tst.[DailyFileGoLiveDate] WHEN 2 THEN tst.[MonthlyFileGoLiveDate] END
				,[ActualGoLiveDate] = tst.[DateCompleted]
				,[TestingSkipped] = CASE WHEN FORMAT(tst.[DateCreated],'yyyy-MM-dd HH:mm:ss') > ISNULL(FORMAT(tst.[DateCompleted],'yyyy-MM-dd HH:mm:ss'),'2000-01-01') THEN 'Yes' ELSE 'No' END
			FROM history tst
			LEFT OUTER JOIN [dbo].[SRNStatusReasons] rea
			ON tst.[SRNStatusReasonId] = rea.[Id]
			WHERE tst.[FileType] IN (1,2)
			UNION ALL
			SELECT
					liv.[Id]
					,[SRNId]
				,[FileType] = CASE liv.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' END
				,[FileStatus] = 'Live'
				,[IsLatestRecord] = CASE WHEN liv.[MaxId] = liv.[Id] AND [IsComple] = 1 THEN 'Yes' ELSE 'No' END
				,[FileStatusDate] = liv.[DateCompleted]
				,[TestFileStatusReason] = NULL
				,[PlannedTestEndDate] = CASE liv.[FileType] WHEN 1 THEN liv.[DailyFileTestEndDate] WHEN 2 THEN liv.[MonthlyFileTestEndDate] END
				,[FileTestSignoffDate] = liv.[SignoffDate]
				,[PlannedGoLiveDate] = CASE liv.[FileType] WHEN 1 THEN liv.[DailyFileGoLiveDate] WHEN 2 THEN liv.[MonthlyFileGoLiveDate] END
				,[ActualGoLiveDate] = liv.[DateCompleted]
				,[TestingSkipped] = CASE WHEN FORMAT(liv.[DateCreated],'yyyy-MM-dd HH:mm:ss') > ISNULL(FORMAT(liv.[DateCompleted],'yyyy-MM-dd HH:mm:ss'),'2000-01-01') THEN 'Yes' ELSE 'No' END
			FROM history liv
			LEFT OUTER JOIN [dbo].[SRNStatusReasons] rea
			ON liv.[SRNStatusReasonId] = rea.[Id]
			WHERE liv.[IsComple] = 1
			AND liv.[FileType] IN (1,2)
			) files
			INNER JOIN [dbo].[SRNs] srn
			ON files.[SRNId] = srn.[Id]
			INNER JOIN [dbo].[SRNStatuses] sta
			ON srn.[SRNStatusId] = sta.[Id]
			GO
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNFileExtract];");
        }
    }
}
