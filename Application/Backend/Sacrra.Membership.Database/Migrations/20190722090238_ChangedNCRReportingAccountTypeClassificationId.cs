using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class ChangedNCRReportingAccountTypeClassificationId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_NCRReportingAccountTypeClassifications_NCRReportingAccountTypeClassificationId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "NCRReportingAccountTypeClassificationId",
                table: "SRNs",
                nullable: false,
                oldClrType: typeof(int),
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_NCRReportingAccountTypeClassifications_NCRReportingAccountTypeClassificationId",
                table: "SRNs",
                column: "NCRReportingAccountTypeClassificationId",
                principalTable: "NCRReportingAccountTypeClassifications",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeign<PERSON>ey(
                name: "FK_SRNs_NCRReportingAccountTypeClassifications_NCRReportingAccountTypeClassificationId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "NCRReportingAccountTypeClassificationId",
                table: "SRNs",
                nullable: true,
                oldClrType: typeof(int));

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_NCRReportingAccountTypeClassifications_NCRReportingAccountTypeClassificationId",
                table: "SRNs",
                column: "NCRReportingAccountTypeClassificationId",
                principalTable: "NCRReportingAccountTypeClassifications",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
