using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedVatRegistrant : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsVatRegistrant",
                table: "PartialMembers",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsVatRegistrant",
                table: "Members",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsVatRegistrant",
                table: "PartialMembers");

            migrationBuilder.DropColumn(
                name: "IsVatRegistrant",
                table: "Members");
        }
    }
}
