using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class UpdatedSRN : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_ALG_ALGId",
                table: "SRNs");

            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendor_LoanManagementSystemVendorId",
                table: "SRNs");

            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SoftwareVendor_SoftwareVendorId",
                table: "SRNs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SoftwareVendor",
                table: "SoftwareVendor");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LoanManagementSystemVendor",
                table: "LoanManagementSystemVendor");

            migrationBuilder.DropPrimaryKey(
                name: "<PERSON><PERSON>_<PERSON><PERSON>",
                table: "ALG");

            migrationBuilder.RenameTable(
                name: "SoftwareVendor",
                newName: "SoftwareVendors");

            migrationBuilder.RenameTable(
                name: "LoanManagementSystemVendor",
                newName: "LoanManagementSystemVendors");

            migrationBuilder.RenameTable(
                name: "ALG",
                newName: "ALGs");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SoftwareVendors",
                table: "SoftwareVendors",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_LoanManagementSystemVendors",
                table: "LoanManagementSystemVendors",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_ALGs",
                table: "ALGs",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "AccountTypes",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountTypes", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNs_AccountTypeId",
                table: "SRNs",
                column: "AccountTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_ALGs_ALGId",
                table: "SRNs",
                column: "ALGId",
                principalTable: "ALGs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_AccountTypes_AccountTypeId",
                table: "SRNs",
                column: "AccountTypeId",
                principalTable: "AccountTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendors_LoanManagementSystemVendorId",
                table: "SRNs",
                column: "LoanManagementSystemVendorId",
                principalTable: "LoanManagementSystemVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SoftwareVendors_SoftwareVendorId",
                table: "SRNs",
                column: "SoftwareVendorId",
                principalTable: "SoftwareVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_ALGs_ALGId",
                table: "SRNs");

            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_AccountTypes_AccountTypeId",
                table: "SRNs");

            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendors_LoanManagementSystemVendorId",
                table: "SRNs");

            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SoftwareVendors_SoftwareVendorId",
                table: "SRNs");

            migrationBuilder.DropTable(
                name: "AccountTypes");

            migrationBuilder.DropIndex(
                name: "IX_SRNs_AccountTypeId",
                table: "SRNs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SoftwareVendors",
                table: "SoftwareVendors");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LoanManagementSystemVendors",
                table: "LoanManagementSystemVendors");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ALGs",
                table: "ALGs");

            migrationBuilder.RenameTable(
                name: "SoftwareVendors",
                newName: "SoftwareVendor");

            migrationBuilder.RenameTable(
                name: "LoanManagementSystemVendors",
                newName: "LoanManagementSystemVendor");

            migrationBuilder.RenameTable(
                name: "ALGs",
                newName: "ALG");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SoftwareVendor",
                table: "SoftwareVendor",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_LoanManagementSystemVendor",
                table: "LoanManagementSystemVendor",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_ALG",
                table: "ALG",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_ALG_ALGId",
                table: "SRNs",
                column: "ALGId",
                principalTable: "ALG",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_LoanManagementSystemVendor_LoanManagementSystemVendorId",
                table: "SRNs",
                column: "LoanManagementSystemVendorId",
                principalTable: "LoanManagementSystemVendor",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SoftwareVendor_SoftwareVendorId",
                table: "SRNs",
                column: "SoftwareVendorId",
                principalTable: "SoftwareVendor",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
