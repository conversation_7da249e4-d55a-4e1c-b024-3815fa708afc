using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AlterSRNFileExtractView : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
			ALTER VIEW [dbo].[vwSRNFileExtract] 
			AS
			WITH latest
			AS
			(
				SELECT
				[MaxId] = MAX([ID]) OVER (PARTITION BY [SRNId],[Filetype])
				,*
				FROM [dbo].[SRNStatusUpdateHistory]
				WHERE [FileType] IN (1,2)
			)
			SELECT 
				[SRNNumber] = srn.[SRNNumber]
				,[MemberId] = srn.[MemberId]
				,[ALGLeaderId] = srn.[ALGLeaderId]
				,[SRNDisplayName] = srn.[TradingName]
				,[Id] = his.[Id]
				,[CurrentSRNStatus] = sta.[Name]
				,[SRNCreationDate] = srn.[CreationDate]
				,[SRNFileType] = CASE srn.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' WHEN 3 THEN 'Daily & Monthly' END
				,[IsLatestRecord] = CASE WHEN his.[Id] = his.[MaxId] THEN 'Yes' ELSE 'No' END
				,[FileType] = CASE his.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' END
				,[FileStatus] = CASE his.[IsComple] WHEN 1 THEN 'Live' WHEN 0 THEN 'Test' END
				,[FileStatusDate] = CASE WHEN his.[IsComple] = 1 THEN his.[DateCompleted] ELSE his.[DateCreated] END
				,[TestFileStatusReason] = rea.[Name]
				,[PlannedTestEndDate] = CASE his.[FileType] WHEN 1 THEN his.[DailyFileTestEndDate] WHEN 2 THEN his.[MonthlyFileTestEndDate] END
				,[FileTestSignoffDate] = his.[SignoffDate]
				,[PlannedGoLiveDate] = CASE his.[FileType] WHEN 1 THEN his.[DailyFileGoLiveDate] WHEN 2 THEN his.[MonthlyFileGoLiveDate] END
				,[ActualGoLiveDate] = his.[DateCompleted]
				,[TestingSkipped] = CASE WHEN CAST(his.[DateCreated] AS DATE) = CAST([DateCompleted] AS DATE) THEN 'Yes' ELSE 'No' END
			FROM latest his
			INNER JOIN [dbo].[SRNs] srn
			ON his.[SRNId] = srn.[Id]
			INNER JOIN [dbo].[SRNStatuses] sta
			ON srn.[SRNStatusId] = sta.[Id]
			LEFT OUTER JOIN [dbo].[SRNStatusReasons] rea
			ON rea.[Id] = his.[SRNStatusReasonId]

			ORDER BY
				srn.[SRNNumber] ASC,
				his.[DateCompleted] ASC
			OFFSET 0 ROWS
			GO");

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNFileExtract]");
        }
    }
}
