// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Sacrra.Membership.Database;

namespace Sacrra.Membership.Database.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20190705074418_UpdatedSRN")]
    partial class UpdatedSRN
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "2.2.4-servicing-10062")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ALG", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("ALGs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.AccountType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("AccountTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Bureau", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("Bureaus");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.ContactType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("ContactTypes");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.LoanManagementSystemVendor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("LoanManagementSystemVendors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Member", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AnalyticsCompanyName");

                    b.Property<int>("AnnualTurnover");

                    b.Property<int>("ApplicationStatusId");

                    b.Property<string>("DisqualificationReason");

                    b.Property<string>("HeadOfficePhysicalAddress")
                        .IsRequired();

                    b.Property<string>("HeadOfficePostalAddress")
                        .IsRequired();

                    b.Property<string>("IDDocumentBlob");

                    b.Property<long>("IdNumber");

                    b.Property<bool>("IsNcrRegistrant");

                    b.Property<bool>("IsSoleProp");

                    b.Property<int>("MembershipTypeId");

                    b.Property<int>("NcrCategoryId");

                    b.Property<string>("NcrCertificateBlob");

                    b.Property<int>("NcrClassificationId");

                    b.Property<int>("NcrReportingPrimaryBusinessClassificationId");

                    b.Property<int>("NcrcpNumber");

                    b.Property<int>("PrimaryBureauId");

                    b.Property<int>("PrincipleDebtValue");

                    b.Property<string>("RegisteredName")
                        .IsRequired();

                    b.Property<string>("RegisteredNumber");

                    b.Property<int>("SecondaryBureauId");

                    b.Property<int?>("StakeholderManagerId");

                    b.Property<int>("UserId");

                    b.Property<int>("VatNumber");

                    b.Property<string>("Website");

                    b.HasKey("Id");

                    b.HasIndex("PrimaryBureauId");

                    b.HasIndex("SecondaryBureauId");

                    b.HasIndex("StakeholderManagerId");

                    b.HasIndex("UserId");

                    b.ToTable("Members");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberApplicationChangeLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Date");

                    b.Property<string>("Field");

                    b.Property<int>("MemberId");

                    b.Property<string>("NewValue");

                    b.Property<string>("OldValue");

                    b.Property<int>("UserId");

                    b.HasKey("Id");

                    b.ToTable("ChangeLog");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CellNumber");

                    b.Property<int>("ContactTypeId");

                    b.Property<string>("Email");

                    b.Property<string>("FirstName");

                    b.Property<string>("JobTitle");

                    b.Property<int>("MemberId");

                    b.Property<string>("OfficeTelNumber");

                    b.Property<string>("Surname");

                    b.HasKey("Id");

                    b.HasIndex("ContactTypeId");

                    b.HasIndex("MemberId");

                    b.ToTable("MemberContacts");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.PartialMember", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AnalyticsCompanyName");

                    b.Property<int>("AnnualTurnover");

                    b.Property<int>("ApplicationStatusId");

                    b.Property<string>("DisqualificationReason");

                    b.Property<string>("HeadOfficePhysicalAddress");

                    b.Property<string>("HeadOfficePostalAddress");

                    b.Property<string>("IDDocumentBlob");

                    b.Property<long>("IdNumber");

                    b.Property<bool>("IsComplete");

                    b.Property<bool>("IsNcrRegistrant");

                    b.Property<bool>("IsSoleProp");

                    b.Property<int>("MembershipTypeId");

                    b.Property<int>("NcrCategoryId");

                    b.Property<string>("NcrCertificateBlob");

                    b.Property<int>("NcrClassificationId");

                    b.Property<int>("NcrReportingPrimaryBusinessClassificationId");

                    b.Property<int>("NcrcpNumber");

                    b.Property<int>("PrimaryBureauId");

                    b.Property<int>("PrincipleDebtValue");

                    b.Property<string>("RegisteredName");

                    b.Property<string>("RegisteredNumber");

                    b.Property<int>("SecondaryBureauId");

                    b.Property<int>("UserId");

                    b.Property<int>("VatNumber");

                    b.Property<string>("Website");

                    b.HasKey("Id");

                    b.ToTable("PartialMembers");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRN", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("ALGId");

                    b.Property<int>("AccountTypeId");

                    b.Property<int>("BillingCycleDay");

                    b.Property<string>("BranchLocation");

                    b.Property<string>("FileDevelopentEndDate");

                    b.Property<string>("FileDevelopmentStartDate");

                    b.Property<string>("FileTestEndDate");

                    b.Property<string>("FileTestStartDate");

                    b.Property<string>("GoLiveDate");

                    b.Property<int>("LoanManagementSystemVendorId");

                    b.Property<int>("MemberId");

                    b.Property<int>("NumberOfActiveAccounts");

                    b.Property<string>("SRNNumber")
                        .IsRequired();

                    b.Property<int>("SoftwareVendorId");

                    b.Property<string>("TradingBrandName")
                        .IsRequired();

                    b.HasKey("Id");

                    b.HasIndex("ALGId");

                    b.HasIndex("AccountTypeId");

                    b.HasIndex("LoanManagementSystemVendorId");

                    b.HasIndex("MemberId");

                    b.HasIndex("SoftwareVendorId");

                    b.ToTable("SRNs");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SoftwareVendor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.ToTable("SoftwareVendors");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.TradingName", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("MemberId");

                    b.Property<string>("Name");

                    b.HasKey("Id");

                    b.HasIndex("MemberId");

                    b.ToTable("TradingNames");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd.HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Email")
                        .IsRequired();

                    b.Property<byte[]>("EmailConfirmationHash");

                    b.Property<byte[]>("EmailConfirmationSalt");

                    b.Property<string>("FirstName")
                        .IsRequired();

                    b.Property<bool>("IsEmailConfirmed");

                    b.Property<string>("LastName")
                        .IsRequired();

                    b.Property<byte[]>("PasswordHash")
                        .IsRequired();

                    b.Property<byte[]>("PasswordSalt");

                    b.Property<int>("RoleId");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.Member", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Bureau", "PrimaryBureau")
                        .WithMany("PrimaryBureauMembers")
                        .HasForeignKey("PrimaryBureauId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.Bureau", "SecondaryBureau")
                        .WithMany("SecondaryBureauMembers")
                        .HasForeignKey("SecondaryBureauId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.User", "StakeholderManager")
                        .WithMany("Members")
                        .HasForeignKey("StakeholderManagerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.User", "User")
                        .WithMany.HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.MemberContact", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ContactType", "ContactType")
                        .WithMany.HasForeignKey("ContactTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("Contacts")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.SRN", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.ALG", "ALG")
                        .WithMany.HasForeignKey("ALGId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.AccountType", "AccountType")
                        .WithMany.HasForeignKey("AccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.LoanManagementSystemVendor", "LoanManagementSystemVendor")
                        .WithMany.HasForeignKey("LoanManagementSystemVendorId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("SRNs")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sacrra.Membership.Database.Models.SoftwareVendor", "SoftwareVendor")
                        .WithMany.HasForeignKey("SoftwareVendorId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Sacrra.Membership.Database.Models.TradingName", b =>
                {
                    b.HasOne("Sacrra.Membership.Database.Models.Member", "Member")
                        .WithMany("TradingNames")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict);
                });
#pragma warning restore 612, 618
        }
    }
}
