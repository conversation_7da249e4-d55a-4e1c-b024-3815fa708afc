using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Database.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace Ssi.Scf.Database
{
    public static class SeedData
    {
        {
            var context = serviceProvider.GetRequiredService<AppDbContext>();
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();
            context.Database.EnsureCreated();

            if (!context.AdhocFileSubmissionCategory.Any())
            {
                var adhocFileSubmissionCategories = new List<AdhocFileSubmissionCategory>
                {
                    new() { Name = "Data Quality" },
                    new() { Name = "Business As Usual" }
                };

                context.AdhocFileSubmissionCategory.AddRange(adhocFileSubmissionCategories);
                context.SaveChanges();
            }

if (!context.AdhocFileSubmissionReason.Any())
            {
                var adhocFileSubmissionReasons = new List<AdhocFileSubmissionReason>
                {
                    new() { FileSubmissionReasonConstId = 1, Name = "Data Quality/QE 1 Clean Ups", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Data Quality").Id },
                    new() { FileSubmissionReasonConstId = 2, Name = "Data Quality/Removal of Duplicates", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Data Quality").Id },
                    new() { FileSubmissionReasonConstId = 3, Name = "Data Quality/Account Deletion", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Data Quality").Id },
                    new() { FileSubmissionReasonConstId = 4, Name = "Data Quality/Data Rectification", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Data Quality").Id},
                    new() { FileSubmissionReasonConstId = 5, Name = "Data Quality/Account Closures", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Data Quality").Id},
                    new() { FileSubmissionReasonConstId = 6, Name = "Data Quality/Migration Clean Up", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Data Quality").Id},

                    new() { FileSubmissionReasonConstId = 7, Name = "Business as Usual/Data/SRN Migration", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Business As Usual").Id },
                    new() { FileSubmissionReasonConstId = 8, Name = "Business as Usual/Account Number Conversion", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Business As Usual").Id },
                    new() { FileSubmissionReasonConstId = 9, Name = "Business as Usual/Account Closures", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Business As Usual").Id },
                    new() { FileSubmissionReasonConstId = 10, Name = "Business as Usual/Migration Clean Up", FileSubmissionCategoryId = context.AdhocFileSubmissionCategory.FirstOrDefault(x => x.Name ) == "Business As Usual").Id },
                };

                context.AdhocFileSubmissionReason.AddRange(adhocFileSubmissionReasons);
                context.SaveChanges();
            }

if (!context.ContactTypes.Any())
            {
                var types = new List<ContactType>
                {
                    new ContactType(){ Name = "Main Contact Details", ApplicableTo = ContactTypeEnum.Member},
                    new ContactType() { Name = "Alternate Contact Details", ApplicableTo = ContactTypeEnum.Member},
                    new ContactType() { Name = "Financial Contact Details", ApplicableTo = ContactTypeEnum.Member},
                    new ContactType() { Name = "Compliance Contact Details", ApplicableTo = ContactTypeEnum.SRN},
                    new ContactType() { Name = "Data Contact Details", ApplicableTo = ContactTypeEnum.SRN},
                    new ContactType(){ Name = "Manual Amendments Contact Details", ApplicableTo = ContactTypeEnum.SRN},
                    new ContactType(){ Name = "Data Transmission Hub Contact Details", ApplicableTo = ContactTypeEnum.SRN}
                };
                context.ContactTypes.AddRange(types);
                context.SaveChanges();
            }

if (!context.SRNStatuses.Any())
            {
                var list = new List<SRNStatus>
                {
                    new SRNStatus(){ Name = "Requested", Code = "R", IsActivityAllowedWhileInProcess = false, IsActive = false},
                    new SRNStatus(){ Name = "SHM Verification", Code = "SHM-V", IsActivityAllowedWhileInProcess = true, IsActive = false},
                    new SRNStatus(){ Name = "Second Verification", Code = "SSHM-V", IsActivityAllowedWhileInProcess = true, IsActive = false},
                    new SRNStatus(){ Name = "Live", Code = "LV", IsActivityAllowedWhileInProcess = true, IsActive = true},
                    new SRNStatus(){ Name = "Live - Missing information", Code = "L-MI", IsActivityAllowedWhileInProcess = true, IsActive = true},
                    new SRNStatus(){ Name = "Test - DTH user info to be updated", Code = "C-DTHU", IsActivityAllowedWhileInProcess = true, IsActive = false},
                    new SRNStatus(){ Name = "Rejected", Code = "RD", IsActivityAllowedWhileInProcess = false, IsActive = false},
                    new SRNStatus(){ Name = "Sold", Code = "S", IsActivityAllowedWhileInProcess = false, IsActive = false},
                    new SRNStatus(){ Name = "Deactivated - Merged", Code = "D-M", IsActivityAllowedWhileInProcess = false, IsActive = false},
                    new SRNStatus(){ Name = "Deactivated - Split", Code = "D-S", IsActivityAllowedWhileInProcess = false, IsActive = false},
                    new SRNStatus(){ Name = "Sale In Progress - Partial", Code = "SIP-P", IsActivityAllowedWhileInProcess = true, IsActive = true},
                    new SRNStatus(){ Name = "Sale In Progress - Full", Code = "SIP-F", IsActivityAllowedWhileInProcess = false, IsActive = true},
                    new SRNStatus(){ Name = "Split In Progress - Partial", Code = "STIP-P", IsActivityAllowedWhileInProcess = true, IsActive = true},
                    new SRNStatus(){ Name = "Split In Progress - Full", Code = "STIP-F", IsActivityAllowedWhileInProcess = false, IsActive = true},
                    new SRNStatus(){ Name = "Merge In Progress", Code = "MIP", IsActivityAllowedWhileInProcess = false, IsActive = true},
                    new SRNStatus(){ Name = "Deactivated", Code = "D", IsActivityAllowedWhileInProcess = false, IsActive = false},
                    new SRNStatus(){ Name = "Test", Code = "T", IsActivityAllowedWhileInProcess = true, IsActive = false},
                    new SRNStatus(){ Name = "Closure Pending", Code = "CP", IsActivityAllowedWhileInProcess = true, IsActive = true},
                    new SRNStatus(){ Name = "Closed", Code = "C", IsActivityAllowedWhileInProcess = false, IsActive = false},
                    new SRNStatus(){ Name = "Running Down", Code = "RDW", IsActivityAllowedWhileInProcess = true, IsActive = true},
                    new SRNStatus(){ Name = "Dormant", Code = "DM", IsActivityAllowedWhileInProcess = true, IsActive = true},
                };
                context.SRNStatuses.AddRange(list);
                context.SaveChanges();
            }

if (!context.SRNSettings.Any())
            {
                var lastGenerated = new SRNSetting() {
                    LastGeneratedNumber = 0,
                    LastGeneratedAt = DateTime.Now,
                    MaxGeneratedNumberAllowed = 9999,
                    NoOfDigitsAllowed = 4,
                    Prefix = "XY",
                    Exclusions = "XZ0201,XZ0203",
                    Increment = 1
                };
                context.SRNSettings.Add(lastGenerated);
                context.SaveChanges();
            }

            CreateDefaultSRNFieldUpdateSettings(context);
            CreateDefaultSRNFieldUpdateSettings(context);
            CreateSRNAccountTypes(context);
            CreateALGs(context);
            CreateBureaus(context);
            CreateNCRReportingAccountTypeClassifications(context);
            CreateLoanManagementSystemVendors(context);
            CreateSoftwareVendors(context);
            CreateEntityTypes(context);
            CreateMemberStatusReasons(context);
            CreateSRNStatusReasons(context);
            CreateRolloutStatuses(context);
            CreateMailSettings(context);
            CreateCreditInformationClassifications(context);
            CreateBureauObscureMappings(context);
            CreateCamundaErrorRecipients(context);
            CreateDocumentCategories(context);
            CreateDocumentStatuses(context);
            CreateOSLAReasons(context);
            CreateInvoiceRate(context);
            CreateTsAndCsDocument(context);
            CreateFileSubmissionCategories(context);
            CreateViews(context);
            CreateDisqualifiedReasonTypes(context);
        }

        {
            CreateView(context, Scripts.vwSRNSummaryAllDetails, "vwSRNSummaryAllDetails");
            CreateView(context, Scripts.vwRolloutSchedule, "vwRolloutSchedule");
        }

        {
            using var command = context.Database.GetDbConnection.CreateCommand();
            
            command.CommandText = $"SELECT OBJECT_DEFINITION(OBJECT_ID('{objectName}'))";
            context.Database.OpenConnection();
            var result = command.ExecuteScalar();
            
            // object doesn't already exist in the database
            if (result ) == null)
            {
                command.CommandText = viewScript;
                command.ExecuteNonQuery();
                return;
            }

            var existingViewDefinition = result.ToString();
            // If the view already exists and the definition is the same, do nothing
if (viewScript.Replace("CREATE OR ALTER VIEW", "") == existingViewDefinition.Replace("CREATE   VIEW", "")) 
                return;
            
            // View exists but definition is different, we recreate the view
            command.CommandText = viewScript;
            command.ExecuteNonQuery();
        }

        {
            if (!context.MonthlyOSLAReasons.Any())
            {
                List<MonthlyOSLAReason> monthlyOSLAReasons = new()
                {
                    new MonthlyOSLAReason() { Name = "Data Quality Query" },
                    new MonthlyOSLAReason() { Name = "File Layout Query" },
                    new MonthlyOSLAReason() { Name = "Missing Month End - Non ALG" },
                    new MonthlyOSLAReason() { Name = "File Failed Decryption" },
                    new MonthlyOSLAReason() { Name = "Ad Hoc File to be Processed Prior to Monthly File" },
                    new MonthlyOSLAReason() { Name = "Instruction from SACRRA not to Load" },
                    new MonthlyOSLAReason() { Name = "Instruction from Data Provider not to Load" },
                    new MonthlyOSLAReason() { Name = "SRN Closed/Dormant - Live Submission Received" },
                    new MonthlyOSLAReason() { Name = "Reason for Replacement File Pending" },
                    new MonthlyOSLAReason() { Name = "Replacement File Expected" },
                    new MonthlyOSLAReason() { Name = "Replacement File Received, Original Received Date Reflected as Opposed to the Replacement File Receive Date" },
                };

                context.MonthlyOSLAReasons.AddRange(monthlyOSLAReasons);
                context.SaveChanges();
            }
        }

        {
            if (!context.SRNFieldUpdateSettings.Any())
            {
                SRN srn = new SRN();

                var type = srn.GetType();
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(type.GetProperties());

                foreach (PropertyInfo prop in srnProperties)
                {
                    var isApprovalRequired = false;

                    if (prop.Name == "LoanManagementSystemVendorId"
                        || prop.Name == "AccountTypeId" || prop.Name ) == "TradingName")
                    {
                        isApprovalRequired = true;
                    }

if (prop.Name ) != "Id")
                    {
if (prop.PropertyType ) == typeof(string) || prop.PropertyType == typeof(int)
                        || prop.PropertyType == typeof(bool) || prop.PropertyType == typeof(decimal)
                        || prop.PropertyType == typeof(int?) || prop.PropertyType == typeof(bool?)
                        || prop.PropertyType == typeof(decimal?))
                        {
                            var setting = new SRNFieldUpdateSetting() {
                                ObjectName = "SRN",
                                FieldName = prop.Name,
                                IsApprovalRequired = isApprovalRequired,
                                IsUpdatable = true,
                                ToBeApprovedBy = FieldUpdateToBeApprovedBy.SHM
                            };
                            context.SRNFieldUpdateSettings.Add(setting);
                        }
                    }

if (prop.PropertyType ) == typeof(ICollection<SRNContact>))
                    {
                        SRNContact contact = new SRNContact();
                        var contactType = contact.GetType();
                        var contactProperties = contactType.GetProperties();

                        foreach (PropertyInfo contactProp in contactProperties)
                        {
if (contactProp.Name ) != "Id")
                            {
if (contactProp.PropertyType ) == typeof(String) || contactProp.PropertyType == typeof(Int32)
                                || contactProp.PropertyType == typeof(Boolean) || contactProp.PropertyType == typeof(Decimal))
                                {
                                    var setting = new SRNFieldUpdateSetting() {
                                        ObjectName = "SRNContact",
                                        FieldName = contactProp.Name,
                                        IsApprovalRequired = isApprovalRequired,
                                        IsUpdatable = true,
                                        ToBeApprovedBy = FieldUpdateToBeApprovedBy.SHM
                                    };
                                    context.SRNFieldUpdateSettings.Add(setting);
                                }
                            }
                        }
                    }
if (prop.PropertyType ) == typeof(ICollection<BranchLocation>))
                    {
                        BranchLocation location = new BranchLocation();
                        var contactType = location.GetType();
                        var contactProperties = contactType.GetProperties();

                        foreach (PropertyInfo contactProp in contactProperties)
                        {
if (contactProp.Name ) != "Id")
                            {
if (contactProp.PropertyType ) == typeof(String) || contactProp.PropertyType == typeof(Int32)
                                || contactProp.PropertyType == typeof(Boolean) || contactProp.PropertyType == typeof(Decimal))
                                {
                                    var setting = new SRNFieldUpdateSetting() {
                                        ObjectName = "BranchLocation",
                                        FieldName = contactProp.Name,
                                        IsApprovalRequired = isApprovalRequired,
                                        IsUpdatable = true,
                                        ToBeApprovedBy = FieldUpdateToBeApprovedBy.SHM
                                    };
                                    context.SRNFieldUpdateSettings.Add(setting);
                                }
                            }
                        }
                    }

if (prop.PropertyType ) == typeof(ICollection<SRNStatusUpdateHistory>))
                    {
                        SRNStatusUpdateHistory updateHistory = new SRNStatusUpdateHistory();
                        var updateHistoryType = updateHistory.GetType();
                        var updateHistoryProperties = updateHistoryType.GetProperties();

                        foreach (PropertyInfo updateProd in updateHistoryProperties)
                        {
if (updateProd.Name != "Id" && updateProd.Name != "DateCreated" && updateProd.Name != "ProcessInstanceId"
                                && updateProd.Name != "IsComple" && updateProd.Name != "DateCompleted" && updateProd.Name != "UpdateNumber"
                                && updateProd.Name ) != "SRNId")
                            {
if(updateProd.Name == "DailyFileTestStartDate" || updateProd.Name == "MonthlyFileTestStartDate"
                                    || updateProd.Name == "DailyFileGoLiveDate" || updateProd.Name ) == "MonthlyFileGoLiveDate")
                                {
                                    isApprovalRequired = true;
                                }                                {
                                    isApprovalRequired = false;
                                }

if (updateProd.PropertyType ) == typeof(String) || updateProd.PropertyType == typeof(Int32)
                                || updateProd.PropertyType == typeof(Boolean) || updateProd.PropertyType == typeof(Decimal)
                                || updateProd.PropertyType == typeof(DateTime) || updateProd.PropertyType == typeof(DateTime?))
                                {
                                    var setting = new SRNFieldUpdateSetting() {
                                        ObjectName = "SRNStatusUpdate",
                                        FieldName = updateProd.Name,
                                        IsApprovalRequired = isApprovalRequired,
                                        IsUpdatable = true,
                                        ToBeApprovedBy = FieldUpdateToBeApprovedBy.SHM
                                    };
                                    context.SRNFieldUpdateSettings.Add(setting);
                                }
                            }
                        }
                    }

                    context.SaveChanges();
                }
            }
        }
        {
            if (!context.DisqualifiedReasonTypes.Any())
            {
                var data = new List<DisqualifiedReason>
                {
                    new DisqualifiedReason(){ Name = "Other"},
                    new DisqualifiedReason(){ Name = "Rejected"},
                    new DisqualifiedReason(){ Name = "Test"},
                  
                };
                context.DisqualifiedReasonTypes.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.AccountTypes.Any())
            {
                var data = new List<AccountType>
                {
                    new AccountType(){ Name = "B - Building Loan"},
                    new AccountType(){ Name = "C - Credit Card"},
                    new AccountType(){ Name = "D - Debt Recovery"},
                    new AccountType(){ Name = "E - Single Credit Facility"},
                    new AccountType(){ Name = "F - Open Services"},
                    new AccountType(){ Name = "G - Garage Card"},
                    new AccountType(){ Name = "H - Home Loan"},
                    new AccountType(){ Name = "I - Instalment"},
                    new AccountType(){ Name = "L - Life Insurance"},
                    new AccountType(){ Name = "M - One Month Personal Loan"},
                    new AccountType(){ Name = "N - Secured Pension / Policy Backed Lending"},
                    new AccountType(){ Name = "O - Open / Limitless"},
                    new AccountType(){ Name = "P - Personal Loan"},
                    new AccountType(){ Name = "R - Revolving Credit Store Cards"},
                    new AccountType(){ Name = "S - Short Term Insurance"},
                    new AccountType(){ Name = "T - Student Loans"},
                    new AccountType(){ Name = "U - Utility"},
                    new AccountType(){ Name = "V - Overdrafts"},
                    new AccountType(){ Name = "W - Rental Asset"},
                    new AccountType(){ Name = "X - Rentals Property"},
                    new AccountType(){ Name = "Y - Vehicle and Asset Finance"},
                    new AccountType(){ Name = "Z - Revolving - Non Store Card"},
                };
                context.AccountTypes.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.ALGs.Any())
            {
                var data = new List<ALG>
                {
                    new ALG(){ Name = "A4dable"},
                    new ALG(){ Name = "Acpas"},
                    new ALG(){ Name = "CMS"},
                    new ALG(){ Name = "Compuscan"},
                    new ALG(){ Name = "Delter"},
                    new ALG(){ Name = "Loan-Info"},
                    new ALG(){ Name = "Mycomax"}
                };
                context.ALGs.AddRange(data);
                context.SaveChanges();
            }

            var algLeaders = context.Members.Where(i => i.MembershipTypeId ) == MembershipTypes.ALGLeader).ToList();
if (algLeaders.Count ) == 0)
            {
                var user = context.Users.FirstOrDefault(i => i.RoleId ) == UserRoles.StakeHolderManager);

                var defaultMember = new Member() {
                    IsNcrRegistrant = false,
                    IsSoleProp = false,
                    HeadOfficePhysicalAddress = "",
                    HeadOfficePostalAddress = "",
                    MembershipTypeId = MembershipTypes.ALGLeader,
                    ApplicationStatusId = ApplicationStatuses.MemberRegistrationCompleted,
                    IsVatRegistrant = false,
                    Users = new List<MemberUsers>
                    {
                        new MemberUsers() {
                            UserId = (user ) != null) ? user.Id : 1,
                            DateCreated = DateTime.Now
                        }
                    }
                };

                var defaultALGLeaders = new string[] { "A4DABLE", "ACPAS", "CMS", "COMPUSCAN", "DELTER", "LOAN-INFO", "MYCOMAX" };
                foreach (var alg in defaultALGLeaders)
                {
                    var data = new Member() {
                        RegisteredName = alg,
                        IsNcrRegistrant = defaultMember.IsNcrRegistrant,
                        IsSoleProp = defaultMember.IsSoleProp,
                        HeadOfficePhysicalAddress = defaultMember.HeadOfficePhysicalAddress,
                        HeadOfficePostalAddress = defaultMember.HeadOfficePostalAddress,
                        MembershipTypeId = defaultMember.MembershipTypeId,
                        ApplicationStatusId = defaultMember.ApplicationStatusId,
                        IsVatRegistrant = defaultMember.IsVatRegistrant,
                        StakeholderManagerId = (user ) != null) ? user.Id : 1,
                        Contacts = new List<MemberContact>
                        {
                            new MemberContact() {
                                JobTitle = "Dr",
                                FirstName = "Sam",
                                Surname = "Sung",
                                CellNumber = "0745874422",
                                OfficeTelNumber = "0112543366",
                                Email = "<EMAIL>",
                                ContactTypeId = GetContactTypeIdByName("Main Contact Details", context)
                            },
                            new MemberContact() {
                                JobTitle = "Mr",
                                FirstName = "Jack",
                                Surname = "Daniels",
                                CellNumber = "0765475522",
                                OfficeTelNumber = "0123652244",
                                Email = "<EMAIL>",
                                ContactTypeId = GetContactTypeIdByName("Alternate Contact Details", context)
                            },
                            new MemberContact() {
                                JobTitle = "Mr",
                                FirstName = "Lord",
                                Surname = "Brain",
                                CellNumber = "0793652211",
                                OfficeTelNumber = "0112547788",
                                Email = "<EMAIL>",
                                ContactTypeId = GetContactTypeIdByName("Financial Contact Details", context)
                            },
                            new MemberContact() {
                                JobTitle = "Mrs",
                                FirstName = "Beautiful",
                                Surname = "Existence",
                                CellNumber = "0846521100",
                                OfficeTelNumber = "0123658833",
                                Email = "<EMAIL>",
                                ContactTypeId = GetContactTypeIdByName("Compliance Contact Details", context)
                            },
                            new MemberContact() {
                                JobTitle = "Ms",
                                FirstName = "Hashtag",
                                Surname = "Follow",
                                CellNumber = "0785874411",
                                OfficeTelNumber = "0113652211",
                                Email = "<EMAIL>",
                                ContactTypeId = GetContactTypeIdByName("Data Contact Details", context)
                            },
                            new MemberContact() {
                                JobTitle = "Mr",
                                FirstName = "Rick",
                                Surname = "Roll",
                                CellNumber = "0793652211",
                                OfficeTelNumber = "0118886322",
                                Email = "<EMAIL>",
                                ContactTypeId = GetContactTypeIdByName("Manual Amendments Contact Details", context)
                            },
                            new MemberContact() {
                                JobTitle = "Mr",
                                FirstName = "Jackson",
                                Surname = "Galaxy",
                                CellNumber = "0785874411",
                                OfficeTelNumber = "0113652211",
                                Email = "<EMAIL>",
                                ContactTypeId = GetContactTypeIdByName("Data Transmission Hub Contact Details", context)
                            }
                        },
                        Users = new List<MemberUsers>
                        {
                            new MemberUsers() {
                                UserId = (user ) != null) ? user.Id : 1,
                                DateCreated = DateTime.Now
                            }
                        }
                    };

                    context.Members.Add(data);
                    context.SaveChanges();

                    var memberDetails = new ALGMemberDetails() {
                        MemberId = data.Id,
                        LoanManagementSystemName = "XYZ Systems",
                        NumberOfClients = 0
                    };

                    context.ALGMemberDetails.Add(memberDetails);
                    context.SaveChanges();
                }
            }
        }
        {
            if (!context.Bureaus.Any())
            {
                var data = new List<Bureau>
                {
                    new Bureau(){ Name = "TransUnion"},
                    new Bureau(){ Name = "Experian"},
                    new Bureau(){ Name = "Compuscan"},
                    new Bureau(){ Name = "XDS"},
                    new Bureau(){ Name = "CPB"},
                    new Bureau(){ Name = "VeriCred"},
                    new Bureau(){ Name = "Other/None"}
                };
                context.Bureaus.AddRange(data);
                context.SaveChanges();
            }

            var bureaus = context.Members.Where(i => i.MembershipTypeId ) == MembershipTypes.Bureau).ToList();
if (bureaus.Count ) == 0)
            {
                var defaultMember = new Member() {
                    IsNcrRegistrant = false,
                    IsSoleProp = false,
                    HeadOfficePhysicalAddress = "",
                    HeadOfficePostalAddress = "",
                    MembershipTypeId = MembershipTypes.Bureau,
                    ApplicationStatusId = ApplicationStatuses.MemberRegistrationCompleted,
                    IsVatRegistrant = false,
                    Users = new List<MemberUsers>
                    {
                        new MemberUsers() {
                            UserId = 1,
                            DateCreated = DateTime.Now
                        }
                    }
                };

                var defaultBureaus = new string[] { "XDS", "TRANSUNION", "COMPUSCAN", "EXPERIAN", "LUCID", "CPB" };
                foreach (var alg in defaultBureaus)
                {
                    var data = new Member() {
                        RegisteredName = alg,
                        IsNcrRegistrant = defaultMember.IsNcrRegistrant,
                        IsSoleProp = defaultMember.IsSoleProp,
                        HeadOfficePhysicalAddress = defaultMember.HeadOfficePhysicalAddress,
                        HeadOfficePostalAddress = defaultMember.HeadOfficePostalAddress,
                        MembershipTypeId = defaultMember.MembershipTypeId,
                        ApplicationStatusId = defaultMember.ApplicationStatusId,
                        IsVatRegistrant = defaultMember.IsVatRegistrant,
                        Users = defaultMember.Users
                    };

                    context.Members.Add(data);
                }
                context.SaveChanges();
            }
        }
        {
            if (!context.NCRReportingAccountTypeClassifications.Any())
            {
                var data = new List<NCRReportingAccountTypeClassification>
                {
                    new NCRReportingAccountTypeClassification(){ Name = "Mortgage"},
                    new NCRReportingAccountTypeClassification(){ Name = "Vehicle and Asset finance"},
                    new NCRReportingAccountTypeClassification(){ Name = "Credit Cards"},
                    new NCRReportingAccountTypeClassification(){ Name = "Store Cards"},
                    new NCRReportingAccountTypeClassification(){ Name = "Fixed Term Agreements"},
                    new NCRReportingAccountTypeClassification(){ Name = "Fixed Term Loans (short term)"},
                    new NCRReportingAccountTypeClassification(){ Name = "Fixed Term Loans (unsecured)"},
                    new NCRReportingAccountTypeClassification(){ Name = "Student Loans"},
                    new NCRReportingAccountTypeClassification(){ Name = "Open Line - Telecommunication"},
                    new NCRReportingAccountTypeClassification(){ Name = "Open Line - Other"},
                    new NCRReportingAccountTypeClassification(){ Name = "All Other Credit Transactions"},
                    new NCRReportingAccountTypeClassification(){ Name = "Overdraft Facilities"},
                    new NCRReportingAccountTypeClassification(){ Name = "Consolidation Accounts / Pesion Backed"},
                    new NCRReportingAccountTypeClassification(){ Name = "Revolving Loans - Revolving & Loans Not Defined in 6.6 or 6.7"},
                    new NCRReportingAccountTypeClassification(){ Name = "Sole Prop / Comercial"},
                    new NCRReportingAccountTypeClassification(){ Name = "Non SA"},
                    new NCRReportingAccountTypeClassification(){ Name = "Credit Cards or 6.7 Fixed Term Loans (Unsecured)"},
                    new NCRReportingAccountTypeClassification(){ Name = "Fixed Term Agreements or 6.6 Fixed Term Loans (Short Term) or 6.7 Fixed Term Loans (Unsecured)"},
                    new NCRReportingAccountTypeClassification(){ Name = "Fixed Term Loans (Short Term) or 6.7 Fixed Term Loans (Unsecured)"},
                    new NCRReportingAccountTypeClassification(){ Name = "Fixed Term Loans (Short Term) or 6.7 Fixed Term Loans (Unsecured) or 6.14 Revolving Loans"},
                    new NCRReportingAccountTypeClassification(){ Name = "Credit Cards or 6.12 Overdraft Facilities"},
                };
               
                context.NCRReportingAccountTypeClassifications.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.LoanManagementSystemVendors.Any())
            {
                var data = new List<LoanManagementSystemVendor>
                {
                    new LoanManagementSystemVendor(){ Name = "A4dable"},
                    new LoanManagementSystemVendor(){ Name = "Acczone"},
                    new LoanManagementSystemVendor(){ Name = "Acpas"},
                    new LoanManagementSystemVendor(){ Name = "African Cash System"},
                    new LoanManagementSystemVendor(){ Name = "Amsa / Elfin"},
                    new LoanManagementSystemVendor(){ Name = "Ao Lms (Inhouse)"},
                    new LoanManagementSystemVendor(){ Name = "Argility"},
                    new LoanManagementSystemVendor(){ Name = "Bancs / Enterprise"},
                    new LoanManagementSystemVendor(){ Name = "Benefit"},
                    new LoanManagementSystemVendor(){ Name = "Cml"},
                    new LoanManagementSystemVendor(){ Name = "Cms"},
                    new LoanManagementSystemVendor(){ Name = "Compuloan"},
                    new LoanManagementSystemVendor(){ Name = "Creditcheck"},
                    new LoanManagementSystemVendor(){ Name = "Creditease"},
                    new LoanManagementSystemVendor(){ Name = "Creditscan"},
                    new LoanManagementSystemVendor(){ Name = "Crm"},
                    new LoanManagementSystemVendor(){ Name = "Customer Inhouse Developed Software"},
                    new LoanManagementSystemVendor(){ Name = "Delfin"},
                    new LoanManagementSystemVendor(){ Name = "E-Box"},
                    new LoanManagementSystemVendor(){ Name = "Einstein"},
                    new LoanManagementSystemVendor(){ Name = "Elfin"},
                    new LoanManagementSystemVendor(){ Name = "Evolution"},
                    new LoanManagementSystemVendor(){ Name = "Exactus"},
                    new LoanManagementSystemVendor(){ Name = "Fame"},
                    new LoanManagementSystemVendor(){ Name = "Ft Loans"},
                    new LoanManagementSystemVendor(){ Name = "Icems"},
                    new LoanManagementSystemVendor(){ Name = "Infotech"},
                    new LoanManagementSystemVendor(){ Name = "Interactive Solutions"},
                    new LoanManagementSystemVendor(){ Name = "Lims"},
                    new LoanManagementSystemVendor(){ Name = "Loan Info"},
                    new LoanManagementSystemVendor(){ Name = "Loan-Info - Sacrra Only"},
                    new LoanManagementSystemVendor(){ Name = "Maxloan"},
                    new LoanManagementSystemVendor(){ Name = "Maxmoney"},
                    new LoanManagementSystemVendor(){ Name = "Net1"},
                    new LoanManagementSystemVendor(){ Name = "P2p Force"},
                    new LoanManagementSystemVendor(){ Name = "Proloan"},
                    new LoanManagementSystemVendor(){ Name = "Run-A-Loan"},
                    new LoanManagementSystemVendor(){ Name = "Seg Data"},
                    new LoanManagementSystemVendor(){ Name = "Smartfin"},
                    new LoanManagementSystemVendor(){ Name = "Syspro"},
                    new LoanManagementSystemVendor(){ Name = "System Integration"},
                    new LoanManagementSystemVendor(){ Name = "Tfg Infotec"},
                    new LoanManagementSystemVendor(){ Name = "Trader"},
                    new LoanManagementSystemVendor(){ Name = "Vision Plus"},
                    new LoanManagementSystemVendor(){ Name = "Web Service Or Api"},
                    new LoanManagementSystemVendor(){ Name = "Webfin"},
                    new LoanManagementSystemVendor(){ Name = "Webfin2"},
                    new LoanManagementSystemVendor(){ Name = "Xls"},
                    new LoanManagementSystemVendor(){ Name = "Zie (In-House)"},
                    new LoanManagementSystemVendor(){ Name = "Zonketech Loan Management System"}
                };
                context.LoanManagementSystemVendors.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.SoftwareVendors.Any())
            {
                var data = new List<SoftwareVendor>
                {
                    new SoftwareVendor(){ Name = "A4dable"},
                    new SoftwareVendor(){ Name = "Acquire"},
                    new SoftwareVendor(){ Name = "Argility"},
                    new SoftwareVendor(){ Name = "Astech"},
                    new SoftwareVendor(){ Name = "Badger Group Softsure "},
                    new SoftwareVendor(){ Name = "Bancs / Enterprise"},
                    new SoftwareVendor(){ Name = "Benefit"},
                    new SoftwareVendor(){ Name = "Bigworld Mobile (Pty) Ltd "},
                    new SoftwareVendor(){ Name = "Blake & Associates (Pty) Ltd"},
                    new SoftwareVendor(){ Name = "Brolink"},
                    new SoftwareVendor(){ Name = "Chartwell Housing Finance Solutions (Pty) Ltd"},
                    new SoftwareVendor(){ Name = "Cms"},
                    new SoftwareVendor(){ Name = "Comit"},
                    new SoftwareVendor(){ Name = "Complete Coding"},
                    new SoftwareVendor(){ Name = "Compuscan"},
                    new SoftwareVendor(){ Name = "Crm"},
                    new SoftwareVendor(){ Name = "Cpb"},
                    new SoftwareVendor(){ Name = "Delter It"},
                    new SoftwareVendor(){ Name = "Direct Axis"},
                    new SoftwareVendor(){ Name = "Direct Request"},
                    new SoftwareVendor(){ Name = "Emid Solutions"},
                    new SoftwareVendor(){ Name = "Emp"},
                    new SoftwareVendor(){ Name = "Ems"},
                    new SoftwareVendor(){ Name = "Entertech Solutions"},
                    new SoftwareVendor(){ Name = "Ey"},
                    new SoftwareVendor(){ Name = "Fleet Domain"},
                    new SoftwareVendor(){ Name = "Fluxfirm"},
                    new SoftwareVendor(){ Name = "Helen Wessels"},
                    new SoftwareVendor(){ Name = "Huawei"},
                    new SoftwareVendor(){ Name = "Interactive Solutions"},
                    new SoftwareVendor(){ Name = "Iris"},
                    new SoftwareVendor(){ Name = "Izwe Loans (Pty) Ltd"},
                    new SoftwareVendor(){ Name = "Ke Concepts"},
                    new SoftwareVendor(){ Name = "Lims"},
                    new SoftwareVendor(){ Name = "Loan-Info"},
                    new SoftwareVendor(){ Name = "Maxloan"},
                    new SoftwareVendor(){ Name = "Mercantile"},
                    new SoftwareVendor(){ Name = "Mettle (Pty) Ltd"},
                    new SoftwareVendor(){ Name = "Mip"},
                    new SoftwareVendor(){ Name = "Mis"},
                    new SoftwareVendor(){ Name = "Mlcb"},
                    new SoftwareVendor(){ Name = "Mvnx (Pty) Ltd"},
                    new SoftwareVendor(){ Name = "Mycomax"},
                    new SoftwareVendor(){ Name = "Net1"},
                    new SoftwareVendor(){ Name = "Octagon"},
                    new SoftwareVendor(){ Name = "Pepkor It"},
                    new SoftwareVendor(){ Name = "Prime"},
                    new SoftwareVendor(){ Name = "Product Credit Solutions"},
                    new SoftwareVendor(){ Name = "Red Panda"},
                    new SoftwareVendor(){ Name = "Rent Pay"},
                    new SoftwareVendor(){ Name = "S A Home Loans"},
                    new SoftwareVendor(){ Name = "Sage"},
                    new SoftwareVendor(){ Name = "Sap"},
                    new SoftwareVendor(){ Name = "Ssda"},
                    new SoftwareVendor(){ Name = "Udu"},
                    new SoftwareVendor(){ Name = "Visionplus (Emp)"},
                    new SoftwareVendor(){ Name = "Wesbank"},
                    new SoftwareVendor(){ Name = "Xds"},
                    new SoftwareVendor(){ Name = "Xpertek"}
                };
                context.SoftwareVendors.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.EntityTypes.Any())
            {
                var data = new List<EntityType>
                {
                    new EntityType(){ Name = "Member"},
                    new EntityType(){ Name = "SRN"},
                    new EntityType(){ Name = "Branch Location"},
                    new EntityType(){ Name = "Member Contact"},
                    new EntityType(){ Name = "SRN Contact"},
                    new EntityType(){ Name = "Trading Name"},
                    new EntityType(){ Name = "Member Document"},
                    new EntityType(){ Name = "Account Type"},
                    new EntityType(){ Name = "Bureau"},
                    new EntityType(){ Name = "Contact Type"},
                    new EntityType(){ Name = "Loan Management System Vendor"},
                    new EntityType(){ Name = "NCRReporting AccountType Classification"},
                    new EntityType(){ Name = "Software Vendor"},
                    new EntityType(){ Name = "SP Group"},
                    new EntityType(){ Name = "SRN Status"},
                    new EntityType(){ Name = "User"},
                    new EntityType(){ Name = "Automated Task Completion"},
                    new EntityType(){ Name = "ReplacementFileSubmission"},
                    new EntityType(){ Name = "AdHocFileSubmission"}
                };
                context.EntityTypes.AddRange(data);
                context.SaveChanges();
            }
        }
        private static int GetContactTypeIdByName(string name, AppDbContext context)
        {
            var contactType = context.ContactTypes.FirstOrDefault(i => i.Name ) == name);
            if (contactType ) == null)
                return 1;                return contactType.Id;
        }

        {
            if (!context.MemberStatusReasons.Any())
            {
                var data = new List<MemberStatusReason>
                {
                    new MemberStatusReason(){ Name = "Business Closed"},
                    new MemberStatusReason(){ Name = "Non-Compliance"},
                    new MemberStatusReason(){ Name = "Non-Payment"},
                    new MemberStatusReason(){ Name = "Resigned"},
                    new MemberStatusReason(){ Name = "Accidental Closure"},
                };
                context.MemberStatusReasons.AddRange(data);
                context.SaveChanges();
            }
        }

        {
            if (!context.SRNStatusReasons.Any())
            {
                var data = new List<SRNStatusReason>
                {
                    new SRNStatusReason() {
                        Name = "Product offering ending", 
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Running down")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "ALG Client in the process of moving to a new ALG Leader",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Running down")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Product discontinued",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Book sold",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Book merged",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Book migrated",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Book split",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "ALG Client no longer with ALG leader",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Branch closed",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Membership type changed",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Product not yet launched",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Business Closed",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Product on hold",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Dormant")
                            }
                        },
                    },
                    new SRNStatusReason() {
                        Name = "Product not yet launched",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Dormant")
                            }
                        },
                    },

                    new SRNStatusReason() {
                        Name = "No more data being submitted",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Dormant")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closure Pending")
                            }
                        }
                    },

                    new SRNStatusReason() {
                        Name = "New SRN",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Live")
                            }
                        }
                    },

                    new SRNStatusReason() {
                        Name = "Re-activated",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Live")
                            }
                        }
                    },

                    new SRNStatusReason() {
                        Name = "New development",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Live")
                            }
                        }
                    },

                    new SRNStatusReason() {
                        Name = "Layout changes",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Live")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Data Quality",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            },
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Live")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout Changes ? Prescribed Debt",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout Changes ? Overdraft",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout Changes - Mortgage",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout Changes ? Sold to Third Party",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout changes - mortgages & overdrafts",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout changes - mortgages & prescribed debt",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout changes - mortgages & sold to third party",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout changes - mortgages & overdrafts & prescribed debt & sold to third party",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout changes - overdrafts & prescribed debt",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout changes - overdrafts & sold to third party",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Layout changes - sold to third party & prescribed debt",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Test")
                            }
                        }
                    },
                    new SRNStatusReason() {
                        Name = "Duplicate SRN",
                        SRNStatuses = new List<SRNStatusReasonSRNStatusLink>
                        {
                            new SRNStatusReasonSRNStatusLink() {
                                SRNStatusId = GetSRNStatusIdByName(context, "Closed")
                            }
                        }
                    }
                };

                context.SRNStatusReasons.AddRange(data);
                context.SaveChanges();
            }
        }

        private static int GetSRNStatusIdByName(AppDbContext context, string name)
        {
            var statuses = context.SRNStatuses.AsQueryable();
            if(statuses ) != null)
            {
                int statusId;

                var status = statuses.FirstOrDefault(i => i.Name ) == name);
                if (status ) != null)
                {
                    statusId = status.Id;
                    return statusId;
                }
            }

            return 0;
        }

        {
            if (!context.RolloutStatuses.Any())
            {
                var data = new List<RolloutStatus>
                {
                    new RolloutStatus(){ Name = "Requested" },
                    new RolloutStatus(){ Name = "Dormant" },
                    new RolloutStatus(){ Name = "Running Down" },
                    new RolloutStatus(){ Name = "Test" },
                    new RolloutStatus(){ Name = "Live" }
                };
                context.RolloutStatuses.AddRange(data);
                context.SaveChanges();
            }
        }

        {
            if (!context.MailSettings.Any())
            {
                var data = new List<MailSetting>
                {
                    new ()
                    {
                        Name = "Failed Events",
                        IsEmailEnabled = true,
                        FilterEmailsFromSender = "<EMAIL>",
                        MailRecipients = new List<MailRecipient>
                        {
                            new()
                            {
                                FirstName = "Linda",
                                LastName = "Mramba",
                                Email = "<EMAIL>"
                            },
                            new()
                            {
                                FirstName = "Sean",
                                LastName = "Nortje",
                                Email = "<EMAIL>"
                            },
                            new()
                            {
                                FirstName = "Franco",
                                LastName = "Scheepers",
                                Email = "<EMAIL>"
                            },
                            new()
                            {
                                FirstName = "Heinrich",
                                LastName = "Smit",
                                Email = "<EMAIL>"
                            },
                            new()
                            {
                                FirstName = "Thato",
                                LastName = "Diseko",
                                Email = "<EMAIL>"
                            }
                        },
                        MailColumns = new List<MailColumn>
                        {
                            new()
                            {
                                PropertyName = "DeliveryStatusAttemptNo",
                                FriendlyName = "Retries"
                            },
                            new()
                            {
                                PropertyName = "DeliveryStatusMessage",
                                FriendlyName = "Status Message"
                            },
                            new()
                            {
                                PropertyName = "DeliveryStatusDescription",
                                FriendlyName = "Status Description"
                            },
                            new()
                            {
                                PropertyName = "EnvelopTargets",
                                FriendlyName = "Target Addresses"
                            },
                            new()
                            {
                                PropertyName = "HeaderTo",
                                FriendlyName = "To Addresses"
                            },
                            new()
                            {
                                PropertyName = "HeaderMessageId",
                                FriendlyName = "ID"
                            },
                            new()
                            {
                                PropertyName = "HeaderSubject",
                                FriendlyName = "Subject"
                            },
                            new()
                            {
                                PropertyName = "Severity",
                                FriendlyName = "Severity"
                            },
                            new()
                            {
                                PropertyName = "LogLevel",
                                FriendlyName = "Log Level"
                            },
                            new()
                            {
                                PropertyName = "Member",
                                FriendlyName = "Member"
                            },
                        }
                    }
                };
                context.MailSettings.AddRange(data);
                context.SaveChanges();
            }
        }

        {
            if (!context.CreditInformationClassifications.Any())
            {
                var data = new List<CreditInformationClassification>
                {
                    new CreditInformationClassification(){ Name = "Consumer Credit" },
                    new CreditInformationClassification(){ Name = "BusCRI Credit" }
                };
                context.CreditInformationClassifications.AddRange(data);
                context.SaveChanges();
            }
        }

        {
            var bureaus = context.Members
                    .Where(i => i.MembershipTypeId ) == MembershipTypes.Bureau)
                    .Select(m => new Member() {
                        Id = m.Id,
                        RegisteredName = m.RegisteredName
                    })
                    .ToList();

            var obscureName = string.Empty;
            var data = new List<BureauObscureMapping>();

            foreach (var member in bureaus)
            {
                var bureauExists = context.BureauObscureMappings.Any(i => i.MemberId ) == member.Id);

                if (!bureauExists)
                {
                    switch (member.RegisteredName)
                    {
                        case "COMPUSCAN":
                            obscureName = "BUREAU1";
                            break;

                        case "CPB":
                            obscureName = "BUREAU2";
                            break;

                        case "EXPERIAN":
                            obscureName = "BUREAU3";
                            break;

                        case "TRANSUNION":
                            obscureName = "BUREAU4";
                            break;

                        case "VCCB":
                            obscureName = "BUREAU5";
                            break;

                        case "XDS":
                            obscureName = "BUREAU6";
                            break;

                        case "ITC Credit Bureau":
                            obscureName = "BUREAU7";
                            break;
                    }

if (!string.IsNullOrWhiteSpace(obscureName))
                    {
                        data.Add(new BureauObscureMapping() {
                            MemberId = member.Id,
                            ObscureName = obscureName
                        });
                    }
                }
            }

            context.BureauObscureMappings.AddRange(data);
            context.SaveChanges();
        }
        {
            if (!context.CamundaErrorRecipients.Any())
            {
                var data = new List<CamundaErrorRecipient>
                {
                    new()
                    {
                        FirstName = "Linda",
                        LastName = "Mramba",
                        Email = "<EMAIL>"
                    },
                    new()
                    {
                        FirstName = "Sean",
                        LastName = "Nortje",
                        Email = "<EMAIL>"
                    },
                    new()
                    {
                        FirstName = "Franco",
                        LastName = "Scheepers",
                        Email = "<EMAIL>"
                    },
                    new()
                    {
                        FirstName = "Heinrich",
                        LastName = "Smit",
                        Email = "<EMAIL>"
                    },
                    new()
                    {
                        FirstName = "Michael",
                        LastName = "Van Antwerpen",
                        Email = "<EMAIL>"
                    },
                };
                context.CamundaErrorRecipients.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.DocumentCategories.Any())
            {
                var data = new List<DocumentCategory>
                {
                    new()
                    {
                        Name = "Category1"
                    }
                };
                context.DocumentCategories.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.DocumentStatuses.Any())
            {
                var data = new List<DocumentStatus>
                {
                    new()
                    {
                        Name = "Active"
                    },
                    new()
                    {
                        Name = "Inactive"
                    }
                };
                context.DocumentStatuses.AddRange(data);
                context.SaveChanges();
            }
        }
        {
            if (!context.InvoiceRates.Any())
            {
                var data = new List<InvoiceRate>
                {
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Jan 2022 to 14 Feb 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-01-15"),
                        DateTo = Convert.ToDateTime("2022-02-14"),
                        Amount = 679.25
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Feb 2022 to 14 Mar 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-02-15"),
                        DateTo = Convert.ToDateTime("2022-03-14"),
                        Amount = 617.50
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Mar 2022 to 14 Apr 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-03-15"),
                        DateTo = Convert.ToDateTime("2022-04-14"),
                        Amount = 555.75
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Apr 2022 to 14 May 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-04-15"),
                        DateTo = Convert.ToDateTime("2022-05-14"),
                        Amount = 494.00
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 May 2022 to 14 June 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-05-15"),
                        DateTo = Convert.ToDateTime("2022-06-14"),
                        Amount = 432.25
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Jun 2022 to 14 Jul 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-06-15"),
                        DateTo = Convert.ToDateTime("2022-07-14"),
                        Amount = 370.50
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Jul 2022 to 14 Aug 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-07-15"),
                        DateTo = Convert.ToDateTime("2022-08-14"),
                        Amount = 308.75
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Aug 2022 to 14 Sep 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-08-15"),
                        DateTo = Convert.ToDateTime("2022-09-14"),
                        Amount = 247.00
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Sep 2022 to 14 Oct 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-09-15"),
                        DateTo = Convert.ToDateTime("2022-10-14"),
                        Amount = 185.25
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Oct 2022 to 14 Nov 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-10-15"),
                        DateTo = Convert.ToDateTime("2022-11-14"),
                        Amount = 123.5
                    },
                    new()
                    {
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        Description = "15 Nov 2022 to 14 Dec 2022",
                        IsActive = true,
                        DateFrom = Convert.ToDateTime("2022-11-15"),
                        DateTo = Convert.ToDateTime("2022-12-14"),
                        Amount = 61.75
                    }
                };
                context.InvoiceRates.AddRange(data);
                context.SaveChanges();
            }
        }

        {
            if (!context.TermsConditionsDocuments.Any())
            {
                context.TermsConditionsDocuments.Add(new TermsConditionsDocument() {
                    BlobName = "Terms of Use & Privacy Policy 2020 11 19.pdf",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = new DateTime(2021, 10, 21),
                    IsActive = true
                });

                context.SaveChanges();
            }
        }

        {
            if (!context.ReplacementFileSubmissionCategories.Any())
            {
                var data = new List<ReplacementFileSubmissionCategory>
                {
                    new()
                    {
                        Name = "Not a replacement file",
                        Reasons = new List<ReplacementFileSubmissionReason>
                        {
                            new()
                            {
                                Name = "File failed decryption"
                            },
                            new()
                            {
                                Name = "File failed DTH validation"
                            },
                            new()
                            {
                                Name = "Additional file to process"
                            }
                        }
                    },
                    new()
                    {
                        Name = "Data quality",
                        Reasons = new List<ReplacementFileSubmissionReason>
                        {
                            new()
                            {
                                Name = "File submitted incorrectly"
                            },
                            new()
                            {
                                Name = "Incorrectly populated data"
                            },
                            new()
                            {
                                Name = "All data not populated"
                            },
                            new()
                            {
                                Name = "High rejections"
                            },
                            new()
                            {
                                Name = "Records not submitted"
                            }
                        }
                    },
                    new()
                    {
                        Name = "Layout related issues",
                        Reasons = new List<ReplacementFileSubmissionReason>
                        {
                            new()
                            {
                                Name = "File corrupted"
                            },
                            new()
                            {
                                Name = "File empty"
                            },
                            new()
                            {
                                Name = "File incomplete"
                            },
                            new()
                            {
                                Name = "File not encrypted"
                            },
                            new()
                            {
                                Name = "Incorrect file format"
                            },
                            new()
                            {
                                Name = "Header issues"
                            },
                            new()
                            {
                                Name = "Invalid characters"
                            },
                            new()
                            {
                                Name = "Misalignment"
                            },
                            new()
                            {
                                Name = "Trailer issue"
                            }
                        }
                    }
                };
                context.ReplacementFileSubmissionCategories.AddRange(data);
                context.SaveChanges();
            }
        }
    }
}
