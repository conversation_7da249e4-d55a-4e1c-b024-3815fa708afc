using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Sacrra.Membership.Database.Models
{
    public abstract class BaseErrorModel
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        [NotMapped]
        public string Path { get; set; }
        [NotMapped]
        public string Input { get; set; }
        [NotMapped]
        public string Method { get; set; }
        [NotMapped]
        public string UserId { get; set; }

public BaseErrorModel()
        {
            Date = DateTime.Now;
        }
    }
}