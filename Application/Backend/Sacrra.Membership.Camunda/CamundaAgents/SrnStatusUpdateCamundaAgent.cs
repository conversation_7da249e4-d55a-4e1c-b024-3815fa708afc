using System;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnStatusUpdateCamundaAgent: BaseCamundaAgent
{
    public SrnStatusUpdateCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    override void Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "email-member-about-SRN-pending-closure":
                EmailMemberAboutSrnPendingClosure(task, completeExternalTask, serviceScope);
                break;
            
            case "email-bureaus-about-srn-pending-closure":
                EmailBureausAboutSrnPendingClosure(task, completeExternalTask, serviceScope);
                break;
            
            case "update-srn-status-to-pending-closure":
                UpdateSrnStatusToPendingClosure(task, completeExternalTask, serviceScope);
                break;
            
            case "update-srn-status-to-closed":
                UpdateSrnStatusToClosed(task, completeExternalTask, serviceScope);
                break;
            
            case "email-bureaus-to-close-the-payment-profile":
                EmailBureausToCloseThePaymentProfile(task, completeExternalTask, serviceScope);
                break;
            
            case "email-member-to-confirm-srn-closure":
                EmailMemberToConfirmSrnClosure(task, completeExternalTask, serviceScope);
                break;
            
            case "kick-off-member-auto-close-workflow-if-member-has-no-active-SRNs":
                KickOffMemberAutoCloseWorkflowIfMemberHasNoActiveSrns(task, completeExternalTask, serviceScope);
                break;
            
            case "set-srn-status-date-to-todays-date":
                SetSrnStatusDateToTodaysDate(task, completeExternalTask, serviceScope);
                break;
            
            case "kill_file_test_tasks":
                KillFileTestTasks(task, completeExternalTask, serviceScope);
                break;
        }

        task.Complete(completeExternalTask);
    }

    private void KillFileTestTasks(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        throw new NotImplementedException();
    }

    private void SetSrnStatusDateToTodaysDate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateCamundaService(serviceScope).SetSRNStatusDateToCurrentDate(Convert.ToInt32(srnId));
        }
    }

    private void KickOffMemberAutoCloseWorkflowIfMemberHasNoActiveSrns(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);
        
        GetSrnStatusUpdateCamundaService(serviceScope).KickoffMemberAutoCloseWorkflowIfMemberHasNoActiveSRNs(srnId);
    }

    private void EmailMemberToConfirmSrnClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateCamundaService(serviceScope).EmailMemberToConfirmClosure(Convert.ToInt32(srnId));
        }
    }

    private void EmailBureausToCloseThePaymentProfile(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateCamundaService(serviceScope).EmailBureausToClosePaymentProfile(Convert.ToInt32(srnId));
        }
    }

    private void UpdateSrnStatusToClosed(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            var taskInfo = task.Get();
        
            var processInstanceId = string.Empty;
            if (taskInfo != null)
            {
                processInstanceId = taskInfo.ProcessInstanceId;
            }
        
            GetSrnStatusUpdateCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), "Closed", 0, processInstanceId);
        }
    }

    private void UpdateSrnStatusToPendingClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        var taskInfo = task.Get();
        var processInstanceId = string.Empty;
        
        if (taskInfo != null)
        {
            processInstanceId = taskInfo.ProcessInstanceId;
        }
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), "Closure Pending", 0, processInstanceId);
        }
    }

    private void EmailBureausAboutSrnPendingClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateCamundaService(serviceScope).EmailBureausAboutSRNPendingClosure(Convert.ToInt32(srnId));
        }
    }

    private void EmailMemberAboutSrnPendingClosure(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateCamundaService(serviceScope).EmailMemberAboutSRNPendingClosure(Convert.ToInt32(srnId));
        }
    }
    
    private static SrnStatusUpdateCamundaService GetSrnStatusUpdateCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnStatusUpdateCamundaService>();
    }
}