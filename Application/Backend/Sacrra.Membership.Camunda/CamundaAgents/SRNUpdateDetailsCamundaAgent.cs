using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Models;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnUpdateDetailsCamundaAgent: BaseCamundaAgent
{
    public SrnUpdateDetailsCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    override void Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "notify-member-of-srn-details-change-decline":
                NotifyMemberOfSrnDetailsChangeDecline(task, completeExternalTask, serviceScope);
                break;
            
            case "assign-srn-details-update-to-shm":
                AssignSrnDetailsUpdateToShm(task, completeExternalTask, serviceScope);
                break;
            
            case "notify-shm-of-srn-update":
                NotifyShmOfSrnUpdate(task, completeExternalTask, serviceScope);
                break;
            
            case "apply-srn-details-update":
                ApplySrnDetailsUpdate(task, completeExternalTask, serviceScope);
                break;
        }

        task.Complete(completeExternalTask);
    }

    private void ApplySrnDetailsUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = GetSrnUpdateDetailsCamundaService(serviceScope).GetMemberChangeRequest(requestId);

        if (changeRequest != null)
        {
            if (changeRequest.Type == ChangeObjectType.SRN)
            {
                var srnId = changeRequest.ObjectId;

                GetSrnUpdateDetailsCamundaService(serviceScope).CamundaApplySRNChanges(srnId, changeRequest);
                GetSrnUpdateDetailsCamundaService(serviceScope).NotifyApplicantOfSRNUpdateAccepted(srnId);
            }
        }
    }

    private void NotifyShmOfSrnUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetMemberVariable(task, "SRNId", serviceScope);

        if (srnId > 0)
        {
            GetSrnUpdateDetailsCamundaService(serviceScope).NotifySHMAndSACRRAAdminOfSRNUpdate(srnId);
        }
    }

    private void AssignSrnDetailsUpdateToShm(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = GetCamundaRepository(serviceScope).GetMemberChangeRequest(requestId);

        if (changeRequest != null)
        {
            if (changeRequest.Type == ChangeObjectType.SRN)
            {
                var shmId = GetSrnUpdateDetailsCamundaService(serviceScope).GetSRNStakeholderManager(changeRequest.ObjectId);

                if (shmId > 0)
                {
                    completeExternalTask.Variables = new Dictionary<string, VariableValue>
                    {
                        ["stakeHolderManagerAssignee"] = VariableValue.FromObject(shmId.ToString())
                    };
                }
            }
        }
    }

    private void NotifyMemberOfSrnDetailsChangeDecline(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = GetSrnUpdateDetailsCamundaService(serviceScope).GetMemberChangeRequest(requestId);

        if (changeRequest != null)
        {
            if (changeRequest.Type == ChangeObjectType.SRN)
            {
                var srnId = changeRequest.ObjectId;

                GetSrnUpdateDetailsCamundaService(serviceScope).NotifyApplicantOfSRNUpdateDecline(srnId);
            }
        }
    }
    
    private static SrnUpdateDetailsCamundaService GetSrnUpdateDetailsCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnUpdateDetailsCamundaService>();
    }
}