using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class ReplacementFileSubmissionCamundaAgent : BaseCamundaAgent
{
    private const string UpdateStatusDeclined = "replacement_file_submission_update_status_declined";
    private const string CheckFileSubmittedToDth = "replacement_file_submission_check_file_submitted_to_dth";
    private const string UpdateStatusApproved = "replacement_file_submission_update_status_approved";
    private const string EmailMemberBureausApproved = "replacement_file_submission_email_member_bureaus_approved";
    private const string CreateFileFtpServer = "replacement_file_submission_create_file_ftpserver";
    private const string EmailMemberDeclined = "replacement_file_submission_email_member_declined";
    private const string UpdateSubmittedCancelled = "replacement_file_submission_update_submitted_cancelled";
    private const string EmailMemberBureausCancelled = "replacement_file_submission_email_member_bureaus_cancelled";

    public ReplacementFileSubmissionCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

    override void Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var camundaTask = task.Get();

        switch (topicName)
        {
            case UpdateStatusDeclined:
                UpdateStatusToDeclined(camundaTask.ProcessInstanceId, serviceScope);
                break;
            
            case CheckFileSubmittedToDth:
                CheckIfFileSubmittedToDth(camundaTask, serviceScope);
                break;
            
            case UpdateStatusApproved: 
                UpdateStatusToApproved(camundaTask.ProcessInstanceId, serviceScope);
                break;
            
            case EmailMemberBureausApproved:
                EmailMemberBureausApproval(camundaTask.ProcessInstanceId, serviceScope);
                break;
            
            case CreateFileFtpServer:
                CreateFileOnFtpServer(camundaTask.ProcessInstanceId, serviceScope);
                break;
            
            case EmailMemberDeclined:
                EmailMemberDecline(camundaTask.ProcessInstanceId, serviceScope);
                break;
            
            case UpdateSubmittedCancelled:
                UpdateTaskSubmittedOrCancelled(camundaTask, serviceScope);
                break;
            
            case EmailMemberBureausCancelled:
                EmailMemberBureausCancel(camundaTask.ProcessInstanceId, serviceScope);
                break;
        }

        task.Complete(completeExternalTask);
    }

    private static void UpdateStatusToDeclined(string processInstanceId, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).UpdateReplacementFileSubmissionStatusToDeclined(processInstanceId);
    }

    private static void CheckIfFileSubmittedToDth(ExternalTaskInfo camundaTask, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).ReplacementFileSubmissionCheckFileSubmittedToDTH(camundaTask);
    }

    private static void UpdateStatusToApproved(string processInstanceId, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).UpdateReplacementFileSubmissionStatusToApproved(processInstanceId);
    }

    private static void EmailMemberBureausApproval(string processInstanceId, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).ReplacementFileSubmissionEmailMemberApproved(processInstanceId);
    }

    private static void CreateFileOnFtpServer(string processInstanceId, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).ReplacementFileSubmissionCreateFileOnFTPServer(processInstanceId);
    }

    private static void EmailMemberDecline(string processInstanceId, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).ReplacementFileSubmissionEmailMemberDeclined(processInstanceId);
    }

    static void UpdateTaskSubmittedOrCancelled(ExternalTaskInfo camundaTask, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).ReplacementFileSubmissionUpdateFileSubmissionToSubmittedOrCancelled(camundaTask);
    }

    private static void EmailMemberBureausCancel(string processInstanceId, IServiceScope serviceScope)
    {
        GetReplacementFileSubmissionCamundaService(serviceScope).ReplacementFileSubmissionEmailMemberBureausCancelled(processInstanceId);
    }
    
    private static ReplacementFileSubmissionCamundaService GetReplacementFileSubmissionCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<ReplacementFileSubmissionCamundaService>();
    }
}