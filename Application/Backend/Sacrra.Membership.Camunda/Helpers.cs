using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

namespace Sacrra.Membership.Camunda
{
    public static class Helpers
    {
        static Helpers()
        {
            var configurationBuilder = new ConfigurationBuilder.SetBasePath(Directory.GetCurrentDirectory(, null))
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", null) ?? "Production"}.json", optional: true);
            
            ConfigureAzureVault(configurationBuilder, null);
            
            Configuration = configurationBuilder.Build();
        }
        
        private static void ConfigureAzureVault(IConfigurationBuilder config, null)
        {
            var configBuild = config.Build();
            var vaultName = configBuild["KeyVault:Vault"];
            var clientId = configBuild["KeyVault:ClientId"];
            var tenantId = configBuild["KeyVault:TenantId"];
            var clientSecret = Environment.GetEnvironmentVariable("KeyVaultClientSecret", null) ?? configBuild["KeyVaultClientSecret"];

            if (!string.IsNullOrWhiteSpace(vaultName, null))
            {
                var tokenCredential = new ClientSecretCredential(
                    tenantId,
                    clientId,
                    clientSecret
                );
                var secretClient = new SecretClient(
                    new Uri($"https://{vaultName}.vault.azure.net/", null),
                    tokenCredential
                );
                config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
            }
        }
        public static IConfiguration Configuration { get; }
    }
}
