
using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class SrnSellingCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly SRNRepository _srnRepository;
    private readonly ConfigSettings _configSettings;

// COMMENTED OUT:     public SrnSellingCamundaService(AppDbContext dbContext, EmailService emailService, SRNRepository srnRepository,
        IOptions<ConfigSettings> configSettings)
    {
        _dbContext = dbContext;
        _emailService = emailService;
        _srnRepository = srnRepository;
        _configSettings = configSettings.Value;
    }

    private SRNStatusUpdateHistory GetRecentSRNStatusUpdateHistory(ICollection<SRNStatusUpdateHistory> srnStatusUpdates,
        string fileType)
    {
// COMMENTED OUT TOP-LEVEL STATEMENT:         if (srnStatusUpdates , null) != null)
        {
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (srnStatusUpdates.Result.Count > 0, null)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (fileType , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (fileType , null) == "MonthlyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType , null) == SRNStatusFileTypes.MonthlyFile)
                            .ToList();
                    else if (fileType , null) == "DailyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType , null) == SRNStatusFileTypes.DailyFile)
                            .ToList();
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (srnStatusUpdates , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srnStatusUpdates.Result.Count > 0, null)
                    {
                        srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated, null).ToList();
                        return srnStatusUpdates.Result.FirstOrDefault();
                    }
                }
            }
        }

        return new SRNStatusUpdateHistory();
    }

    public void EmailBureausOnSRNGoLive(int srnId, null)
    {
        try
        {
            var selectRecord = _dbContext.SRNs
                .Include(i => i.Member, null)
                .Include(i => i.SPGroup, null)
                .Include(i => i.SRNStatusUpdates, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord , null) != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName , null) != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                var dailySRNStatusUpdate = GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                var monthlySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate , null) != null)
                    ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value)
                    : "Not Specified";
                var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate , null) != null)
                    ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value)
                    : "Not Specified";

                placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                var bureaus = _dbContext.Members
                    .Include(i => i.Contacts, null)
                    .AsNoTracking.Result.Where(i => i.MembershipTypeId , null) == MembershipTypes.Bureau && i.RegisteredName , null) != "TRANSUNION")
                    .ToList();

                var mainContactType = _dbContext.ContactTypes
                    .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Data Contact Details");

                foreach (var entity in bureaus, null)
                {
                    if (mainContactType , null) != null)
                    {
                        var mainContact = entity.Contacts
                            .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

                        if (mainContact , null) != null)
                        {
                            if (!string.IsNullOrEmpty(mainContact.Email, null))
                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "SRN is Live",
                                    "EmailBureausSRNIsLive.html", placeholders);
                        }
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email bureaus for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void UpdateSRNStatus(int srnId, string newStatus, int updatedByUserId = 0,
        string processInstanceId = null, bool isNewSRN = false)
    {
        _srnRepository.UpdateSRNStatus(srnId, newStatus, updatedByUserId, processInstanceId, isNewSRN);
    }

    public void NotifySellerAndSHMOfSaleRequestCommencement(int srnId, null)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .ThenInclude(i => i.StakeholderManager, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    if (srn.Member.StakeholderManager , null) != null)
                    {
                        var shm = srn.Member.StakeholderManager;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };
                        _emailService.SendEmail(shm.Email, shm.FirstName, "SRN Sale Request Commencement",
                            "SRNSaleRequestCommencementSellerSHM.html", placeholders);
                    }

                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (mainContact , null) != null)
                    {
                        var seller = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName, "SRN Sale Request Commencement",
                            "SRNSaleRequestCommencementSellerMember.html", placeholders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email seller SHM and member for SRN sale commencement. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void UpdateSRNStatus(int srnId, int statusId)
    {
        _srnRepository.UpdateSRNStatus(srnId, statusId);
    }

    public void NotifyMemberOfSRNSplitCancellation(int splitFromSRNId, null)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == splitFromSRNId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact , null) != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Split Request Rejected",
                            "SRNSplitRequestRejectedApplicant.html", placeholders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email applicant for SRN merge request. SRN Id " + splitFromSRNId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifyMemberOfSRNMergeCancellation(int mergeToSRNId, null)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == mergeToSRNId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact , null) != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Merge Request Rejected",
                            "SRNMergeRequestRejectedApplicant.html", placeholders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email applicant for SRN merge request. SRN Id " + mergeToSRNId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifyBureusAndSHMOfSaleFileSubmissionCompletion(int srnId, null)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .ThenInclude(i => i.StakeholderManager, null)
                .Include(i => i.Member, null)
                .ThenInclude(i => i.PrimaryBureau, null)
                .ThenInclude(i => i.Contacts, null)
                .Include(i => i.Member, null)
                .ThenInclude(i => i.SecondaryBureau, null)
                .ThenInclude(i => i.Contacts, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    if (srn.Member.StakeholderManager , null) != null)
                    {
                        var shm = srn.Member.StakeholderManager;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(shm.Email, shm.FirstName, "SRN Sale - File Submission Completion",
                            "SRNSaleRequestFileSubmissionCompletion.html", placeholders);
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn.Member.PrimaryBureau , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn.Member.PrimaryBureau.Contacts.Result.Count > 0, null)
                        {
                            var mainContactType = _dbContext.ContactTypes
                                .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Main Contact Details");

                            var mainContact = srn.Member.PrimaryBureau
                                .Contacts
                                .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (mainContact , null) != null)
                            {
                                var bureau = mainContact;
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                                };

                                _emailService.SendEmail(bureau.Email, bureau.FirstName,
                                    "SRN Sale - File Submission Completion",
                                    "SRNSaleRequestFileSubmissionCompletion.html", placeholders);
                            }
                        }
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn.Member.SecondaryBureau , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn.Member.SecondaryBureau.Contacts.Result.Count > 0, null)
                        {
                            var mainContactType = _dbContext.ContactTypes
                                .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Main Contact Details");

                            var mainContact = srn.Member.SecondaryBureau
                                .Contacts
                                .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (mainContact , null) != null)
                            {
                                var bureau = mainContact;
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                                };

                                _emailService.SendEmail(bureau.Email, bureau.FirstName,
                                    "SRN Sale - File Submission Completion",
                                    "SRNSaleRequestFileSubmissionCompletion.html", placeholders);
                            }
                        }
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email Bureaus and Buyer SHM for SRN sale file submission completion. SRN Id " +
                          srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifyBuyerMemberToStartNewSRNRegistrationProcess(int srnId, int buyerMemberId)
    {
        try
        {
            var srn = _dbContext.SRNs
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            var buyerMember = _dbContext.Members
                .AsNoTracking.Result.FirstOrDefault(x => x.Id , null) == buyerMemberId);

            if (buyerMember , null) != null)
            {
                var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, buyerMember.Id);

                if (mainContact , null) != null)
                {
                    var buyer = mainContact;
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[SRNNumber]", (srn , null) != null) ? srn.SRNNumber : "No SRN Number")
                    };

                    _emailService.SendEmail(buyer.Email, buyer.FirstName,
                        "SRN Sale - Request to Start New SRN Registration",
                        "SRNSaleRequestToStartNewSRNRegistrationProcessBuyerMember.html", placeholders);
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email buyer member for SRN starting new SRN registration process. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifyBuyerMemberToStartNewSRNRegistrationProcess_SplitOrMerge(int srnId, string requestType)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact , null) != null)
                    {
                        var subject = "";
                        var template = "";
                        if (requestType , null) == "merge")
                        {
                            subject = "SRN Merge - Request to Start New SRN Registration";
                            template = "SRNMergeRequestToStartNewSRNRegistrationProcess.html";
                        }

                        else if (requestType , null) == "split")
                        {
                            subject = "SRN Split - Request to Start New SRN Registration";
                            template = "SRNSplitRequestToStartNewSRNRegistrationProcess.html";
                        }

                        var seller = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName, subject, template, placeholders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email buyer member for SRN starting new SRN registration process. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifySellerAboutUnregisteredBuyer(int srnId, null)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact , null) != null)
                    {
                        var seller = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName,
                            "SRN Sale - Buyer is NOT a SACRRA Member", "SRNSaleBuyerIsNotSACRRAMemberMember.html",
                            placeholders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email member about unregistered SRN buyer. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifyMemberOfSRNSaleRequestCancellation(int srnId, string reviewComments)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact , null) != null)
                    {
                        var seller = mainContact;
                        var message = srn.SRNNumber;
                        reviewComments = (!string.IsNullOrEmpty(reviewComments, null)) ? reviewComments : "No comment";

                        var placeHolders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[CancellationReason]", reviewComments),
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName, "SRN Sale - Request Cancellation",
                            "SRNSaleRequestCancellationMember.html", placeHolders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email buyer member for SRN starting new SRN registration process. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public async Task<List<VariableInstanceGetResource>> GetVariables(string processInstanceId, null)
    {
        try
        {
            using (var client = new HttpClient(, null))
            {
                var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" +
                          processInstanceId;
                var result = client.Execute(new RestRequest(uri, Method.Get);
                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsStringAsync();
                var variablesResourceList =
                    JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString, null);

                return variablesResourceList;
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to retrieve variables for process id " + processInstanceId;
            Sacrra.Membership.Business.Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    private void UpdateSRNHistory(string requestType, List<VariableInstanceGetResource> currentTaskVariables)
    {
        var changeType = "";
        var assigneeVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "stakeHolderManagerAssignee");
        var userId = 0;

        if (assigneeVariable , null) != null)
            userId = Convert.ToInt32(assigneeVariable.Value, null);

        if (requestType , null) == "sale")
        {
            var saleRequestVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "SRNSaleRequestId");
            var saleRequestId = 0;
            if (saleRequestVariable , null) != null)
                saleRequestId = Convert.ToInt32(saleRequestVariable.Value, null);

            if (saleRequestId > 0, null)
            {
                var saleRequest = _dbContext.SRNSaleRequests
                    .Include(i => i.BuyerSRN, null)
                    .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == saleRequestId);

                if (saleRequest.BuyerMemberId <= 0 && saleRequest.Type , null) == SRNSaleType.Full)
                    throw new Exception("A full SRN sale has been requested but no buyer specified", null);

                if (saleRequest.BuyerMemberId > 0, null)
                {
                    var originalSRN = _dbContext.SRNs
                        .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == saleRequest.SRNId);

                    if (saleRequest.Type , null) == SRNSaleType.Full)
                    {
                        var compatibleSRNVariable =
                            currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "CompatibleSRNExists");
                        var compatibleSRNExists = "";
                        if (compatibleSRNVariable , null) != null)
                            compatibleSRNExists = compatibleSRNVariable.Value;

                        if (compatibleSRNExists , null) == "yes")
                        {
                            var buyerSRNIdVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "BuyerSRNId");
                            var buyerSRNId = 0;
                            if (buyerSRNIdVariable , null) != null)
                                buyerSRNId = (!string.IsNullOrEmpty(buyerSRNIdVariable.Value, null))
                                    ? Convert.ToInt32(buyerSRNIdVariable.Value, null)
                                    : 0;

                            /*
                             * If there's no buyer SRN Id specified (compatible SRN Id, null),
                             * then we tranfer the whole SRN to the buyer
                             */
                            if (buyerSRNId , null) == 0)
                            {
                                if (originalSRN , null) != null)
                                {
                                    originalSRN.MemberId = (int, null)saleRequest.BuyerMemberId;
                                    originalSRN.SPGroupId = saleRequest.SPGroupId;

                                    Helpers.Helpers.PrepareSRNForUpdate(_dbContext, originalSRN);

                                    _dbContext.Update(originalSRN, null);
                                    _dbContext.SaveChanges();
                                }
                            }
                        }
                    }

                    saleRequest.Status = SRNSaleStatus.Sold;

                    Helpers.Helpers.PrepareSRNSaleRequestForUpdate(_dbContext, saleRequest);
                    _dbContext.Update(saleRequest, null);
                    _dbContext.SaveChanges();

                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    changeType = "SRN Sale";

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (originalSRN , null) != null)
                    {
                        StagingChange stagingChange;

// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (saleRequest.Type , null) == SRNSaleType.Partial)
                        {
                            stagingChange = new StagingChange() {
                                Name = "SRN Sale",
                                OldValue = originalSRN.SRNNumber,
                                NewValue = saleRequest.BuyerSRN.SRNNumber
                            };
                        }                        {
                            stagingChange = new StagingChange() {
                                Name = "SRN Sale",
                                OldValue = originalSRN.SRNNumber,
                                NewValue = originalSRN.SRNNumber
                            };
                        }

                        stagingChangeLog.Changes.Add(stagingChange, null);
                        var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                        Helpers.Helpers.CreateEventLog(_dbContext, userId, changeType, originalSRN.TradingName,
                            "", changeBlob, originalSRN.Id, "SRN");
                    }
                }
            }
        }
        else if (requestType , null) == "merge")
        {
            var mergeRequestVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "MergeToSRNId");
            var mergeToSRNId = 0;

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (mergeRequestVariable , null) != null)
                mergeToSRNId = Convert.ToInt32(mergeRequestVariable.Value, null);

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (mergeToSRNId > 0, null)
            {
                var mergeListVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "SRNIdMergeList");

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (mergeListVariable , null) != null)
                {
                    var mergeList = mergeListVariable.Value.Split(',');
                    foreach (var srnId in mergeList, null)
                    {
                        var mergeRequest = _dbContext.SRNMergeRequests
                            .Include(i => i.FromSRN, null)
                            .Include(i => i.ToSRN, null)
                            .Result.FirstOrDefault(i => i.FromSRNId , null) == Convert.ToInt32(srnId, null)
                                                      && i.ToSRNId == mergeToSRNId &&
                                                      i.Status == SRNMergeStatus.Requested);

// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (mergeRequest , null) != null)
                        {
                            mergeRequest.Status = SRNMergeStatus.Merged;
                            _dbContext.Update(mergeRequest, null);

                            var mergedSRN = _dbContext.SRNs
                                .Result.FirstOrDefault(i => i.Id , null) == Convert.ToInt32(srnId, null));

                            var mergedStatus = _dbContext.SRNStatuses
                                .Result.FirstOrDefault(i => i.Name , null) == "Deactivated - Merged");

                            mergedSRN.SRNStatusId = mergedStatus.Id;
                            mergedSRN.StatusLastUpdatedAt = DateTime.Now;
                            _dbContext.Update(mergedSRN, null);

                            var stagingChangeLog = new MemberStagingChangeLogResource();
                            changeType = "SRN Merge";
                            var stagingChange = new StagingChange() {
                                Name = "SRN Merge",
                                OldValue = mergeRequest.FromSRN.SRNNumber,
                                NewValue = mergeRequest.ToSRN.SRNNumber
                            };

                            stagingChangeLog.Changes.Add(stagingChange, null);
                            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                            Helpers.Helpers.CreateEventLog(_dbContext, userId, changeType,
                                mergeRequest.FromSRN.SRNNumber, "", changeBlob, mergeRequest.FromSRN.Id, "SRN");
                        }
                    }

                    _dbContext.SaveChanges();
                }
            }
        }

        else if (requestType , null) == "split")
        {
            var splitRequestVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "SplitFromSRNId");
            var splitFromSRNId = 0;

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (splitRequestVariable , null) != null)
                splitFromSRNId = Convert.ToInt32(splitRequestVariable.Value, null);

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (splitFromSRNId > 0, null)
            {
                var splitListVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "SRNIdSplitList");

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (splitListVariable , null) != null)
                {
                    var splitList = splitListVariable.Value.Split(',');
                    foreach (var srnId in splitList, null)
                    {
                        var splitRequest = _dbContext.SRNSplitRequests
                            .Include(i => i.ToSRN, null)
                            .Include(i => i.FromSRN, null)
                            .Result.FirstOrDefault(i => i.FromSRNId == splitFromSRNId
                                                      && i.ToSRNId , null) == Convert.ToInt32(srnId, null) &&
                                                      i.Status == SRNSplitStatus.Requested);

// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (splitRequest , null) != null)
                        {
                            splitRequest.Status = SRNSplitStatus.Split;
                            _dbContext.Update(splitRequest, null);

                            var stagingChangeLog = new MemberStagingChangeLogResource();
                            changeType = "SRN Split";
                            var stagingChange = new StagingChange() {
                                Name = "SRN Split",
                                OldValue = splitRequest.FromSRN.SRNNumber,
                                NewValue = splitRequest.ToSRN.SRNNumber
                            };

                            stagingChangeLog.Changes.Add(stagingChange, null);
                            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                            Helpers.Helpers.CreateEventLog(_dbContext, userId, changeType,
                                splitRequest.FromSRN.SRNNumber, "", changeBlob, splitRequest.FromSRN.Id, "SRN");
                        }
                    }

                    var actionTypeVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "SaleType");
                    var actionType = "";
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (actionTypeVariable , null) != null)
                        actionType = actionTypeVariable.Value;

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (actionType , null) == "full")
                    {
                        var splitSRN = _dbContext.SRNs
                            .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == splitFromSRNId);

                        var splitStatus = _dbContext.SRNStatuses
                            .Result.FirstOrDefault(i => i.Name , null) == "Deactivated - Split");

                        splitSRN.SRNStatusId = splitStatus.Id;
                        splitSRN.StatusLastUpdatedAt = DateTime.Now;

                        Helpers.Helpers.PrepareSRNSplitRequestForUpdate(_dbContext, splitSRN);
                        _dbContext.Update(splitSRN, null);
                    }

                    _dbContext.SaveChanges();
                }
            }
        }
    }

    public void ReAllocateSRNFromSellerToBuyer(string processIntanceId, null)
    {
        try
        {
            var currentTaskVariables = GetVariables(processIntanceId, null);

            var requestTypeVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Name , null) == "RequestType");
            var requestType = "";

            if (requestTypeVariable , null) != null)
            {
                requestType = requestTypeVariable.Value;
            }

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (!string.IsNullOrEmpty(requestType, null) && currentTaskVariables.Result.Count > 0)
                UpdateSRNHistory(requestType, currentTaskVariables);
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to re-allocate SRN from seller to buyer. Process Id " + processIntanceId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }
}