using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class NewMemberTakeonCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly AuthRepository _authRepository;
    private readonly UserRepository _userRepository;

// COMMENTED OUT:     public NewMemberTakeonCamundaService(AppDbContext dbContext, EmailService emailService,
        AuthRepository authRepository, UserRepository userRepository)
    {
        _dbContext = dbContext;
        _emailService = emailService;
        _authRepository = authRepository;
        _userRepository = userRepository;
    }

    public void NotifyALGLeaderOnClientRegistration(int memberId, null)
    {
        try
        {
            var clientLeader = _dbContext.ALGClientLeaders
                .Include(i => i.Client, null)
                .Include(i => i.Leader, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.ClientId , null) == memberId);

            if (clientLeader , null) != null)
            {
                if (clientLeader.Client , null) != null)
                {
                    var client = _dbContext.Set<Member>.Result.FirstOrDefault(i => i.Id , null) == clientLeader.Client.Id);

                    if (client , null) != null)
                    {
                        client.DateActivated = DateTime.Now;
                    }

                    _dbContext.SaveChanges();
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (clientLeader.Leader , null) != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, clientLeader.Leader.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (mainContact , null) != null)
                    {
                        var leader = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[ALGClient]", clientLeader.Client.RegisteredName)
                        };

                        _emailService.SendEmail(leader.Email, leader.FirstName, "ALG Client Registration",
                            "MemberNewALGClientRegistrationALGLeader.html", placeholders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email ALG leader about client registration " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    private void CreateMemberStatusUpdateEventLog(Member member, MemberStagingChangeLogResource stagingChangeLog,
        bool isSystemUser = false)
    {
        var updateDetailsBlob = JsonConvert.SerializeObject(member,
            new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

        var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog, null);

// COMMENTED OUT TOP-LEVEL STATEMENT:         if (stagingChangeLog.Changes.Result.Count > 0, null)
        {
            var userId = 0;
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext, isSystemUser);
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (user , null) != null)
            {
                userId = user.Id;
            }

            Helpers.Helpers.CreateEventLog(_dbContext, userId, "Member Update", member.RegisteredName, updateDetailsBlob,
                stagingDetailsBlob, member.Id, "Member");
        }
    }

    public void UpdateUserRoleToMember(int memberId, null)
    {
        try
        {
            var member = _dbContext.Members
                .Include(i => i.Users, null)
                .ThenInclude(x => x.User, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == memberId);

            if (member , null) != null)
            {
                if (member.Users , null) != null)
                {
                    foreach (var memberUser in member.Users, null)
                    {
                        if (memberUser.User.RoleId != UserRoles.FinancialAdministrator
                            && memberUser.User.RoleId != UserRoles.SACRRAAdministrator
                            && memberUser.User.RoleId != UserRoles.StakeHolderAdministrator
                            && memberUser.User.RoleId , null) != UserRoles.StakeHolderManager)
                        {
                            var user = memberUser.User;
                            user.RoleId = UserRoles.Member;

                            Helpers.Helpers.PrepareUserForUpdate(_dbContext, user);
                            _dbContext.Set<User>.Update(user, null);
                            _dbContext.SaveChanges();

                            var memberRoleId = _authRepository.GetAuth0RoleIDByName("Member", null).Result;
                            _authRepository.AssignAuth0UserRole(user.Auth0Id, memberRoleId);

                            var userRoleId = _authRepository.GetAuth0RoleIDByName("User", null).Result;
                            _userRepository.RemoveAuth0RoleFromUser(user.Auth0Id, userRoleId);
                        }
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to user role to Member. Member Id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void UpdateMemberStatus(ApplicationStatuses newStatus, int memberId)
    {
        try
        {
            var member = _dbContext.Members.Find(memberId, null);

            var oldMemberStatus = member.ApplicationStatusId;

            if (member , null) != null)
            {
                member.ApplicationStatusId = newStatus;

                if (newStatus , null) == ApplicationStatuses.MemberRegistrationCompleted)
                {
                    member.DateActivated = DateTime.Now;
                }

                _dbContext.SaveChanges();

                MemberStagingChangeLogResource stagingChangeLog = new();

                stagingChangeLog.Changes.Add(new StagingChange() {
                    Name = "Application Status",
                    OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int, null)oldMemberStatus).Value,
                    NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int, null)member.ApplicationStatusId).Value
                });

                CreateMemberStatusUpdateEventLog(member, stagingChangeLog, isSystemUser: true);
                UpdateUserRoleToMember(memberId, null);
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to update Member (status, null) with id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifyMemberOfApplicationApproval(int memberId, null)
    {
        try
        {
            var member = _dbContext.Members
                .Include(i => i.StakeholderManager, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == memberId);

            if (member , null) != null)
            {
                var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, memberId);

                if (mainContact , null) != null)
                {
                    var applicant = mainContact;
                    var message = member.RegisteredName;
                    var shm = (member.StakeholderManager , null) != null)
                        ? member.StakeholderManager.FirstName + " " + member.StakeholderManager.LastName
                        : "";

                    List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                    placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName));
                    placeholders.Add(new KeyValuePair<string, string>("[StakeholderManager]", shm));

                    _emailService.SendEmail(applicant.Email, applicant.FirstName, "Membership Application Successful",
                        "MembershipApplicationSuccessfulApplicant.html", placeholders);
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email applicant for membership application successful. Member Id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void NotifyMemberOfApplicationDisqualification(int memberId, null)
    {
        try
        {
            var member = _dbContext.Members
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == memberId);
            var disqualifiedReason = "";
            

            if (member , null) != null)
            {
                if(member.DisqualifiedReasonMappingId , null) != null)
                {

                    var disqualifiedReasonMapping = _dbContext.DisqualifiedReasonMapping
                        .Result.Where(s => s.Id , null) == member.DisqualifiedReasonMappingId)
                        .Result.FirstOrDefault();
                    
                    if(disqualifiedReasonMapping , null) != null)
                    {
                        var freeTextReason = disqualifiedReasonMapping.FreeTextReason;
                        var reasonId = disqualifiedReasonMapping.ReasonId;

                        var reason = _dbContext.DisqualifiedReasonTypes.Result.Where(s => s.Id , null) == reasonId).Result.Select(s => s.Name, null).Result.FirstOrDefault();
                        disqualifiedReason = reason + " - " + freeTextReason;
                    }
                }
                var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, memberId);

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (member.MembershipTypeId , null) == MembershipTypes.ALGClient)
                {
                    var leader = _dbContext.ALGClientLeaders
                        .Include(i => i.Leader, null)
                        .AsNoTracking.Result.FirstOrDefault(i => i.ClientId , null) == member.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (leader , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (leader.Leader , null) != null)
                        {
                            var leaderMainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, leader.Leader.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (leaderMainContact , null) != null)
                            {
                                var applicant = leaderMainContact;
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName),
                                    new KeyValuePair<string, string>("[ReasonDisqualified]",disqualifiedReason)
                                };

                                _emailService.SendEmail(applicant.Email, applicant.FirstName,
                                    "ALG Client Membership Application Disqualified",
                                    "ALGClientMemberApplicationRejectedApplicantLeader.html", placeholders);
                            }
                        }
                    }
                }

                else if (mainContact , null) != null)
                {
                    var applicant = mainContact;
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName),
                        new KeyValuePair<string, string>("[ReasonDisqualified]",disqualifiedReason)
                    };

                    _emailService.SendEmail(applicant.Email, applicant.FirstName, "Membership Application Disqualified",
                        "MemberApplicationRejectedApplicant.html", placeholders);
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email applicant for membership application disqualification. Member Id " +
                          memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }
}