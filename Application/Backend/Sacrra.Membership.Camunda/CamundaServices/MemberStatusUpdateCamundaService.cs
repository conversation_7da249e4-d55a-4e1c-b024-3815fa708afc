using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class MemberStatusUpdateCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly UserRepository _userRepository;
    private readonly EmailService _emailService;

    public MemberStatusUpdateCamundaService(AppDbContext dbContext, UserRepository userRepository, EmailService emailService)
    {
        _dbContext = dbContext;
        _userRepository = userRepository;
        _emailService = emailService;
    }

    public void EnableOrDisableMemberUsers(int memberId, string memberStatus)
    {
        try
        {
            var member = _dbContext.Members
                .Include(i => i.Users)
                .ThenInclude(x => x.User)
                .AsNoTracking()
                .Result.FirstOrDefault(i => i.Id == memberId);

            if (member != null)
            {
                if (member.MembershipTypeId != MembershipTypes.ALGClient)
                {
                    if (member.Users != null)
                    {
                        if (member.Users.Result.Count > 0)
                        {
                            bool isBlocked = false;

                            if (memberStatus == "activated")
                                isBlocked = false;
                            else if (memberStatus == "cancelled")
                                isBlocked = true;

                            foreach (var user in member.Users)
                            {
                                //Block or activate use in Auth0
                                _userRepository.EnableOrDisableUser(user.User.Auth0Id, isBlocked);
                            }
                        }
                    }
                }
                else
                {
                    //TODO: I don't know what to do here for now
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to enable or disable member users. member id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public void EmailMemberToStateRemovalOrReactivation(int memberId, string memberStatus)
    {
        try
        {
            var member = _dbContext.Members
                .Include(i => i.MemberStatusReason)
                .Include(i => i.Contacts)
                .AsNoTracking()
                .Result.FirstOrDefault(i => i.Id == memberId);

            List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

            placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName));

            var statusReason = (member.MemberStatusReason != null) ? member.MemberStatusReason.Name : "Not Specified";
            placeholders.Add(new KeyValuePair<string, string>("[MemberStatusReason]", statusReason));

            var contactTypes = _dbContext.ContactTypes
                .AsNoTracking()
                .Result.Where(i => i.Name == "Main Contact Details"
                            || i.Name == "Alternate Contact Details"
                            || i.Name == "Financial Contact Details")
                .ToList();

            if (member != null)
            {
                if (member.MembershipTypeId == MembershipTypes.ALGClient)
                {
                    var algLeaders = _dbContext.ALGClientLeaders
                        .Include(i => i.Leader)
                        .ThenInclude(x => x.Contacts)
                        .Result.Where(i => i.ClientId == member.Id)
                        .ToList();

                    foreach (var leader in algLeaders)
                    {
                        if (leader.Leader.Contacts != null)
                        {
                            if (contactTypes != null)
                            {
                                foreach (var contactType in contactTypes)
                                {
                                    var contact = leader.Leader.Contacts
                                        .Result.FirstOrDefault(i => i.ContactTypeId == contactType.Id);

                                    if (contact != null)
                                    {
                                        if (!string.IsNullOrEmpty(contact.Email))
                                        {
                                            if (memberStatus == "cancelled")
                                                _emailService.SendEmail(contact.Email, contact.FirstName,
                                                    "Member Status Update", "EmailMemberToStateRemoval.html",
                                                    placeholders);
                                            else if (memberStatus == "activated")
                                                _emailService.SendEmail(contact.Email, contact.FirstName,
                                                    "Member Status Update", "EmailMemberToStateReactivation.html",
                                                    placeholders);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    if (member.Contacts != null)
                    {
                        if (contactTypes != null)
                        {
                            foreach (var contactType in contactTypes)
                            {
                                var contact = member.Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId == contactType.Id);

                                if (contact != null)
                                {
                                    if (!string.IsNullOrEmpty(contact.Email))
                                    {
                                        if (memberStatus == "cancelled")
                                            _emailService.SendEmail(contact.Email, contact.FirstName,
                                                "Member Status Update", "EmailMemberToStateRemoval.html", placeholders);
                                        else if (memberStatus == "activated")
                                            _emailService.SendEmail(contact.Email, contact.FirstName,
                                                "Member Status Update", "EmailMemberToStateReactivation.html",
                                                placeholders);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for member status update. member id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }
}