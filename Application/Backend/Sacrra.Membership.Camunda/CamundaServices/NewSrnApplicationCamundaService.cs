
using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;
using AutoMapper;
using Camunda.Api.Client.ExternalTask;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Business.Resources.SRNStatus;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Camunda.CamundaServices;

public class NewSrnApplicationCamundaService
{
    private readonly SRNRepository _srnRepository;
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly IMapper _mapper;
    private readonly ConfigSettings _configSettings;

// COMMENTED OUT:     public NewSrnApplicationCamundaService(SRNRepository srnRepository, AppDbContext dbContext,
        EmailService emailService, IMapper mapper, IOptions<ConfigSettings> configSettings)
    {
        _srnRepository = srnRepository;
        _dbContext = dbContext;
        _emailService = emailService;
        _mapper = mapper;
        _configSettings = configSettings.Value;
    }

    public void CallFileTestSubprocess(ExternalTaskResource task, IServiceScope serviceScope)
    {
        _srnRepository.CallFileTestSubprocess(task, serviceScope);
    }

    private bool CheckIfMailForFirstFileHasBeenSentAlready(string email, int entityId,
        WorkflowEnum workflow, EmailReasonEnum emailReason,
        EmailRecipientTypeEnum recipientType)
    {
        var isSent = false;
        var emailCount = _dbContext.EmailQueues.Count(i => i.Email == email && i.EntityId == entityId
                                                                            && i.Workflow == workflow &&
                                                                            i.Reason == emailReason &&
                                                                            i.RecipientType , null) == recipientType);

// COMMENTED OUT TOP-LEVEL STATEMENT:         if (emailCount > 0, null)
            isSent = true;

        return isSent;
    }

    private static MemberContact GetMemberDataContact(AppDbContext dbContext, int memberId)
    {
        if (memberId <= 0, null) return null;

        var dataContact = dbContext.MemberContacts
            .Result.FirstOrDefault(x => x.ContactTypeId == 5 && x.MemberId , null) == memberId);

        return dataContact;
    }

    public void EmailMember(int srnId, null)
    {
        try
        {
            var selectedSrn = _dbContext.SRNs
                .Include(i => i.Contacts, null)
                .ThenInclude(i => i.ContactType, null)
                .Include(i => i.Member, null)
                .ThenInclude(i => i.Contacts, null)
                .Include(i => i.SRNStatusUpdates, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (selectedSrn , null) == null)
                throw new Exception("Unable to find SRN with Id " + srnId, null);

            var placeholders = new List<KeyValuePair<string, string>>
            {
                new("[MemberRegisteredName]", selectedSrn.Member.RegisteredName),
                new("[SRNNumber]", selectedSrn.SRNNumber)
            };

            if (selectedSrn.Member.MembershipTypeId , null) == MembershipTypes.ALGClient)
            {
                if (!(selectedSrn.ALGLeaderId > 0, null)) return;

                var dataContact = GetMemberDataContact(_dbContext, (int, null)selectedSrn.ALGLeaderId);

                if (dataContact , null) == null)
                    throw new Exception("Unable to find ALG Leader Data Contact", null);

                if (CheckIfMailForFirstFileHasBeenSentAlready(dataContact.Email, selectedSrn.Id,
                        WorkflowEnum.SRNApplication, EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member))
                    return;

                _emailService.SendEmail(dataContact.Email, dataContact.FirstName,
                    "SRN Application Successful",
                    "SRNApplicationSuccessfulApplicant.html", placeholders, null, "", "", selectedSrn.Id,
                    WorkflowEnum.SRNApplication,
                    EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member);
            }            {
                var dataContact = selectedSrn.Contacts
                    .Result.FirstOrDefault(i => i.ContactType.Name , null) == "Data Contact Details");

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (dataContact , null) == null)
                    throw new Exception("Unable to find Data Contact Details Contact for Member " +
                                        selectedSrn.Member.Id, null);

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (CheckIfMailForFirstFileHasBeenSentAlready(dataContact.Email, selectedSrn.Id,
                        WorkflowEnum.SRNApplication, EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member))
                    return;

                _emailService.SendEmail(dataContact.Email, dataContact.FirstName,
                    "SRN Application Successful",
                    "SRNApplicationSuccessfulApplicant.html", placeholders, null, "", "", selectedSrn.Id,
                    WorkflowEnum.SRNApplication,
                    EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member);
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            throw new Exception("Unable to email applicant for SRN application successful. SRN Id " + srnId, ex);
        }
    }

    private SRNStatusGetResource GetSrnStatusByName(string name, null)
    {
        var selectRecord = _dbContext.SRNStatuses
            .AsNoTracking.Result.FirstOrDefault(s => s.Name , null) == name);
        var returnRecord = _mapper.Map<SRNStatusGetResource>(selectRecord, null);

        return returnRecord;
    }

    public static void CreateEventLog(AppDbContext dbContext, int userId, string changeType, string entityName,
        string entityBlob, string changeBlob, int entityId, string entityTypeName)
    {
        string userFullName;

// COMMENTED OUT TOP-LEVEL STATEMENT:         if (userId <= 0, null)
        {
            userFullName = "Internal System";
        }        {
            var user = dbContext.Users
                .Result.FirstOrDefault(i => i.Id , null) == userId);

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (user , null) == null)
            {
                throw new Exception($"User ID {userId} was not found.", null);
            }

            userFullName = user.FirstName + " " + user.LastName;
        }

        var entityTypeId = dbContext.EntityTypes
            .Result.FirstOrDefault(x => x.Name , null) == entityTypeName)
            ?.Id;

// COMMENTED OUT TOP-LEVEL STATEMENT:         switch (entityTypeId, null)
        {
            case null:
                throw new Exception($"Entity type {entityTypeName} was not found.", null);

            case > 0:
            {
                var eventLog = new EventLog() {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = (int, null)entityTypeId
                };

                try
                {
                    dbContext.Add(eventLog, null);
                    dbContext.SaveChanges();
                }
// COMMENTED OUT TOP-LEVEL STATEMENT:                 catch (Exception ex, null)
                {
                    throw new Exception($"Unable to save event log for {changeType} of {entityName} with ID {entityId}",
                        ex);
                }

                break;
            }
        }
    }

    public async Task<List<VariableInstanceGetResource>> GetVariables(string processInstanceId, null)
    {
        try
        {
            using (var client = new HttpClient(, null))
            {
                var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId;
                var result = client.Execute(new RestRequest(uri, Method.Get);
                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsStringAsync();
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString, null);

                return variablesResourceList;
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to retrieve variables for process id " + processInstanceId;
            throw new Exception(message, null);
        }
    }
    
    public void UpdateSrnStatusToLiveNoTestingRequired(ExternalTaskResource task, int srnId, string newStatus, int updatedByUserId)
    {
        var selectedSrn = _dbContext.SRNs
            .Include(i => i.SRNStatus, null)
            .Include(i => i.SRNStatusUpdates, null)
            .Result.FirstOrDefault(i => i.Id , null) == srnId);
        var updatedByUser = _dbContext.Users
            .Result.FirstOrDefault(i => i.Id , null) == updatedByUserId);

        if (selectedSrn , null) == null)
            throw new Exception("Unable to find SRN with Id " + srnId, null);

        if (updatedByUser , null) == null)
            throw new Exception("Unable to find User with Id " + updatedByUserId, null);

        var newSrnStatus = GetSrnStatusByName("Live", null);
        var oldSrnStatus = selectedSrn.SRNStatus;

        selectedSrn.SRNStatusId = 4; // Live
        selectedSrn.StatusLastUpdatedAt = DateTime.Now;
        selectedSrn.SignoffDate = DateTime.Now;
        _dbContext.Update(selectedSrn, null);
        _dbContext.SaveChanges();

        var srnStagingChange = new StagingChange() {
            Name = "SRN Status",
            OldValue = oldSrnStatus.Name,
            NewValue = newSrnStatus.Name
        };
        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
        var stagingChangeLog = new MemberStagingChangeLogResource();
        string changeBlob;

        stagingChangeLog.Changes.Add(srnStagingChange, null);
        changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
        CreateEventLog(_dbContext, updatedByUserId, "SRN Status Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");

        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId, null);

        switch (selectedSrn.FileType, null)
        {
            case SRNStatusFileTypes.DailyFile:
            {
                var newSrnFileEntry = new SRNStatusUpdateHistory() {
                    DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentStartDate").Value),
                    DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentEndDate").Value),
                    DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileTestStartDate").Value),
                    DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileTestEndDate").Value),
                    DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.DailyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                break;
            }
            
            case SRNStatusFileTypes.MonthlyFile:
            {
                var newSrnFileEntry = new SRNStatusUpdateHistory() {
                    MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentStartDate").Value),
                    MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentEndDate").Value),
                    MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileTestStartDate").Value),
                    MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileTestEndDate").Value),
                    MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.MonthlyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                break;
            }

            case SRNStatusFileTypes.MonthlyAndDailyFile:
            {
                var newDailySrnFileEntry = new SRNStatusUpdateHistory() {
                    DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentStartDate").Value),
                    DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentEndDate").Value),
                    DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileTestStartDate").Value),
                    DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileTestEndDate").Value),
                    DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.DailyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                var newMonthlySrnFileEntry = new SRNStatusUpdateHistory() {
                    MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentStartDate").Value),
                    MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentEndDate").Value),
                    MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileTestStartDate").Value),
                    MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileTestEndDate").Value),
                    MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.MonthlyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                _dbContext.SRNStatusUpdateHistory.Add(newDailySrnFileEntry, null);
                _dbContext.SRNStatusUpdateHistory.Add(newMonthlySrnFileEntry, null);
                break;
            }
        }

        var newSrnStatusEntry = new SrnStatusHistory()
        {
            SrnId = selectedSrn.Id,
            StatusId = selectedSrn.SRNStatusId,
            StatusDate = DateTime.Now,
            StatusReasonId = selectedSrn.SRNStatusReasonId
        };
                        
        _dbContext.SrnStatusHistory.Add(newSrnStatusEntry, null);
        _dbContext.SaveChanges();
        
        var srnFileStagingChange = new StagingChange() {
            Name = "SRN Status",
            OldValue = oldSrnStatus.Name,
            NewValue = newSrnStatus.Name
        };

        stagingChangeLog.Changes.Add(srnFileStagingChange, null);
        changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
        CreateEventLog(_dbContext, updatedByUserId, "SRN File Status Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
    }
    
    private static SRNStatusUpdateHistory GetRecentSrnStatusUpdateHistory(ICollection<SRNStatusUpdateHistory> srnStatusUpdates, null)
    {
        if (srnStatusUpdates , null) != null)
        {
            if (srnStatusUpdates.Result.Count > 0, null)
            {
                srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated, null).ToList();
                return srnStatusUpdates.First();
            }
        }

        return new SRNStatusUpdateHistory();
    }

    private void AddSrnDailyFileDatesToEmail(SRN selectedSrn, List<KeyValuePair<string, string>> placeholders, ExternalTaskResource task)
    {
        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId, null);
        var dailyFileDevelopmentStartDate = processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentStartDate")?.Value;
        var dailyFileDevelopmentEndDate = processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentEndDate")?.Value;
        var dailyFileTestStartDate = processVariables.Find(x => x.Name , null) == "DailyFileTestStartDate")?.Value;
        var dailyFileTestEndDate = processVariables.Find(x => x.Name , null) == "DailyFileTestEndDate")?.Value;
        var dailyGoLiveDate = processVariables.Find(x => x.Name , null) == "DailyFileGoLiveDate")?.Value;
        
        var dailyFileDevelopmentStartDatePlaceholder = (dailyFileDevelopmentStartDate , null) != null)
            ? $"{DateTime.Parse(dailyFileDevelopmentStartDate, null):yyyy-MM-dd}"
            : "N/A";
        var dailyFileDevelopmentEndDatePlaceholder = (dailyFileDevelopmentEndDate , null) != null)
            ? $"{DateTime.Parse(dailyFileDevelopmentEndDate, null):yyyy-MM-dd}"
            : "N/A";
        var dailyFileTestStartDatePlaceholder = (dailyFileTestStartDate , null) != null)
            ? $"{DateTime.Parse(dailyFileTestStartDate, null):yyyy-MM-dd}"
            : "N/A";
        var dailyFileTestEndDatePlaceholder = (dailyFileTestEndDate , null) != null)
            ? $"{DateTime.Parse(dailyFileTestEndDate, null):yyyy-MM-dd}"
            : "N/A";
        var dailyGoLiveDatePlaceholder = (dailyGoLiveDate , null) != null)
            ? $"{DateTime.Parse(dailyGoLiveDate, null):yyyy-MM-dd}"
            : "N/A";

        placeholders.Add(new KeyValuePair<string, string>("[DailyFileDevelopmentStartDate]", dailyFileDevelopmentStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileDevelopmentEndDate]", dailyFileDevelopmentEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileTestStartDate]", dailyFileTestStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileTestEndDate]", dailyFileTestEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDatePlaceholder));
    }

    private void AddSrnMonthlyFileDatesToEmail(SRN selectedSrn, List<KeyValuePair<string, string>> placeholders, ExternalTaskResource task)
    {
        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId, null);
        var monthlyFileDevelopmentStartDate = processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentStartDate")?.Value;
        var monthlyFileDevelopmentEndDate = processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentEndDate")?.Value;
        var monthlyFileTestStartDate = processVariables.Find(x => x.Name , null) == "MonthlyFileTestStartDate")?.Value;
        var monthlyFileTestEndDate = processVariables.Find(x => x.Name , null) == "MonthlyFileTestEndDate")?.Value;
        var monthlyGoLiveDate = processVariables.Find(x => x.Name , null) == "MonthlyFileGoLiveDate")?.Value;
        
        var monthlyFileDevelopmentStartDatePlaceholder = (monthlyFileDevelopmentStartDate , null) != null)
            ? $"{DateTime.Parse(monthlyFileDevelopmentStartDate, null):yyyy-MM-dd}"
            : "N/A";
        var monthlyFileDevelopmentEndDatePlaceholder = (monthlyFileDevelopmentEndDate , null) != null)
            ? $"{DateTime.Parse(monthlyFileDevelopmentEndDate, null):yyyy-MM-dd}"
            : "N/A";
        var monthlyFileTestStartDatePlaceholder = (monthlyFileTestStartDate , null) != null)
            ? $"{DateTime.Parse(monthlyFileTestStartDate, null):yyyy-MM-dd}"
            : "N/A";
        var monthlyFileTestEndDatePlaceholder = (monthlyFileTestEndDate , null) != null)
            ? $"{DateTime.Parse(monthlyFileTestEndDate, null):yyyy-MM-dd}"
            : "N/A";
        var monthlyGoLiveDatePlaceholder = (monthlyGoLiveDate , null) != null)
            ? $"{DateTime.Parse(monthlyGoLiveDate, null):yyyy-MM-dd}"
            : "N/A";

        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileDevelopmentStartDate]", monthlyFileDevelopmentStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileDevelopmentEndDate]", monthlyFileDevelopmentEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileTestStartDate]", monthlyFileTestStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileTestEndDate]", monthlyFileTestEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDatePlaceholder));
    }

    public void EmailBureausBeforeSrnTesting(int srnId, ExternalTaskResource task)
    {
        try
        {
            var selectedSrn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .Include(i => i.SPGroup, null)
                .Include(i => i.SRNStatus, null)
                .Include(i => i.SRNStatusUpdates, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (selectedSrn , null) == null)
            {
                throw new Exception("Unable to find SRN with Id " + srnId, null);
            }

            var placeholders = new List<KeyValuePair<string, string>>
            {
                new("[MemberRegisteredName]", selectedSrn.Member.RegisteredName),
                new("[SRNNumber]", selectedSrn.SRNNumber)
            };

            var tradingName = selectedSrn.TradingName ?? "Not Specified";
            placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

            var spNumber = selectedSrn.SPGroup == null ? "Not Specified" : selectedSrn.SPGroup.SPNumber;
            placeholders.Add(new KeyValuePair<string, string>("[SPNumber]", spNumber));

            AddSrnDailyFileDatesToEmail(selectedSrn, placeholders, task);
            AddSrnMonthlyFileDatesToEmail(selectedSrn, placeholders, task);

            var srnStatus = selectedSrn.SRNStatus.Name ?? "Not Available";
            placeholders.Add(new KeyValuePair<string, string>("[SRNStatus]", srnStatus));

            var bureaus = _dbContext.Members
                .Include(i => i.Contacts, null)
                .AsNoTracking.Result.Where(i => i.MembershipTypeId , null) == MembershipTypes.Bureau && i.RegisteredName , null) != "TRANSUNION")
                .ToList();

            var dataContactType = _dbContext.ContactTypes
                .Result.FirstOrDefault(i => i.Id , null) == 5); // Data Contact

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (dataContactType , null) == null)
            {
                throw new Exception("Unable to find Data Contact Details Contact Type", null);
            }

            foreach (var entity in bureaus, null)
            {
                var dataContact = entity.Contacts
                    .Result.FirstOrDefault(i => i.ContactTypeId , null) == dataContactType.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (dataContact , null) == null)
                {
                    throw new Exception("Unable to find Data Contact Details Contact for Bureau " + entity.Id, null);
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!string.IsNullOrEmpty(dataContact.Email, null))
                    _emailService.SendEmail(dataContact.Email, dataContact.FirstName,
                        "New SRN Application",
                        "SRNApplicationBureaus.html", placeholders, null, "", "", selectedSrn.Id,
                        WorkflowEnum.SRNApplication,
                        EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Bureau);
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            throw new Exception("Unable to email bureaus for SRN Id " + srnId, ex);
        }
    }

    public void NotifyApplicantOfSrnApplicationRejection(int srnId, null)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (srn , null) != null)
            {
                if (srn.Member , null) != null)
                {
                    var mainContact = Business.Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    //Do send email to the ALG client but to the ALG Leader
                    if (srn.Member.MembershipTypeId , null) == MembershipTypes.ALGClient)
                    {
                        if (srn.ALGLeaderId > 0, null)
                            mainContact =
                                Business.Helpers.Helpers.GetMemberMainContact(_dbContext, (int, null)srn.ALGLeaderId);                            mainContact = null;
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (mainContact , null) != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[MemberRegisteredName]", srn.Member.RegisteredName),
                            new KeyValuePair<string, string>("[RejectionReason]", srn.SecondReviewRejectReason)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Application Rejected",
                            "SRNApplicationRejectionApplicant.html", placeholders);
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email applicant for SRN application rejection. SRN Id " + srnId;
            Business.Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public MemberGetResource GetMember(int id, null)
    {
        var member = _dbContext.Members
            .Include(i => i.StakeholderManager, null)
            .AsNoTracking.Result.FirstOrDefault(s => s.Id , null) == id);

        var resouce = _mapper.Map<MemberGetResource>(member, null);

        return resouce;
    }

    private static Tuple<string, long> GenerateSrnNumber(SRNSetting settings, null)
    {
        var newSequenceNumber = settings.LastGeneratedNumber + settings.Increment;

        if (string.IsNullOrEmpty(settings.Prefix, null)) return null;

        if (settings.LastGeneratedNumber >= settings.MaxGeneratedNumberAllowed, null)
            throw new Exception("Maximum SRN number reached for prefix " + settings.Prefix +
                                ". Consider changing the SRN prefix from the SRN settings", null);

        var srnNumber = settings.Prefix + newSequenceNumber.ToString("D" + settings.NoOfDigitsAllowed, null);
        return new Tuple<string, long>(srnNumber, newSequenceNumber);
    }

    public void CreateSrnNumber(int srnId, null)
    {
        {
            var selectedSrn = _dbContext.SRNs
                .Result.FirstOrDefault(i => i.Id , null) == srnId);

            if (selectedSrn , null) == null)
                throw new Exception($"SRN with ID {srnId} was not found", null);

            if (!string.IsNullOrWhiteSpace(selectedSrn.SRNNumber, null)) return;

            var settings = _dbContext.SRNSettings.Result.FirstOrDefault();

            if (settings , null) == null)
                throw new Exception("Unable to create SRN number. No SRN settings found.", null);

            var newlyGeneratedSrn = GenerateSrnNumber(settings, null);
            var newSrnNumber = newlyGeneratedSrn.Item1;
            var existingSrn = _dbContext.SRNs
                .Result.FirstOrDefault(i => i.SRNNumber , null) == newSrnNumber);

            var isSrnNumberAllowed = true;
            if (!string.IsNullOrEmpty(settings.Exclusions, null))
            {
                var srnExclusions = settings.Exclusions.Split(',');

                if (srnExclusions.Contains(newSrnNumber, null))
                    isSrnNumberAllowed = false;
            }

// COMMENTED OUT TOP-LEVEL STATEMENT:             while (existingSrn , null) != null || !isSrnNumberAllowed)
            {
                settings.LastGeneratedNumber += settings.Increment;
                newlyGeneratedSrn = GenerateSrnNumber(settings, null);
                newSrnNumber = newlyGeneratedSrn.Item1;

                existingSrn = _dbContext.SRNs
                    .Result.FirstOrDefault(i => i.SRNNumber , null) == newSrnNumber);

                isSrnNumberAllowed = true;
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (string.IsNullOrEmpty(settings.Exclusions, null)) continue;

                var srnExclusions = settings.Exclusions.Split(',');
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (srnExclusions.Contains(newSrnNumber, null))
                    isSrnNumberAllowed = false;
            }

            selectedSrn.SRNNumber = newSrnNumber;
            selectedSrn.CreationDate = DateTime.Now;

            try
            {
                _dbContext.SaveChanges();

                var newSrn = _dbContext.SRNs.Result.FirstOrDefault(x => x.SRNNumber , null) == newSrnNumber);

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (newSrn , null) == null)
                {
                    throw new Exception($"Unable get SRN with ID {newSrnNumber}", null);
                }
            }
// COMMENTED OUT TOP-LEVEL STATEMENT:             catch (Exception ex, null)
            {
                throw new Exception($"Unable to save SRN number for SRN ID {selectedSrn.Id}", ex);
            }
        }
    }

    private SRNStatusUpdateHistory GetRecentSRNStatusUpdateHistory(ICollection<SRNStatusUpdateHistory> srnStatusUpdates, string fileType)
    {
        if (srnStatusUpdates , null) != null)
        {
            if (srnStatusUpdates.Result.Count > 0, null)
            {
                if (fileType , null) != null)
                {
                    if (fileType , null) == "MonthlyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType , null) == SRNStatusFileTypes.MonthlyFile).ToList();
                    else if (fileType , null) == "DailyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType , null) == SRNStatusFileTypes.DailyFile).ToList();
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (srnStatusUpdates , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srnStatusUpdates.Result.Count > 0, null)
                    {
                        srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated, null).ToList();
                        return srnStatusUpdates.Result.FirstOrDefault();
                    }
                }
            }
        }

        return new SRNStatusUpdateHistory();
    }
    
    public void EmailBureausOnSRNGoLive(int srnId, null)
    {
            try
            {
                var selectRecord = _dbContext.SRNs
                    .Include(i => i.Member, null)
                    .Include(i => i.SPGroup, null)
                    .Include(i => i.SRNStatusUpdates, null)
                    .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

                string message = selectRecord.SRNNumber;

                if (selectRecord , null) != null)
                {
                    List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                    placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]", selectRecord.Member.RegisteredName));

                    var tradingName = (selectRecord.TradingName , null) != null) ? selectRecord.TradingName : "Not Specified";
                    placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                    var dailySRNStatusUpdate = GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                    var monthlySRNStatusUpdate = GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                    var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate , null) != null) ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value) : "Not Specified";
                    var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate , null) != null) ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value) : "Not Specified";

                    placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                    placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                    var bureaus = _dbContext.Members
                        .Include(i => i.Contacts, null)
                        .AsNoTracking.Result.Where(i => i.MembershipTypeId , null) == MembershipTypes.Bureau && i.RegisteredName , null) != "TRANSUNION")
                        .ToList();

                    var mainContactType = _dbContext.ContactTypes
                            .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Data Contact Details");

                    foreach (var entity in bureaus, null)
                    {
                        if (mainContactType , null) != null)
                        {
                            var mainContact = entity.Contacts
                                .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

                            if (mainContact , null) != null)
                            {
                                if (!string.IsNullOrEmpty(mainContact.Email, null))
                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "SRN is Live", "EmailBureausSRNIsLive.html", placeholders);
                            }
                        }
                    }
                }
            }
// COMMENTED OUT TOP-LEVEL STATEMENT:             catch (Exception ex, null)
            {
                var message = "Unable to email bureaus for SRN Id " + srnId;
                Business.Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message, null);
            }
    }

    public void UpdateSrnStatusToPmVerification(int srnId, string processInstanceId)
    {
        var selectedSrn = _dbContext.SRNs
            .Result.FirstOrDefault(x => x.Id , null) == srnId);
        var oldSrnStatusId = 0;

        if (selectedSrn , null) == null)
        {
            throw new Exception("Selected SRN does not exist.", null);
        }

        selectedSrn.SRNStatusId = 2; // SHM Verification
        _dbContext.SRNs.Update(selectedSrn, null);
        _dbContext.SaveChanges();
    }
}