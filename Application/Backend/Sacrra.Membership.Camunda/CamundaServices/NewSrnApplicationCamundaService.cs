
using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;
using AutoMapper;
using Camunda.Api.Client.ExternalTask;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Business.Resources.SRNStatus;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Camunda.CamundaServices;

public class NewSrnApplicationCamundaService
{
    private readonly SRNRepository _srnRepository;
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly IMapper _mapper;
    private readonly ConfigSettings _configSettings;

    public NewSrnApplicationCamundaService(SRNRepository srnRepository, AppDbContext dbContext,
        EmailService emailService, IMapper mapper, IOptions<ConfigSettings> configSettings)
    {
        _srnRepository = srnRepository;
        _dbContext = dbContext;
        _emailService = emailService;
        _mapper = mapper;
        _configSettings = configSettings.Value;
    }

    public void CallFileTestSubprocess(ExternalTaskResource task, IServiceScope serviceScope)
    {
        _srnRepository.CallFileTestSubprocess(task, serviceScope);
    }

    private bool CheckIfMailForFirstFileHasBeenSentAlready(string email, int entityId,
        WorkflowEnum workflow, EmailReasonEnum emailReason,
        EmailRecipientTypeEnum recipientType)
    {
        var isSent = false;
        var emailCount = _dbContext.EmailQueues.Count(i => i.Email == email && i.EntityId == entityId
                                                                            && i.Workflow == workflow &&
                                                                            i.Reason == emailReason &&
                                                                            i.RecipientType == recipientType);

        if (emailCount > 0)
            isSent = true;

        return isSent;
    }

    private static MemberContact GetMemberDataContact(AppDbContext dbContext, int memberId)
    {
        if (memberId <= 0) return null;

        var dataContact = dbContext.MemberContacts
            .Result.FirstOrDefault(x => x.ContactTypeId == 5 && x.MemberId == memberId);

        return dataContact;
    }

    public void EmailMember(int srnId)
    {
        try
        {
            var selectedSrn = _dbContext.SRNs
                .Include(i => i.Contacts)
                .ThenInclude(i => i.ContactType)
                .Include(i => i.Member)
                .ThenInclude(i => i.Contacts)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .Result.FirstOrDefault(i => i.Id == srnId);

            if (selectedSrn == null)
                throw new Exception("Unable to find SRN with Id " + srnId);

            var placeholders = new List<KeyValuePair<string, string>>
            {
                new("[MemberRegisteredName]", selectedSrn.Member.RegisteredName),
                new("[SRNNumber]", selectedSrn.SRNNumber)
            };

            if (selectedSrn.Member.MembershipTypeId == MembershipTypes.ALGClient)
            {
                if (!(selectedSrn.ALGLeaderId > 0)) return;

                var dataContact = GetMemberDataContact(_dbContext, (int)selectedSrn.ALGLeaderId);

                if (dataContact == null)
                    throw new Exception("Unable to find ALG Leader Data Contact");

                if (CheckIfMailForFirstFileHasBeenSentAlready(dataContact.Email, selectedSrn.Id,
                        WorkflowEnum.SRNApplication, EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member))
                    return;

                _emailService.SendEmail(dataContact.Email, dataContact.FirstName,
                    "SRN Application Successful",
                    "SRNApplicationSuccessfulApplicant.html", placeholders, null, "", "", selectedSrn.Id,
                    WorkflowEnum.SRNApplication,
                    EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member);
            }
            else
            {
                var dataContact = selectedSrn.Contacts
                    .Result.FirstOrDefault(i => i.ContactType.Name == "Data Contact Details");

                if (dataContact == null)
                    throw new Exception("Unable to find Data Contact Details Contact for Member " +
                                        selectedSrn.Member.Id);

                if (CheckIfMailForFirstFileHasBeenSentAlready(dataContact.Email, selectedSrn.Id,
                        WorkflowEnum.SRNApplication, EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member))
                    return;

                _emailService.SendEmail(dataContact.Email, dataContact.FirstName,
                    "SRN Application Successful",
                    "SRNApplicationSuccessfulApplicant.html", placeholders, null, "", "", selectedSrn.Id,
                    WorkflowEnum.SRNApplication,
                    EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Member);
            }
        }
        catch (Exception ex)
        {
            throw new Exception("Unable to email applicant for SRN application successful. SRN Id " + srnId, ex);
        }
    }

    private SRNStatusGetResource GetSrnStatusByName(string name)
    {
        var selectRecord = _dbContext.SRNStatuses
            .AsNoTracking()
            .Result.FirstOrDefault(s => s.Name == name);
        var returnRecord = _mapper.Map<SRNStatusGetResource>(selectRecord);

        return returnRecord;
    }

    public static void CreateEventLog(AppDbContext dbContext, int userId, string changeType, string entityName,
        string entityBlob, string changeBlob, int entityId, string entityTypeName)
    {
        string userFullName;

        if (userId <= 0)
        {
            userFullName = "Internal System";
        }
        else
        {
            var user = dbContext.Users
                .Result.FirstOrDefault(i => i.Id == userId);

            if (user == null)
            {
                throw new Exception($"User ID {userId} was not found.");
            }

            userFullName = user.FirstName + " " + user.LastName;
        }

        var entityTypeId = dbContext.EntityTypes
            .Result.FirstOrDefault(x => x.Name == entityTypeName)
            ?.Id;

        switch (entityTypeId)
        {
            case null:
                throw new Exception($"Entity type {entityTypeName} was not found.");

            case > 0:
            {
                var eventLog = new EventLog
                {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = (int)entityTypeId
                };

                try
                {
                    dbContext.Add(eventLog);
                    dbContext.SaveChanges();
                }
                catch (Exception ex)
                {
                    throw new Exception($"Unable to save event log for {changeType} of {entityName} with ID {entityId}",
                        ex);
                }

                break;
            }
        }
    }

    public async Task<List<VariableInstanceGetResource>> GetVariables(string processInstanceId)
    {
        try
        {
            using (var client = new HttpClient())
            {
                var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId;
                var result = client.Execute(new RestRequest(uri, Method.Get);
                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsStringAsync();
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString);

                return variablesResourceList;
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to retrieve variables for process id " + processInstanceId;
            throw new Exception(message);
        }
    }
    
    public void UpdateSrnStatusToLiveNoTestingRequired(ExternalTaskResource task, int srnId, string newStatus, int updatedByUserId)
    {
        var selectedSrn = _dbContext.SRNs
            .Include(i => i.SRNStatus)
            .Include(i => i.SRNStatusUpdates)
            .Result.FirstOrDefault(i => i.Id == srnId);
        var updatedByUser = _dbContext.Users
            .Result.FirstOrDefault(i => i.Id == updatedByUserId);

        if (selectedSrn == null)
            throw new Exception("Unable to find SRN with Id " + srnId);

        if (updatedByUser == null)
            throw new Exception("Unable to find User with Id " + updatedByUserId);

        var newSrnStatus = GetSrnStatusByName("Live");
        var oldSrnStatus = selectedSrn.SRNStatus;

        selectedSrn.SRNStatusId = 4; // Live
        selectedSrn.StatusLastUpdatedAt = DateTime.Now;
        selectedSrn.SignoffDate = DateTime.Now;
        _dbContext.Update(selectedSrn);
        _dbContext.SaveChanges();

        var srnStagingChange = new StagingChange
        {
            Name = "SRN Status",
            OldValue = oldSrnStatus.Name,
            NewValue = newSrnStatus.Name
        };
        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
        var stagingChangeLog = new MemberStagingChangeLogResource();
        string changeBlob;

        stagingChangeLog.Changes.Add(srnStagingChange);
        changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
        CreateEventLog(_dbContext, updatedByUserId, "SRN Status Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");

        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId);

        switch (selectedSrn.FileType)
        {
            case SRNStatusFileTypes.DailyFile:
            {
                var newSrnFileEntry = new SRNStatusUpdateHistory
                {
                    DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileDevelopmentStartDate").Value),
                    DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileDevelopmentEndDate").Value),
                    DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileTestStartDate").Value),
                    DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileTestEndDate").Value),
                    DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.DailyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                break;
            }
            
            case SRNStatusFileTypes.MonthlyFile:
            {
                var newSrnFileEntry = new SRNStatusUpdateHistory
                {
                    MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileDevelopmentStartDate").Value),
                    MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileDevelopmentEndDate").Value),
                    MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileTestStartDate").Value),
                    MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileTestEndDate").Value),
                    MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.MonthlyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                break;
            }

            case SRNStatusFileTypes.MonthlyAndDailyFile:
            {
                var newDailySrnFileEntry = new SRNStatusUpdateHistory
                {
                    DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileDevelopmentStartDate").Value),
                    DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileDevelopmentEndDate").Value),
                    DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileTestStartDate").Value),
                    DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileTestEndDate").Value),
                    DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.DailyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                var newMonthlySrnFileEntry = new SRNStatusUpdateHistory
                {
                    MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileDevelopmentStartDate").Value),
                    MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileDevelopmentEndDate").Value),
                    MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileTestStartDate").Value),
                    MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileTestEndDate").Value),
                    MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileGoLiveDate").Value),
                    DateCreated = DateTime.Now,
                    FileType = SRNStatusFileTypes.MonthlyFile,
                    IsComple = true,
                    IsLiveFileSubmissionsSuspended = false,
                    SRNId = selectedSrn.Id,
                    SRNStatusId = 4, // Live
                    Comments = selectedSrn.Comments,
                    ProcessInstanceId = srnTask.ProcessInstanceId,
                    RolloutStatusId = 5, // Live
                    SignoffDate = DateTime.Now,
                    DateCompleted = DateTime.Now
                };
                _dbContext.SRNStatusUpdateHistory.Add(newDailySrnFileEntry);
                _dbContext.SRNStatusUpdateHistory.Add(newMonthlySrnFileEntry);
                break;
            }
        }

        var newSrnStatusEntry = new SrnStatusHistory()
        {
            SrnId = selectedSrn.Id,
            StatusId = selectedSrn.SRNStatusId,
            StatusDate = DateTime.Now,
            StatusReasonId = selectedSrn.SRNStatusReasonId
        };
                        
        _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
        _dbContext.SaveChanges();
        
        var srnFileStagingChange = new StagingChange
        {
            Name = "SRN Status",
            OldValue = oldSrnStatus.Name,
            NewValue = newSrnStatus.Name
        };

        stagingChangeLog.Changes.Add(srnFileStagingChange);
        changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
        CreateEventLog(_dbContext, updatedByUserId, "SRN File Status Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
    }
    
    private static SRNStatusUpdateHistory GetRecentSrnStatusUpdateHistory(ICollection<SRNStatusUpdateHistory> srnStatusUpdates)
    {
        if (srnStatusUpdates != null)
        {
            if (srnStatusUpdates.Result.Count > 0)
            {
                srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated).ToList();
                return srnStatusUpdates.First();
            }
        }

        return new SRNStatusUpdateHistory();
    }

    private void AddSrnDailyFileDatesToEmail(SRN selectedSrn, List<KeyValuePair<string, string>> placeholders, ExternalTaskResource task)
    {
        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId);
        var dailyFileDevelopmentStartDate = processVariables.Find(x => x.Name == "DailyFileDevelopmentStartDate")?.Value;
        var dailyFileDevelopmentEndDate = processVariables.Find(x => x.Name == "DailyFileDevelopmentEndDate")?.Value;
        var dailyFileTestStartDate = processVariables.Find(x => x.Name == "DailyFileTestStartDate")?.Value;
        var dailyFileTestEndDate = processVariables.Find(x => x.Name == "DailyFileTestEndDate")?.Value;
        var dailyGoLiveDate = processVariables.Find(x => x.Name == "DailyFileGoLiveDate")?.Value;
        
        var dailyFileDevelopmentStartDatePlaceholder = (dailyFileDevelopmentStartDate != null)
            ? $"{DateTime.Parse(dailyFileDevelopmentStartDate):yyyy-MM-dd}"
            : "N/A";
        var dailyFileDevelopmentEndDatePlaceholder = (dailyFileDevelopmentEndDate != null)
            ? $"{DateTime.Parse(dailyFileDevelopmentEndDate):yyyy-MM-dd}"
            : "N/A";
        var dailyFileTestStartDatePlaceholder = (dailyFileTestStartDate != null)
            ? $"{DateTime.Parse(dailyFileTestStartDate):yyyy-MM-dd}"
            : "N/A";
        var dailyFileTestEndDatePlaceholder = (dailyFileTestEndDate != null)
            ? $"{DateTime.Parse(dailyFileTestEndDate):yyyy-MM-dd}"
            : "N/A";
        var dailyGoLiveDatePlaceholder = (dailyGoLiveDate != null)
            ? $"{DateTime.Parse(dailyGoLiveDate):yyyy-MM-dd}"
            : "N/A";

        placeholders.Add(new KeyValuePair<string, string>("[DailyFileDevelopmentStartDate]", dailyFileDevelopmentStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileDevelopmentEndDate]", dailyFileDevelopmentEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileTestStartDate]", dailyFileTestStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileTestEndDate]", dailyFileTestEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDatePlaceholder));
    }

    private void AddSrnMonthlyFileDatesToEmail(SRN selectedSrn, List<KeyValuePair<string, string>> placeholders, ExternalTaskResource task)
    {
        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId);
        var monthlyFileDevelopmentStartDate = processVariables.Find(x => x.Name == "MonthlyFileDevelopmentStartDate")?.Value;
        var monthlyFileDevelopmentEndDate = processVariables.Find(x => x.Name == "MonthlyFileDevelopmentEndDate")?.Value;
        var monthlyFileTestStartDate = processVariables.Find(x => x.Name == "MonthlyFileTestStartDate")?.Value;
        var monthlyFileTestEndDate = processVariables.Find(x => x.Name == "MonthlyFileTestEndDate")?.Value;
        var monthlyGoLiveDate = processVariables.Find(x => x.Name == "MonthlyFileGoLiveDate")?.Value;
        
        var monthlyFileDevelopmentStartDatePlaceholder = (monthlyFileDevelopmentStartDate != null)
            ? $"{DateTime.Parse(monthlyFileDevelopmentStartDate):yyyy-MM-dd}"
            : "N/A";
        var monthlyFileDevelopmentEndDatePlaceholder = (monthlyFileDevelopmentEndDate != null)
            ? $"{DateTime.Parse(monthlyFileDevelopmentEndDate):yyyy-MM-dd}"
            : "N/A";
        var monthlyFileTestStartDatePlaceholder = (monthlyFileTestStartDate != null)
            ? $"{DateTime.Parse(monthlyFileTestStartDate):yyyy-MM-dd}"
            : "N/A";
        var monthlyFileTestEndDatePlaceholder = (monthlyFileTestEndDate != null)
            ? $"{DateTime.Parse(monthlyFileTestEndDate):yyyy-MM-dd}"
            : "N/A";
        var monthlyGoLiveDatePlaceholder = (monthlyGoLiveDate != null)
            ? $"{DateTime.Parse(monthlyGoLiveDate):yyyy-MM-dd}"
            : "N/A";

        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileDevelopmentStartDate]", monthlyFileDevelopmentStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileDevelopmentEndDate]", monthlyFileDevelopmentEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileTestStartDate]", monthlyFileTestStartDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileTestEndDate]", monthlyFileTestEndDatePlaceholder));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDatePlaceholder));
    }

    public void EmailBureausBeforeSrnTesting(int srnId, ExternalTaskResource task)
    {
        try
        {
            var selectedSrn = _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.SPGroup)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .Result.FirstOrDefault(i => i.Id == srnId);

            if (selectedSrn == null)
            {
                throw new Exception("Unable to find SRN with Id " + srnId);
            }

            var placeholders = new List<KeyValuePair<string, string>>
            {
                new("[MemberRegisteredName]", selectedSrn.Member.RegisteredName),
                new("[SRNNumber]", selectedSrn.SRNNumber)
            };

            var tradingName = selectedSrn.TradingName ?? "Not Specified";
            placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

            var spNumber = selectedSrn.SPGroup == null ? "Not Specified" : selectedSrn.SPGroup.SPNumber;
            placeholders.Add(new KeyValuePair<string, string>("[SPNumber]", spNumber));

            AddSrnDailyFileDatesToEmail(selectedSrn, placeholders, task);
            AddSrnMonthlyFileDatesToEmail(selectedSrn, placeholders, task);

            var srnStatus = selectedSrn.SRNStatus.Name ?? "Not Available";
            placeholders.Add(new KeyValuePair<string, string>("[SRNStatus]", srnStatus));

            var bureaus = _dbContext.Members
                .Include(i => i.Contacts)
                .AsNoTracking()
                .Result.Where(i => i.MembershipTypeId == MembershipTypes.Bureau && i.RegisteredName != "TRANSUNION")
                .ToList();

            var dataContactType = _dbContext.ContactTypes
                .Result.FirstOrDefault(i => i.Id == 5); // Data Contact

            if (dataContactType == null)
            {
                throw new Exception("Unable to find Data Contact Details Contact Type");
            }

            foreach (var entity in bureaus)
            {
                var dataContact = entity.Contacts
                    .Result.FirstOrDefault(i => i.ContactTypeId == dataContactType.Id);

                if (dataContact == null)
                {
                    throw new Exception("Unable to find Data Contact Details Contact for Bureau " + entity.Id);
                }

                if (!string.IsNullOrEmpty(dataContact.Email))
                    _emailService.SendEmail(dataContact.Email, dataContact.FirstName,
                        "New SRN Application",
                        "SRNApplicationBureaus.html", placeholders, null, "", "", selectedSrn.Id,
                        WorkflowEnum.SRNApplication,
                        EmailReasonEnum.NewSRNCreated, EmailRecipientTypeEnum.Bureau);
            }
        }
        catch (Exception ex)
        {
            throw new Exception("Unable to email bureaus for SRN Id " + srnId, ex);
        }
    }

    public void NotifyApplicantOfSrnApplicationRejection(int srnId)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .Result.FirstOrDefault(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = Business.Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    //Do send email to the ALG client but to the ALG Leader
                    if (srn.Member.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        if (srn.ALGLeaderId > 0)
                            mainContact =
                                Business.Helpers.Helpers.GetMemberMainContact(_dbContext, (int)srn.ALGLeaderId);
                        else
                            mainContact = null;
                    }

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[MemberRegisteredName]", srn.Member.RegisteredName),
                            new KeyValuePair<string, string>("[RejectionReason]", srn.SecondReviewRejectReason)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Application Rejected",
                            "SRNApplicationRejectionApplicant.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for SRN application rejection. SRN Id " + srnId;
            Business.Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public MemberGetResource GetMember(int id)
    {
        var member = _dbContext.Members
            .Include(i => i.StakeholderManager)
            .AsNoTracking()
            .Result.FirstOrDefault(s => s.Id == id);

        var resouce = _mapper.Map<MemberGetResource>(member);

        return resouce;
    }

    private static Tuple<string, long> GenerateSrnNumber(SRNSetting settings)
    {
        var newSequenceNumber = settings.LastGeneratedNumber + settings.Increment;

        if (string.IsNullOrEmpty(settings.Prefix)) return null;

        if (settings.LastGeneratedNumber >= settings.MaxGeneratedNumberAllowed)
            throw new Exception("Maximum SRN number reached for prefix " + settings.Prefix +
                                ". Consider changing the SRN prefix from the SRN settings");

        var srnNumber = settings.Prefix + newSequenceNumber.ToString("D" + settings.NoOfDigitsAllowed);
        return new Tuple<string, long>(srnNumber, newSequenceNumber);
    }

    public void CreateSrnNumber(int srnId)
    {
        {
            var selectedSrn = _dbContext.SRNs
                .Result.FirstOrDefault(i => i.Id == srnId);

            if (selectedSrn == null)
                throw new Exception($"SRN with ID {srnId} was not found");

            if (!string.IsNullOrWhiteSpace(selectedSrn.SRNNumber)) return;

            var settings = _dbContext.SRNSettings.Result.FirstOrDefault();

            if (settings == null)
                throw new Exception("Unable to create SRN number. No SRN settings found.");

            var newlyGeneratedSrn = GenerateSrnNumber(settings);
            var newSrnNumber = newlyGeneratedSrn.Item1;
            var existingSrn = _dbContext.SRNs
                .Result.FirstOrDefault(i => i.SRNNumber == newSrnNumber);

            var isSrnNumberAllowed = true;
            if (!string.IsNullOrEmpty(settings.Exclusions))
            {
                var srnExclusions = settings.Exclusions.Split(',');

                if (srnExclusions.Contains(newSrnNumber))
                    isSrnNumberAllowed = false;
            }

            while (existingSrn != null || !isSrnNumberAllowed)
            {
                settings.LastGeneratedNumber += settings.Increment;
                newlyGeneratedSrn = GenerateSrnNumber(settings);
                newSrnNumber = newlyGeneratedSrn.Item1;

                existingSrn = _dbContext.SRNs
                    .Result.FirstOrDefault(i => i.SRNNumber == newSrnNumber);

                isSrnNumberAllowed = true;
                if (string.IsNullOrEmpty(settings.Exclusions)) continue;

                var srnExclusions = settings.Exclusions.Split(',');
                if (srnExclusions.Contains(newSrnNumber))
                    isSrnNumberAllowed = false;
            }

            selectedSrn.SRNNumber = newSrnNumber;
            selectedSrn.CreationDate = DateTime.Now;

            try
            {
                _dbContext.SaveChanges();

                var newSrn = _dbContext.SRNs.Result.FirstOrDefault(x => x.SRNNumber == newSrnNumber);

                if (newSrn == null)
                {
                    throw new Exception($"Unable get SRN with ID {newSrnNumber}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Unable to save SRN number for SRN ID {selectedSrn.Id}", ex);
            }
        }
    }

    private SRNStatusUpdateHistory GetRecentSRNStatusUpdateHistory(ICollection<SRNStatusUpdateHistory> srnStatusUpdates, string fileType)
    {
        if (srnStatusUpdates != null)
        {
            if (srnStatusUpdates.Result.Count > 0)
            {
                if (fileType != null)
                {
                    if (fileType == "MonthlyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType == SRNStatusFileTypes.MonthlyFile).ToList();
                    else if (fileType == "DailyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType == SRNStatusFileTypes.DailyFile).ToList();
                }

                if (srnStatusUpdates != null)
                {
                    if (srnStatusUpdates.Result.Count > 0)
                    {
                        srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated).ToList();
                        return srnStatusUpdates.Result.FirstOrDefault();
                    }
                }
            }
        }

        return new SRNStatusUpdateHistory();
    }
    
    public void EmailBureausOnSRNGoLive(int srnId)
    {
            try
            {
                var selectRecord = _dbContext.SRNs
                    .Include(i => i.Member)
                    .Include(i => i.SPGroup)
                    .Include(i => i.SRNStatusUpdates)
                    .AsNoTracking()
                    .Result.FirstOrDefault(i => i.Id == srnId);

                string message = selectRecord.SRNNumber;

                if (selectRecord != null)
                {
                    List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                    placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]", selectRecord.Member.RegisteredName));

                    var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                    placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                    var dailySRNStatusUpdate = GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                    var monthlySRNStatusUpdate = GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                    var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate != null) ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value) : "Not Specified";
                    var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate != null) ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value) : "Not Specified";

                    placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                    placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                    var bureaus = _dbContext.Members
                        .Include(i => i.Contacts)
                        .AsNoTracking()
                        .Result.Where(i => i.MembershipTypeId == MembershipTypes.Bureau && i.RegisteredName != "TRANSUNION")
                        .ToList();

                    var mainContactType = _dbContext.ContactTypes
                            .AsNoTracking()
                            .Result.FirstOrDefault(i => i.Name == "Data Contact Details");

                    foreach (var entity in bureaus)
                    {
                        if (mainContactType != null)
                        {
                            var mainContact = entity.Contacts
                                .Result.FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                            if (mainContact != null)
                            {
                                if (!string.IsNullOrEmpty(mainContact.Email))
                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "SRN is Live", "EmailBureausSRNIsLive.html", placeholders);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email bureaus for SRN Id " + srnId;
                Business.Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
    }

    public void UpdateSrnStatusToPmVerification(int srnId, string processInstanceId)
    {
        var selectedSrn = _dbContext.SRNs
            .Result.FirstOrDefault(x => x.Id == srnId);
        var oldSrnStatusId = 0;

        if (selectedSrn == null)
        {
            throw new Exception("Selected SRN does not exist.");
        }

        selectedSrn.SRNStatusId = 2; // SHM Verification
        _dbContext.SRNs.Update(selectedSrn);
        _dbContext.SaveChanges();
    }
}