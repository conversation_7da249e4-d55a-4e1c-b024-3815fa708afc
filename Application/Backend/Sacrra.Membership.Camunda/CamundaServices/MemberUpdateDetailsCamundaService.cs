using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class MemberUpdateDetailsCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    
// COMMENTED OUT:     public MemberUpdateDetailsCamundaService(AppDbContext dbContext, EmailService emailService)
    {
        _dbContext = dbContext;
        _emailService = emailService;
    }

    public void NotifySHMOfMemberUpdate(int memberId, null)
    {
        try
        {
            var member = _dbContext.Members
                .Include(i => i.StakeholderManager, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == memberId);

            if (member , null) != null)
            {
                if (member.StakeholderManager , null) != null)
                {
                    var shm = member.StakeholderManager;
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                    };

                    _emailService.SendEmail(shm.Email, shm.FirstName, "Member Updated - No Approval Required",
                        "MemberUpdateNoApprovalSHM.html", placeholders);
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email SHM for member update. Member Id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }
}