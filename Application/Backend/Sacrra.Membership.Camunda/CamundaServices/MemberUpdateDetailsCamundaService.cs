using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class MemberUpdateDetailsCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    
    public MemberUpdateDetailsCamundaService(AppDbContext dbContext, EmailService emailService)
    {
        _dbContext = dbContext;
        _emailService = emailService;
    }

    public void NotifySHMOfMemberUpdate(int memberId)
    {
        try
        {
            var member = _dbContext.Members
                .Include(i => i.StakeholderManager)
                .AsNoTracking()
                .Result.FirstOrDefault(i => i.Id == memberId);

            if (member != null)
            {
                if (member.StakeholderManager != null)
                {
                    var shm = member.StakeholderManager;
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                    };

                    _emailService.SendEmail(shm.Email, shm.FirstName, "Member Updated - No Approval Required",
                        "MemberUpdateNoApprovalSHM.html", placeholders);
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email SHM for member update. Member Id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }
}