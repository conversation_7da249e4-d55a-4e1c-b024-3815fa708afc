using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Camunda.Api.Client.ExternalTask;
using Hangfire.Storage.Monitoring;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Renci.SshNet;
using RestSharp;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class ReplacementFileSubmissionCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly GlobalHelper _globalHelper;
    private readonly DataWarehouseService.DataWarehouseService _dataWarehouseService;
    private readonly ConfigSettings _configSettings;
    private readonly EmailService _emailService;
    private readonly LookupsService.LookupsService _lookupsService;

    public ReplacementFileSubmissionCamundaService(AppDbContext dbContext, GlobalHelper globalHelper,
        DataWarehouseService.DataWarehouseService dataWarehouseService, IOptions<ConfigSettings> configSettings, EmailService emailService,
        LookupsService.LookupsService lookupsService)
    {
        _dbContext = dbContext;
        _globalHelper = globalHelper;
        _dataWarehouseService = dataWarehouseService;
        _configSettings = configSettings.Value;
        _emailService = emailService;
        _lookupsService = lookupsService;
    }

    public void UpdateReplacementFileSubmissionStatusToDeclined(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        int replacementFileSubmissionId;
        ReplacementFileSubmission replacementFileSubmission;

        if (taskVariables != null)
        {
            replacementFileSubmissionId = int.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        }
        else
        {
            throw new Exception($"Unable to fetch variables for task with process instance ID ({processInstanceId})");
        }

        if (replacementFileSubmissionId > 0)
        {
            replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                .Result.Where(x => x.Id == replacementFileSubmissionId).Result.FirstOrDefault();
        }
        else
        {
            throw new Exception($"Unable to find replacement file submission with ID ({replacementFileSubmissionId})");
        }

        try
        {
            replacementFileSubmission.ReplacementFileSubmissionStatusId =
                (int)ReplacementFileSubmissionStatuses.Declined;
            replacementFileSubmission.ReplacementFileSubmissionDeclineReasonId =
                long.Parse(taskVariables.Find(x => x.Name == "reasonForDecline")?.Value);
            _dbContext.ReplacementFileSubmissions.Update(replacementFileSubmission);
            _dbContext.SaveChanges();
        }
        catch
        {
            throw new Exception(
                $"Unable to update replacement file submission with ID ({replacementFileSubmissionId})");
        }
    }

    public void ReplacementFileSubmissionCheckFileSubmittedToDTH(ExternalTaskInfo task)
    {
        var taskVariables = _globalHelper.GetVariables(task.ProcessInstanceId);
        var replacementFileSubmissionId =
            long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
            .Result.Where(x => x.Id == replacementFileSubmissionId).Result.FirstOrDefault();
        var whereClause = $"1 = 1";
        DataWarehouseAPIModel apiCallModel;
        var fileNameList = new List<FileSubmissionOutputDTO>();
        var client = new RestClient();
        RestRequest restRequest;
        var camundaVariables = new
        {
            value = "submitted",
            type = "string"
        };

        whereClause += $" AND FileName = '{replacementFileSubmission.ReplacementFileName}'";
        apiCallModel = new DataWarehouseAPIModel()
        {
            Columns = "FileName, TransactionDate, DWDateCreated",
            Where = whereClause
        };

        fileNameList = _dataWarehouseService
            .GetResultArray<FileSubmissionOutputDTO>("API.vwDailyAndMonthlyFileSubmissions", apiCallModel).ToList();

        if (fileNameList.Result.Count > 0)
        {
            camundaVariables = new
            {
                value = "submitted",
                type = "string"
            };

            replacementFileSubmission.SubmissionLastCheckedDate = DateTime.Now;
            replacementFileSubmission.ActualSubmissionDate = fileNameList.Result.FirstOrDefault().TransactionDate;
            replacementFileSubmission.DWDateCreated = fileNameList.Result.FirstOrDefault().DWDateCreated;
        }
        else
        {
            camundaVariables = new
            {
                value = "notSubmitted",
                type = "string"
            };

            replacementFileSubmission.SubmissionLastCheckedDate = DateTime.Now;
        }

        try
        {
            restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/process-instance/" +
                                          task.ProcessInstanceId + "/variables/fileSubmittedOnPlannedDate")
                .AddJsonBody(JsonConvert.SerializeObject(camundaVariables));
            client.Execute(new RestRequest(restRequest, Method.Get);
            _dbContext.SaveChanges();
        }
        catch (Exception exception)
        {
            // Don't pass the same DbContext instance to avoid concurrent access issues
            _globalHelper.LogError(null, Method.Put).AddJsonBody(exception,
                $"Unable to complete task to check if replacement file was submitted to DTH ({task.Id}))");
            throw new Exception(
                $"Unable to complete task to check if replacement file was submitted to DTH ({task.Id})");
        }
    }

    public void UpdateReplacementFileSubmissionStatusToApproved(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        int replacementFileSubmissionId;
        ReplacementFileSubmission replacementFileSubmission;

        if (taskVariables != null)
        {
            replacementFileSubmissionId = int.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        }
        else
        {
            throw new Exception($"Unable to fetch variables for task with process instance ID ({processInstanceId})");
        }

        if (replacementFileSubmissionId > 0)
        {
            replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                .Result.Where(x => x.Id == replacementFileSubmissionId).Result.FirstOrDefault();
        }
        else
        {
            throw new Exception($"Unable to find replacement file submission with ID ({replacementFileSubmissionId})");
        }

        try
        {
            replacementFileSubmission.ReplacementFileSubmissionStatusId =
                (int)ReplacementFileSubmissionStatuses.Approved;
            replacementFileSubmission.SubmissionStatusDate = DateTime.Now;
            _dbContext.ReplacementFileSubmissions.Update(replacementFileSubmission);
            _dbContext.SaveChanges();
        }
        catch
        {
            throw new Exception(
                $"Unable to update replacement file submission with ID ({replacementFileSubmissionId})");
        }
    }

    public void ReplacementFileSubmissionEmailMemberApproved(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        var srnIdVariable = taskVariables.Find(x => x.Name == "SRNId")?.Value;
        var spIdVariable = taskVariables.Find(x => x.Name == "SPId")?.Value;
        long srnId = 0;
        long spId = 0;

        if (srnIdVariable != null)
        {
            srnId = long.Parse(srnIdVariable);
        }

        if (spIdVariable != null)
        {
            spId = long.Parse(spIdVariable);
        }

        var replacementFileSubmissionId =
            long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        var replacementFileSubmissionSRN = _dbContext.SRNs.Result.Where(x => x.Id == srnId)
            .Include(x => x.Member)
            .Include(x => x.Contacts)
            .Result.FirstOrDefault();
        var replacementFileSubmissionSPNumber = _dbContext.SPGroups.Result.Where(x => x.Id == spId)
            .Include(x => x.Member)
            .Include(x => x.Member.Contacts)
            .Result.FirstOrDefault();
        var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
            .Result.Where(x => x.Id == replacementFileSubmissionId)
            .Include(x => x.ReplacementFileSubmissionReason)
            .Result.FirstOrDefault();

        var memberPlaceholders = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("[Member]",
                replacementFileSubmissionSRN == null
                    ? replacementFileSubmissionSPNumber.Member.RegisteredName
                    : replacementFileSubmissionSRN.Member.RegisteredName),
            new KeyValuePair<string, string>("[ReplacementFile]",
                replacementFileSubmissionSRN == null
                    ? replacementFileSubmissionSPNumber.SPNumber
                    : replacementFileSubmissionSRN.SRNNumber),
            new KeyValuePair<string, string>("[FileName]", replacementFileSubmission.ReplacementFileName),
            new KeyValuePair<string, string>("[ReplacementFileReason]",
                replacementFileSubmission.ReplacementFileSubmissionReason.Name),
            new KeyValuePair<string, string>("[SubmissionStatusDate]",
                replacementFileSubmission.SubmissionStatusDate.ToString("yyyy-MM-dd"))
        };

        MemberContact mainContact = null;
        SRNContact srnDataContact = null;

        if (replacementFileSubmissionSRN == null && replacementFileSubmissionSPNumber != null)
        {
            mainContact = replacementFileSubmissionSPNumber.Member.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 1);
        }

        if (replacementFileSubmissionSRN != null && replacementFileSubmissionSPNumber == null)
        {
            srnDataContact = replacementFileSubmissionSRN.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 5);
        }

        var bureauList = _dbContext.Members.Result.Where(x => x.MembershipTypeId == MembershipTypes.Bureau).ToList();
        var bureauContactList = new List<string>();

        foreach (var bureau in bureauList)
        {
            if (bureau.RegisteredName.ToLower() == "unknown")
            {
                continue;
            }

            var bureauContact = _dbContext.MemberContacts
                .Result.Where(x => x.MemberId == bureau.Id && x.ContactTypeId == 5)
                .Result.FirstOrDefault();

            bureauContactList.Add(bureauContact.Email);
        }

        _emailService.SendEmail(mainContact == null ? srnDataContact.Email : mainContact.Email,
            mainContact == null ? srnDataContact.FirstName : mainContact.FirstName,
            "Replacement File Submission Approved", "ReplacementFileSubmissionApproved.html", memberPlaceholders,
            bureauContactList, "", "",
            replacementFileSubmissionSRN == null
                ? replacementFileSubmissionSPNumber.Id
                : replacementFileSubmissionSRN.Id, WorkflowEnum.ReplacementFileSubmissions,
            EmailReasonEnum.ReplacementFileSubmissionApproved, EmailRecipientTypeEnum.Member);
    }

    public void ReplacementFileSubmissionCreateFileOnFTPServer(string processInstanceId)
    {
        try
        {
            var taskVariables = _globalHelper.GetVariables(processInstanceId);
            var replacementFileSubmissionId =
                long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
            var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                .Result.Where(x => x.Id == replacementFileSubmissionId).Result.FirstOrDefault();
            var client = new SftpClient(_configSettings.FTPServerAddress, int.Parse(_configSettings.FTPServerPort),
                _configSettings.FTPServerUser, _configSettings.FTPServerPassword);

            client.Connect();
            client.Create("./" + replacementFileSubmission.ReplacementFileName);
            client.Disconnect();
            client.Dispose();
        }
        catch (Exception exception)
        {
            var message = "Unable to create file on FTP server for replacement File Submission.";
            Sacrra.Membership.Business.Helpers.Helpers.LogError(_dbContext, exception, message);
            throw new Exception(message);
        }
    }

    public void ReplacementFileSubmissionEmailMemberDeclined(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        var srnIdVariable = taskVariables.Find(x => x.Name == "SRNId")?.Value;
        var spIdVariable = taskVariables.Find(x => x.Name == "SPId")?.Value;
        long srnId = 0;
        long spId = 0;

        if (srnIdVariable != null)
        {
            srnId = long.Parse(srnIdVariable);
        }

        if (spIdVariable != null)
        {
            spId = long.Parse(spIdVariable);
        }

        var replacementFileSubmissionId =
            long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        var replacementFileSubmissionSRN = _dbContext.SRNs
            .Result.Where(x => x.Id == srnId)
            .Include(x => x.Member)
            .Include(x => x.Contacts)
            .Result.FirstOrDefault();
        var replacementFileSubmissionSPNumber = _dbContext.SPGroups
            .Result.Where(x => x.Id == spId)
            .Include(x => x.Member)
            .Include(x => x.Member.Contacts)
            .Result.FirstOrDefault();
        var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
            .Result.Where(x => x.Id == replacementFileSubmissionId)
            .Result.FirstOrDefault();
        var replacementFileSubmissionDeclineReason = _lookupsService
            .GetEnumIdValuePairs<ReplacementFileSubmissionDeclineReasons>()
            .Find(x => x.Id == replacementFileSubmission.ReplacementFileSubmissionDeclineReasonId).Value;

        var memberPlaceholders = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("[Member]",
                replacementFileSubmissionSRN == null
                    ? replacementFileSubmissionSPNumber.Member.RegisteredName
                    : replacementFileSubmissionSRN.Member.RegisteredName),
            new KeyValuePair<string, string>("[ReplacementFile]",
                replacementFileSubmissionSRN == null
                    ? replacementFileSubmissionSPNumber.SPNumber
                    : replacementFileSubmissionSRN.SRNNumber),
            new KeyValuePair<string, string>("[FileName]", replacementFileSubmission.ReplacementFileName),
            new KeyValuePair<string, string>("[DeclineReason]", replacementFileSubmissionDeclineReason)
        };

        MemberContact mainContact = null;
        SRNContact srnDataContact = null;

        if (replacementFileSubmissionSRN == null && replacementFileSubmissionSPNumber != null)
        {
            mainContact = replacementFileSubmissionSPNumber.Member.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 1);
        }

        if (replacementFileSubmissionSRN != null && replacementFileSubmissionSPNumber == null)
        {
            srnDataContact = replacementFileSubmissionSRN.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 5);
        }

        _emailService.SendEmail(mainContact == null ? srnDataContact.Email : mainContact.Email,
            mainContact == null ? srnDataContact.FirstName : mainContact.FirstName,
            "Replacement File Submission Declined", "ReplacementFileSubmissionDeclined.html", memberPlaceholders, null,
            "", "",
            replacementFileSubmissionSRN == null
                ? replacementFileSubmissionSPNumber.Id
                : replacementFileSubmissionSRN.Id, WorkflowEnum.ReplacementFileSubmissions,
            EmailReasonEnum.ReplacementFileSubmissionDeclined, EmailRecipientTypeEnum.Member);
    }

    public void ReplacementFileSubmissionUpdateFileSubmissionToSubmittedOrCancelled(ExternalTaskInfo camundaTask)
    {
        var taskVariables = _globalHelper.GetVariables(camundaTask.ProcessInstanceId);
        int replacementFileSubmissionId;
        ReplacementFileSubmission replacementFileSubmission;

        if (taskVariables != null)
        {
            replacementFileSubmissionId = int.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        }
        else
        {
            throw new Exception(
                $"Unable to fetch variables for task with process instance ID ({camundaTask.ProcessInstanceId})");
        }

        if (replacementFileSubmissionId > 0)
        {
            replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                .Result.Where(x => x.Id == replacementFileSubmissionId).Include(x => x.SRN).Result.FirstOrDefault();
        }
        else
        {
            throw new Exception($"Unable to find replacement file submission with ID ({replacementFileSubmissionId})");
        }

        try
        {
            var eventLogEntry = new EventLog();
            if (taskVariables.Find(x => x.Name == "fileSubmittedOnPlannedDate").Value == "submitted")
            {
                replacementFileSubmission.ReplacementFileSubmissionStatusId =
                    (int)ReplacementFileSubmissionStatuses.Submitted;
                replacementFileSubmission.SubmissionStatusDate = DateTime.Now;
                replacementFileSubmission.ActualSubmissionDate = DateTime.Now;

                dynamic anonObject = new
                {
                    Changes = new[]
                    {
                        new
                        {
                            Name = "ReplacementFileSubmissionStatusId",
                            OldValue = replacementFileSubmission.ReplacementFileSubmissionStatusId,
                            NewValue = (int)ReplacementFileSubmissionStatuses.Submitted
                        }
                    }
                };

                Helpers.Helpers
                       .CreateEventLogSystemTask(_dbContext, "System Task", "TaskCompletion", replacementFileSubmission.ReplacementFileName, JsonConvert.SerializeObject(replacementFileSubmission),
                        JsonConvert.SerializeObject(anonObject)
                       , replacementFileSubmission.Id, "ReplacementFileSubmission");
            }
            else
            {
                replacementFileSubmission.ReplacementFileSubmissionStatusId =
                    (int)ReplacementFileSubmissionStatuses.Cancelled;
                replacementFileSubmission.SubmissionStatusDate = DateTime.Now;

                dynamic anonObject = new
                {
                    Changes = new[]
                    {
                        new
                        {
                            Name = "ReplacementFileSubmissionStatusId",
                            OldValue = replacementFileSubmission.ReplacementFileSubmissionStatusId,
                            NewValue = (int)ReplacementFileSubmissionStatuses.Cancelled
                        }
                    }
                };

                Helpers.Helpers
                     .CreateEventLogSystemTask(_dbContext, "System Task", "TaskCompletion", replacementFileSubmission.ReplacementFileName, JsonConvert.SerializeObject(replacementFileSubmission),
                      JsonConvert.SerializeObject(anonObject)
                     , replacementFileSubmission.Id, "ReplacementFileSubmission");

            }

            _dbContext.ReplacementFileSubmissions.Update(replacementFileSubmission);
            _dbContext.SaveChanges();
        }
        catch
        {
            throw new Exception(
                $"Unable to update replacement file submission with ID ({replacementFileSubmissionId})");
        }
    }

    public void ReplacementFileSubmissionEmailMemberBureausCancelled(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        var srnIdVariable = taskVariables.Find(x => x.Name == "SRNId")?.Value;
        var spIdVariable = taskVariables.Find(x => x.Name == "SPId")?.Value;
        long srnId = 0;
        long spId = 0;

        if (taskVariables.Find(x => x.Name == "fileSubmittedOnPlannedDate").Value == "cancelled")
        {
            if (srnIdVariable != null)
            {
                srnId = long.Parse(srnIdVariable);
            }

            if (spIdVariable != null)
            {
                spId = long.Parse(spIdVariable);
            }

            var replacementFileSubmissionId =
                long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
            var replacementFileSubmissionSRN = _dbContext.SRNs.Result.Where(x => x.Id == srnId)
                .Include(x => x.Member)
                .Include(x => x.Contacts)
                .Result.FirstOrDefault();
            var replacementFileSubmissionSPNumber = _dbContext.SPGroups.Result.Where(x => x.Id == spId)
                .Include(x => x.Member)
                .Include(x => x.Member.Contacts)
                .Result.FirstOrDefault();
            var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                .Result.Where(x => x.Id == replacementFileSubmissionId).Result.FirstOrDefault();

            var memberPlaceholders = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("[Member]",
                    replacementFileSubmissionSRN == null
                        ? replacementFileSubmissionSPNumber.Member.RegisteredName
                        : replacementFileSubmissionSRN.Member.RegisteredName),
                new KeyValuePair<string, string>("[FileName]", replacementFileSubmission.ReplacementFileName),
                new KeyValuePair<string, string>("[CancellationReason]",
                    replacementFileSubmission.ReasonForCancellation)
            };

            MemberContact mainContact = null;
            SRNContact srnDataContact = null;

            if (replacementFileSubmissionSRN == null && replacementFileSubmissionSPNumber != null)
            {
                mainContact =
                    replacementFileSubmissionSPNumber.Member.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 1);
            }

            if (replacementFileSubmissionSRN != null && replacementFileSubmissionSPNumber == null)
            {
                srnDataContact = replacementFileSubmissionSRN.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 5);
            }

            var bureauList = _dbContext.Members.Result.Where(x => x.MembershipTypeId == MembershipTypes.Bureau).ToList();
            var bureauContactList = new List<string>();

            foreach (var bureau in bureauList)
            {
                if (bureau.RegisteredName.ToLower() == "unknown")
                {
                    continue;
                }

                var bureauContact = _dbContext.MemberContacts
                    .Result.Where(x => x.MemberId == bureau.Id && x.ContactTypeId == 5)
                    .Result.FirstOrDefault();

                bureauContactList.Add(bureauContact.Email);
            }

            _emailService.SendEmail(mainContact == null ? srnDataContact.Email : mainContact.Email,
                mainContact == null ? srnDataContact.FirstName : mainContact.FirstName,
                "Replacement File Submission Cancelled", "ReplacementFileSubmissionCancelled.html", memberPlaceholders,
                bureauContactList, "", "",
                replacementFileSubmissionSRN == null
                    ? replacementFileSubmissionSPNumber.Id
                    : replacementFileSubmissionSRN.Id, WorkflowEnum.ReplacementFileSubmissions,
                EmailReasonEnum.ReplacementFileSubmissionCancelled, EmailRecipientTypeEnum.Member);
        }
    }
}