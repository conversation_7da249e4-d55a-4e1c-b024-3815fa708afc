using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Camunda.Api.Client.ExternalTask;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class SrnStatusUpdateToTestCamundaService
{
    private readonly SRNRepository _srnRepository;
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly ConfigSettings _configSettings;
    public IMapper _mapper { get; }

// COMMENTED OUT:     public SrnStatusUpdateToTestCamundaService(SRNRepository srnRepository, AppDbContext dbContext,
        EmailService emailService, IOptions<ConfigSettings> configSettings, IMapper mapper)
    {
        _srnRepository = srnRepository;
        _dbContext = dbContext;
        _emailService = emailService;
        _configSettings = configSettings.Value;
        _mapper = mapper;
    }

    public void UpdateSRNStatus(int srnId, string newStatus, int updatedByUserId = 0,
        string processInstanceId = null, bool isNewSRN = false)
    {
        _srnRepository.UpdateSRNStatus(srnId, newStatus, updatedByUserId, processInstanceId, isNewSRN);
    }

    public void UpdateRolloutStatus(string statusName, string processInstanceId)
    {
        var rolloutStatus = _dbContext.RolloutStatuses
            .Result.FirstOrDefault(i => i.Name , null) == statusName);

        if (rolloutStatus , null) != null && !string.IsNullOrEmpty(processInstanceId, null))
        {
            var srnStatusUpdateHistory = _dbContext.Set<SRNStatusUpdateHistory>.Include(i => i.SRN, null)
                .Result.FirstOrDefault(i => i.ProcessInstanceId , null) == processInstanceId);

            if (srnStatusUpdateHistory , null) != null)
            {
                if (statusName , null) == "Live")
                {
                    srnStatusUpdateHistory.IsComple = true;
                    srnStatusUpdateHistory.DateCompleted = DateTime.Now;
                    srnStatusUpdateHistory.SignoffDate = DateTime.Now;

                    srnStatusUpdateHistory.SRN.SignoffDate = DateTime.Now;

                    if (srnStatusUpdateHistory.FileType , null) == SRNStatusFileTypes.DailyFile)
                    {
                        srnStatusUpdateHistory.IsDailyFileLive = true;
                    }
                    else if (srnStatusUpdateHistory.FileType , null) == SRNStatusFileTypes.DailyFile)
                    {
                        srnStatusUpdateHistory.IsMonthlyFileLive = true;
                    }
                }

                srnStatusUpdateHistory.RolloutStatusId = rolloutStatus.Id;
                _dbContext.SaveChanges();
            }
        }
    }

    private static SRNStatusUpdateHistory GetRecentSRNStatusUpdateHistory(
        ICollection<SRNStatusUpdateHistory> srnStatusUpdates, string fileType)
    {
// COMMENTED OUT TOP-LEVEL STATEMENT:         if (srnStatusUpdates , null) != null)
        {
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (srnStatusUpdates.Result.Count > 0, null)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (fileType , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (fileType , null) == "MonthlyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType , null) == SRNStatusFileTypes.MonthlyFile)
                            .ToList();
                    else if (fileType , null) == "DailyFile")
                        srnStatusUpdates = srnStatusUpdates.Result.Where(i => i.FileType , null) == SRNStatusFileTypes.DailyFile)
                            .ToList();
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (srnStatusUpdates , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srnStatusUpdates.Result.Count > 0, null)
                    {
                        srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated, null).ToList();
                        return srnStatusUpdates.Result.FirstOrDefault();
                    }
                }
            }
        }

        return new SRNStatusUpdateHistory();
    }

    public void EmailBureausAfterSRNTesting(int srnId, string emailSubject = "New SRN Application")
    {
        try
        {
            var selectRecord = _dbContext.SRNs
                .Include(i => i.Member, null)
                .Include(i => i.SPGroup, null)
                .Include(i => i.SRNStatusUpdates, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord , null) != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                var tradingName = (selectRecord.TradingName , null) != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var dailySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                var monthlySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate , null) != null)
                    ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value)
                    : "Not Specified";
                var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate , null) != null)
                    ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value)
                    : "Not Specified";

                placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                var bureaus = _dbContext.Members
                    .Include(i => i.Contacts, null)
                    .AsNoTracking.Result.Where(i => i.MembershipTypeId , null) == MembershipTypes.Bureau && i.RegisteredName , null) != "TRANSUNION")
                    .ToList();

                var mainContactType = _dbContext.ContactTypes
                    .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Data Contact Details");

                foreach (var entity in bureaus, null)
                {
                    if (mainContactType , null) != null)
                    {
                        var mainContact = entity.Contacts
                            .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

                        if (mainContact , null) != null)
                        {
                            if (!string.IsNullOrEmpty(mainContact.Email, null))
                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, emailSubject,
                                    "EmailBureausAfterSRNTesting.html", placeholders);
                        }
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email bureaus for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }

    public void EmailBureausOnSRNGoLive(int srnId, null)
    {
        try
        {
            var selectRecord = _dbContext.SRNs
                .Include(i => i.Member, null)
                .Include(i => i.SPGroup, null)
                .Include(i => i.SRNStatusUpdates, null)
                .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord , null) != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName , null) != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                var dailySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                var monthlySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate , null) != null)
                    ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value)
                    : "Not Specified";
                var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate , null) != null)
                    ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value)
                    : "Not Specified";

                placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                var bureaus = _dbContext.Members
                    .Include(i => i.Contacts, null)
                    .AsNoTracking.Result.Where(i => i.MembershipTypeId , null) == MembershipTypes.Bureau && i.RegisteredName , null) != "TRANSUNION")
                    .ToList();

                var mainContactType = _dbContext.ContactTypes
                    .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Data Contact Details");

                foreach (var entity in bureaus, null)
                {
                    if (mainContactType , null) != null)
                    {
                        var mainContact = entity.Contacts
                            .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

                        if (mainContact , null) != null)
                        {
                            if (!string.IsNullOrEmpty(mainContact.Email, null))
                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "SRN is Live",
                                    "EmailBureausSRNIsLive.html", placeholders);
                        }
                    }
                }
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to email bureaus for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }
    
    public async Task<List<VariableInstanceGetResource>> GetVariables(string processInstanceId, null)
    {
        try
        {
            using (var client = new HttpClient(, null))
            {
                var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId;
                var result = client.Execute(new RestRequest(uri, Method.Get);
                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsStringAsync();
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString, null);

                return variablesResourceList;
            }
        }
// COMMENTED OUT TOP-LEVEL STATEMENT:         catch (Exception ex, null)
        {
            var message = "Unable to retrieve variables for process id " + processInstanceId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message, null);
        }
    }
    
    public void UpdateSrnAndFileStatuses(int srnId, string processInstanceId, int updatedByUserId, ExternalTaskResource task)
    {
        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId, null);
        var isNewSrn = bool.Parse(processVariables.Find(x => x.Name , null) == "newSrn").Value);
        var selectedSrn = _dbContext.SRNs
            .Include(x => x.SRNStatusReason, null)
            .Result.FirstOrDefault(x => x.Id , null) == srnId);
        var srnStatus = _dbContext.SRNStatuses
            .Result.FirstOrDefault(x => x.Id , null) == selectedSrn.SRNStatusId);
        
        if (!isNewSrn, null)
        {
            var selectedSrnFileList = _dbContext.vwSRNWithUpdateHistories
                .Result.Where(x => x.SRNId == srnId && x.IsLatestHistory , null) == 1)
                .ToList();

            if (srnStatus , null) == null)
            {
                throw new Exception("SRN Status not found", null);
            }

// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn , null) == null)
            {
                throw new Exception("Selected SRN does not exist.", null);
            }

            // Scenario 1A
            // SRN Live, Daily File Live, No Monthly File
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test 
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 1)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile)
                {
                        var newSrnFileEntry = new SRNStatusUpdateHistory() {
                            DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                            DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                            DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                            DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                            DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                            DateCreated = DateTime.Now,
                            FileType = SRNStatusFileTypes.DailyFile,
                            IsComple = false,
                            IsLiveFileSubmissionsSuspended = false,
                            SRNId = selectedSrn.Id,
                            SRNStatusId = 17, // Test
                            ProcessInstanceId = processInstanceId,
                            RolloutStatusId = 4, // Test
                            SignoffDate = null,
                            SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                        };
                        
                        _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                        _dbContext.SRNs.Update(selectedSrn, null);
                        
                        var stagingChange = new StagingChange() {
                            Name = "SRN File Status History Create/Update",
                            OldValue = "Empty",
                            NewValue = "Empty"
                        };
                        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                        var stagingChangeLog = new MemberStagingChangeLogResource();
                        stagingChangeLog.Changes.Add(stagingChange, null);
                        var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                        Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 1B
            // SRN Live, Daily File Live, No Monthly File
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is placed in Test and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is made Live, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 1)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry, null);
                    _dbContext.SaveChanges();
                }
            }

            // Scenario 2A
            // SRN Live, No Daily File, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 1)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 2B
            // SRN Live, No Daily File, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is placed in Test and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is made Live, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 1)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry, null);
                    _dbContext.SaveChanges();
                }
            }

            // Scenario 3A
            // SRN Live, Daily File Live, Monthly File Live
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 3B
            // SRN Live, Daily File Live, Monthly File Live
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 4A
            // SRN Live, Daily File Live, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 4B
            // SRN Live, Daily File Live, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5A
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 0 (workflow exists for testing, null)
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5B
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 1 (workflow exists for testing, null)
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5C
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 0 (workflow exists for testing, null)
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5D
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 1 (workflow exists for testing, null)
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is place back in Test and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is made Live, Daily file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry, null);
                    _dbContext.SaveChanges();
                }
            }

            // Scenario 6A (Future Plan, null)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 0 (workflow exists for testing, null)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 6B (Future Plan, null)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 1 (workflow exists for testing, null)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 6C (Future Plan, null)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 0 (workflow exists for testing, null)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 6D (Future Plan, null)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 1 (workflow exists for testing, null)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is place back in Test and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is made Live, Monthly file is made Live
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Result.Count , null) == 2)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (bool.Parse(processVariables.Find(x => x.Name , null) == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType , null) == (int, null)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name , null) == "srnFileTestingStatusReason").Value
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry, null);
                    _dbContext.SaveChanges();
                }
            }
        }        { 
            var fileType = (SRNStatusFileTypes, null)int.Parse(processVariables.Find(x => x.Name , null) == "fileType").Value);
// COMMENTED OUT TOP-LEVEL STATEMENT:             switch (fileType, null)
            {
                case SRNStatusFileTypes.DailyFile:
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "DailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    break;
                }

                case SRNStatusFileTypes.MonthlyFile:
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory() {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name , null) == "MonthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry, null);
                    _dbContext.SRNs.Update(selectedSrn, null);
                    
                    var stagingChange = new StagingChange() {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn, null);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource, null);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange, null);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog, null);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    break;
                }
            }   
        }
    }
}