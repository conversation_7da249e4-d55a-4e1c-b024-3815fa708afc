using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class SrnStatusUpdateNonCancellationsCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly SRNRepository _srnRepository;

    public SrnStatusUpdateNonCancellationsCamundaService(AppDbContext dbContext, EmailService emailService,
        SRNRepository srnRepository)
    {
        _dbContext = dbContext;
        _emailService = emailService;
        _srnRepository = srnRepository;
    }

    private async Task<List<EventLog>> GetEventLogs(int entityId, int entityTypeId, string changeType = null)
    {
        var eventLogs = _dbContext.EventLogs
            .Where(i => i.EntityId == entityId && i.EntityTypeId == entityTypeId)
            .ToList();

        if (!string.IsNullOrEmpty(changeType) && eventLogs != null)
        {
            eventLogs = eventLogs.Where(i => i.ChangeType == changeType).ToList();
        }

        return eventLogs;
    }

    private StagingChange GetOldSrnStatusFromEventLog(int srnId, int index = 0)
    {
        var entityType = _dbContext.EntityTypes.FirstOrDefault(i => i.Name == "SRN");
        if (entityType != null)
        {
            var eventLogs = GetEventLogs(srnId, entityType.Id, "SRN Update");
            var changesBlob = new List<MemberStagingChangeLogResource>();

            if (eventLogs != null)
            {
                eventLogs = eventLogs.OrderByDescending(i => i.Id).ToList();

                foreach (var log in eventLogs)
                {
                    var stagingChange =
                        JsonConvert.DeserializeObject<MemberStagingChangeLogResource>(log.ChangeBlob);
                    changesBlob.Add(stagingChange);
                }
            }

            var changes = new List<StagingChange>();
            foreach (var change in changesBlob)
            {
                changes.AddRange(change.Changes.Where(i => i.Name == "SRN Status").ToList());
            }

            var changeLog = new StagingChange();
            if (changes != null)
            {
                if (changes.Count > 0)
                {
                    if (index < changes.Count)
                        return changeLog = changes[index];
                }
            }
        }

        return null;
    }

    private static SRNStatusUpdateHistory GetRecentSrnStatusUpdateHistory(
        ICollection<SRNStatusUpdateHistory> srnStatusUpdates)
    {
        if (srnStatusUpdates != null)
        {
            if (srnStatusUpdates.Count > 0)
            {
                srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated).ToList();
                return srnStatusUpdates.First();
            }
        }

        return new SRNStatusUpdateHistory();
    }

    private void AddSRNDailyFileDatesToEmail(ICollection<SRNStatusUpdateHistory> srnStatusUpdates,
        List<KeyValuePair<string, string>> placeholders)
    {
        var recentSRNStatusUpdate = GetRecentSrnStatusUpdateHistory(srnStatusUpdates);

        var dailyFileDevelopmentStartDate = (recentSRNStatusUpdate.DailyFileDevelopmentStartDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileDevelopmentStartDate.Value)
            : "Not Specified";
        var dailyFileDevelopentEndDate = (recentSRNStatusUpdate.DailyFileDevelopmentEndDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileDevelopmentEndDate.Value)
            : "Not Specified";
        var dailyFileTestStartDate = (recentSRNStatusUpdate.DailyFileTestStartDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileTestStartDate.Value)
            : "Not Specified";
        var dailyFileTestEndDate = (recentSRNStatusUpdate.DailyFileTestEndDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileTestEndDate.Value)
            : "Not Specified";
        var dailyGoLiveDate = (recentSRNStatusUpdate.DailyFileGoLiveDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileGoLiveDate.Value)
            : "Not Specified";

        placeholders.Add(new KeyValuePair<string, string>("[DailyFileDevelopmentStartDate]",
            dailyFileDevelopmentStartDate));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileDevelopentEndDate]",
            dailyFileDevelopentEndDate));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileTestStartDate]", dailyFileTestStartDate));
        placeholders.Add(new KeyValuePair<string, string>("[DailyFileTestEndDate]", dailyFileTestEndDate));
        placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
    }

    private void AddSRNMonthlyFileDatesToEmail(ICollection<SRNStatusUpdateHistory> srnStatusUpdates,
        List<KeyValuePair<string, string>> placeholders)
    {
        var recentSRNStatusUpdate = GetRecentSrnStatusUpdateHistory(srnStatusUpdates);

        var monthlyFileDevelopmentStartDate = (recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate.Value)
            : "Not Specified";
        var monthlyFileDevelopentEndDate = (recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate.Value)
            : "Not Specified";
        var monthlyFileTestStartDate = (recentSRNStatusUpdate.MonthlyFileTestStartDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileTestStartDate.Value)
            : "Not Specified";
        var monthlyFileTestEndDate = (recentSRNStatusUpdate.MonthlyFileTestEndDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileTestEndDate.Value)
            : "Not Specified";
        var monthlyGoLiveDate = (recentSRNStatusUpdate.MonthlyFileGoLiveDate != null)
            ? string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileGoLiveDate.Value)
            : "Not Specified";

        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileDevelopmentStartDate]",
            monthlyFileDevelopmentStartDate));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileDevelopentEndDate]",
            monthlyFileDevelopentEndDate));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileTestStartDate]", monthlyFileTestStartDate));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyFileTestEndDate]", monthlyFileTestEndDate));
        placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));
    }

    public void EmailBureausAboutSRNStatusUpdateForNonCancellations(int srnId)
    {
        try
        {
            var selectRecord = _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusReason)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                var srnNumber = (!string.IsNullOrEmpty(selectRecord.SRNNumber))
                    ? selectRecord.SRNNumber
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", srnNumber));
                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var oldSRNStatus = "Not Available";
                var changeLog = GetOldSrnStatusFromEventLog(selectRecord.Id);
                if (changeLog != null)
                {
                    oldSRNStatus = (!string.IsNullOrEmpty(changeLog.OldValue))
                        ? changeLog.OldValue
                        : "Not Available";
                }

                placeholders.Add(new KeyValuePair<string, string>("[OldSRNStatus]", oldSRNStatus));

                var newSRNStatus = (selectRecord.SRNStatus != null) ? selectRecord.SRNStatus.Name : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[NewSRNStatus]", newSRNStatus));

                var reasonForUpdate = (selectRecord.SRNStatusReason != null)
                    ? selectRecord.SRNStatusReason.Name
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[ReasonForUpdate]", reasonForUpdate));

                var lastSubmissionDate = (selectRecord.LastSubmissionDate != null)
                    ? selectRecord.LastSubmissionDate.Value.ToString("yyyy-MM-dd")
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[LastSubmissionDate]", lastSubmissionDate));

                var recentSRNStatusUpdate = GetRecentSrnStatusUpdateHistory(selectRecord.SRNStatusUpdates);

                var bureauInstruction = (!string.IsNullOrEmpty(recentSRNStatusUpdate.BureauInstruction))
                    ? recentSRNStatusUpdate.BureauInstruction
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[BureauInstruction]", bureauInstruction));

                var statusDate = (selectRecord.AccountStatusDate != null)
                    ? selectRecord.AccountStatusDate.Value.ToString("yyyy-MM-dd")
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[StatusDate]", statusDate));

                var comments = (!string.IsNullOrEmpty(recentSRNStatusUpdate.Comments))
                    ? recentSRNStatusUpdate.Comments
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[StatusComment]", comments));

                AddSRNDailyFileDatesToEmail(selectRecord.SRNStatusUpdates, placeholders);
                AddSRNMonthlyFileDatesToEmail(selectRecord.SRNStatusUpdates, placeholders);

                var bureaus = _dbContext.Members
                    .Include(i => i.Contacts)
                    .Where(i => i.MembershipTypeId == MembershipTypes.Bureau && i.RegisteredName != "TRANSUNION")
                    .ToList();

                var contactTypes = _dbContext.ContactTypes
                    .AsNoTracking()
                    .Where(i => i.Name == "Data Contact Details")
                    .ToList();

                foreach (var bureau in bureaus)
                {
                    if (contactTypes != null)
                    {
                        foreach (var type in contactTypes)
                        {
                            var contact = bureau.Contacts
                                .FirstOrDefault(i => i.ContactTypeId == type.Id);

                            if (contact != null)
                            {
                                if (!string.IsNullOrEmpty(contact.Email))
                                    _emailService.SendEmail(contact.Email, contact.FirstName, "SRN Status Update",
                                        "EmailBureausAboutSRNStatusUpdateForNonCancellations.html", placeholders);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email bureau for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public void EmailMemberAboutSRNStatusUpdateForNonCancellations(int srnId)
    {
        try
        {
            var selectRecord = _dbContext.SRNs
                .Include(i => i.Member)
                .ThenInclude(x => x.Contacts)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusReason)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                var srnNumber = (!string.IsNullOrEmpty(selectRecord.SRNNumber))
                    ? selectRecord.SRNNumber
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", srnNumber));
                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var oldSRNStatus = "Not Available";
                var changeLog = GetOldSrnStatusFromEventLog(selectRecord.Id);
                if (changeLog != null)
                {
                    oldSRNStatus = (!string.IsNullOrEmpty(changeLog.OldValue))
                        ? changeLog.OldValue
                        : "Not Available";
                }

                placeholders.Add(new KeyValuePair<string, string>("[OldSRNStatus]", oldSRNStatus));

                var newSRNStatus = (selectRecord.SRNStatus != null) ? selectRecord.SRNStatus.Name : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[NewSRNStatus]", newSRNStatus));

                var reasonForUpdate = (selectRecord.SRNStatusReason != null)
                    ? selectRecord.SRNStatusReason.Name
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[ReasonForUpdate]", reasonForUpdate));

                var comments = (!string.IsNullOrEmpty(selectRecord.Comments)) ? selectRecord.Comments : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[StatusComment]", comments));

                var contactTypes = _dbContext.ContactTypes
                    .AsNoTracking()
                    .Where(i => i.Name == "Main Contact Details"
                                || i.Name == "Alternate Contact Details"
                                || i.Name == "Financial Contact Details")
                    .ToList();

                if (contactTypes != null)
                {
                    foreach (var type in contactTypes)
                    {
                        var contact = selectRecord.Member.Contacts
                            .FirstOrDefault(i => i.ContactTypeId == type.Id);

                        if (contact != null)
                        {
                            if (!string.IsNullOrEmpty(contact.Email))
                                _emailService.SendEmail(contact.Email, contact.FirstName, "SRN Status Update",
                                    "EmailMemberAboutSRNStatusUpdateForNonCancellations.html", placeholders);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email member for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public void UpdateSRNStatus(int srnId, string newStatus, int updatedByUserId = 0,
        string processInstanceId = null, bool isNewSRN = false)
    {
        _srnRepository.UpdateSRNStatus(srnId, newStatus, updatedByUserId, processInstanceId, isNewSRN);
    }
}