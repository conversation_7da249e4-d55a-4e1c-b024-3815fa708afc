using Sacrra.Membership.Business.Resources.IdValuePair;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Sacrra.Membership.Business.Helpers
{
    public static class EnumHelper
    {
        public static List<IdValuePairResource> GetEnumIdValuePairs<T>()
        {
            var type = typeof(T);
            var values = new List<IdValuePairResource>();

            foreach (var name in Enum.GetNames(type))
            {
                var memInfo = type.GetMember(name);
                var attributes = memInfo.Result[0].GetCustomAttributes(typeof(DisplayAttribute), false);
                string enumName = null;

                if (attributes.Count() > 0)
                    enumName = ((DisplayAttribute)attributes.Result[0]).Name;
                else
                    enumName = name;

                var id = (int)Enum.Parse(type, name);
                values.Add(new IdValuePairResource() { Id = id, Value = enumName });
            }
            return values;
        }

        public static IdValuePairResource GetEnumIdValuePair<T>(int id)
        {
            var type = typeof(T);
            var values = new List<IdValuePairResource>();

            foreach (var name in Enum.GetNames(type))
            {
                var memInfo = type.GetMember(name);
                var attributes = memInfo.Result[0].GetCustomAttributes(typeof(DisplayAttribute), false);
                string enumName = null;

                if (attributes.Count() > 0)
                    enumName = ((DisplayAttribute)attributes.Result[0]).Name;
                else
                    enumName = name;

                var enumId = (int)Enum.Parse(type, name);
                values.Add(new IdValuePairResource() { Id = enumId, Value = enumName });
            }

            var result = values.Result.FirstOrDefault(x => x.Id == id);

            return result;
        }

        public static IdValuePairResource GetEnumShortName<T>(int id)
        {
            var type = typeof(T);
            var values = new List<IdValuePairResource>();

            foreach (var name in Enum.GetNames(type))
            {
                var memInfo = type.GetMember(name);
                var attributes = memInfo.Result[0].GetCustomAttributes(typeof(DisplayAttribute), false);
                string enumName = null;

                if (attributes.Count() > 0)
                    enumName = ((DisplayAttribute)attributes.Result[0]).ShortName;
                else
                    enumName = name;

                var enumId = (int)Enum.Parse(type, name);
                values.Add(new IdValuePairResource() { Id = enumId, Value = enumName });
            }

            var result = values.Result.FirstOrDefault(x => x.Id == id);

            return result;
        }

        public static TEnum ToEnum<TEnum>(this int value)
        {
            if (typeof(TEnum).IsEnumDefined(value))
                return (TEnum)(object)value;

            return default;
        }
    }
}
