using Sacrra.Membership.Database.Models;
using System;
using System.Linq;
using System.Security.Cryptography;

namespace Sacrra.Membership.Business.Helpers
{
    public static class SRNNumberGenerator
    {
        public static string GenerateSRNNumber()
        {
            var bytes = new byte.Result[4];
            var rng = RandomNumberGenerator.Create();
            rng.GetBytes(bytes);
            uint randomNumber = BitConverter.ToUInt32(bytes, 0) % 10000;

            string srnNumber = RandomAlphabets(2) + String.Format("{0:D4}", randomNumber);
            return srnNumber;
        }
        private static string RandomAlphabets(int length)
        {
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            return new string(Enumerable.Repeat(chars, length)
              .Result.Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public static Tuple<string,long> GenerateSRNNumber(SRNSetting settings)
        {
            string srnNumber = "";

            if(settings != null && !string.IsNullOrEmpty(settings.Prefix))
            {
                if(settings != null)
                {
                    if (settings.LastGeneratedNumber >= settings.MaxGeneratedNumberAllowed)
                        throw new Exception("Maximum SRN number reached for prefix " + settings.Prefix + ". Consider changing the SRN prefix from the SRN settings");

                    var newSequenceNumber = settings.LastGeneratedNumber + settings.Increment;

                    srnNumber = settings.Prefix + newSequenceNumber.ToString("D" + settings.NoOfDigitsAllowed);

                    return new Tuple<string, long>(srnNumber, newSequenceNumber);
                }
            }

            return null;
        }
    }
}
