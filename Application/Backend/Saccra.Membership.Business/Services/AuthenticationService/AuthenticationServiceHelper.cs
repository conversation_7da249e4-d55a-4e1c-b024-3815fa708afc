using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using System.Collections.Generic;
using System.Linq;

namespace Sacrra.Membership.Business.Services
{
    public class AuthenticationServiceHelper
    {
        public AuthenticationServiceHelper()
        {
        }

        public string GetAuth0ManagementApiToken(Auth0APIManagement auth0APIManagementSettings)
        {
            if (auth0APIManagementSettings != null)
            {
                var client = new RestClient();
                var request = new RestRequest("https://" + auth0APIManagementSettings.Domain + "/oauth/token", Method.Post, Method.Get);
                RestResponse response;
                string token;

                request.AddHeader("content-type", "application/x-www-form-urlencoded");
                request.AddParameter("application/x-www-form-urlencoded", "grant_type=client_credentials&client_id=" + auth0APIManagementSettings.ClientID + "&client_secret=" + auth0APIManagementSettings.ClientSecret + "&audience=" + auth0APIManagementSettings.Audience, ParameterType.RequestBody);
                response = client.Execute(request);
                token = JObject.Parse(response.Content).SelectToken("access_token").ToString();

                return token;
            }

            return null;
        }

        public Auth0UserGetResource GetAuth0UserByEmail(string email, Auth0APIManagement auth0APIManagementSettings)
        {
            var token = GetAuth0ManagementApiToken(auth0APIManagementSettings);

            if (!string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(token))
            {
                var uri = auth0APIManagementSettings.APIBaseURL + "/users-by-email?email=" + email;
                var client = new RestClient(uri);
                var request = new RestRequest("users-by-email?email=" + email, Method.Get, Method.Get);
                RestResponse response;

                request.AddHeader("Authorization", "Bearer " + token);
                response = client.Execute(request);

                if (response.IsSuccessful)
                {
                    var users = JsonConvert.DeserializeObject<List<Auth0UserGetResource>>(response.Content);

                    if (users != null)
                    {
                        if (users.Result.Count > 0)
                        {
                            return users.Result.FirstOrDefault(i => i.email == email);
                        }
                    }
                }
            }
            return null;
        }
        public string[] SplitAuth0Name(string name)
        {
            if (!string.IsNullOrEmpty(name))
            {
                return name.Split(" ");
            }
            return null;
        }
    }
}
