
using AutoMapper;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System;
using System.Linq;
using System.Net.Http;
using System.Text;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using Sacrra.Membership.Database.Enums;
using Microsoft.Extensions.Configuration;
using System.IO;
using Sacrra.Membership.Notification.Helpers;
using Microsoft.AspNetCore.Hosting;
using RestSharp;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.AuthDTOs;

namespace Sacrra.Membership.Business.Services
{
    public class AuthenticationService
    {
        private readonly AppDbContext _dbContext;
        private readonly AuthenticationServiceHelper _authenticationServiceHelper;
        private readonly GlobalHelper _globalHelper;
        private readonly Auth0APIManagement _auth0APIManagementSettings;
        public IMapper _mapper { get; }
        private readonly IConfiguration _configuration;
        private readonly StringHelper _stringHelper;

        public AuthenticationService(AppDbContext dbContext, AuthenticationServiceHelper authenticationServiceHelper, IMapper mapper, 
            GlobalHelper globalHelper, IOptions<Auth0APIManagement> auth0APIManagementSettings, IConfiguration configuration)
        {
            _dbContext = dbContext;
            _authenticationServiceHelper = authenticationServiceHelper;
            _mapper = mapper;
            _globalHelper = globalHelper;
            _auth0APIManagementSettings = auth0APIManagementSettings.Value;
            _configuration = configuration;
            _stringHelper = new StringHelper();
        }

        public void SaveAuth0User(AuthInputDTO authInputDTO)
        {
            var existingUser = _dbContext.Users.FirstOrDefault(user => user.Auth0Id == authInputDTO.Auth0Id);

            if(existingUser == null)
            {
                existingUser = _dbContext.Users.FirstOrDefault(user => user.Email == authInputDTO.Email);
            }
            
            if (existingUser == null)
            {
                var newUser = new User
                {
                    Auth0Id = authInputDTO.Auth0Id,
                    Email = authInputDTO.Email,
                    FirstName = authInputDTO.Email,
                    LastName = authInputDTO.Email,
                    RequirePasswordChange = false,
                    IsEmailConfirmed = true,
                    RoleId = UserRoles.User,
                    DateCreated = DateTime.Now
                };

                _dbContext.Users.Add(newUser);
                _dbContext.SaveChanges();
            }
            else
            {
                throw new UserExistsException();
            }
        }

        public void ResendEmailVerification(EmailVerificationInputDTO emailVerificationInputDTO)
        {
            var localUser = _dbContext.Users.FirstOrDefault(user => user.Email == emailVerificationInputDTO.EmailAddress);

            if (!string.IsNullOrEmpty(localUser.Auth0Id))
            {
                using var client = new HttpClient();
                var token = _authenticationServiceHelper.GetAuth0ManagementApiToken(_auth0APIManagementSettings);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, "https://" + _auth0APIManagementSettings.Domain + "/api/v2/jobs/verification-email")
                {
                    Content = new StringContent(JsonConvert.SerializeObject(new
                    {
                        user_id = localUser.Auth0Id,
                        client_id = emailVerificationInputDTO.ClientId
                    }
                    ), Encoding.UTF8, "application/json"),
                };

                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

                var result = client.Send(webRequest);
                result.EnsureSuccessStatusCode();
            }
        }

        public bool SendPasswordResetEmail(AuthPasswordResetInputDTO passwordResetDTO)
        {
            bool isSuccessful = false;
            if (passwordResetDTO != null)
            {
                if (!string.IsNullOrEmpty(passwordResetDTO.Email))
                {
                    using var client = new HttpClient();
                    var webRequest = new HttpRequestMessage(HttpMethod.Post, "https://" + _configuration.GetSection("Auth0:Domain").Value + "/dbconnections/change_password")
                    {
                        Content = new StringContent(JsonConvert.SerializeObject(new
                        {
                            client_id = _configuration.GetSection("Auth0:WebAppClientID").Value,
                            email = passwordResetDTO.Email,
                            connection = "Username-Password-Authentication",
                        }
                        ), Encoding.UTF8, "application/json"),
                    };

                    var result = client.Send(webRequest);
                    result.EnsureSuccessStatusCode();

                    isSuccessful = true;
                    return isSuccessful;
                }         
            }

            return isSuccessful;
        }

        public RestResponse UpdateAuth0LoginTemplate()
        {
            string auth0LoginTemplate = GetAuth0LoginTemplate();

            if (!string.IsNullOrWhiteSpace(auth0LoginTemplate))
            {
                var client = new RestClient($"https://{ _auth0APIManagementSettings.Domain }/api/v2");
                var request = new RestRequest("branding/templates/universal-login", Method.Put);

                var token = _authenticationServiceHelper.GetAuth0ManagementApiToken(_auth0APIManagementSettings);

                request.AddHeader("Authorization", $"Bearer {token}");
                request.AddHeader("Content-Type", "text/html");
                request.AddParameter("text/html", auth0LoginTemplate, ParameterType.RequestBody);
                var response = client.Execute(request);

                return response;
            }

            return null;
        }
        private string GetAuth0LoginTemplate()
        {
            string auth0LoginTemplateName = _configuration["ConfigSettings:Auth0LoginTemplate"];
            string templateFilePath = _configuration["ConfigSettings:ConfigTemplatesPath"];
            string fullTemplateFilePath;


            if (!string.IsNullOrWhiteSpace(templateFilePath))
            {
                fullTemplateFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", templateFilePath, auth0LoginTemplateName);

                if (!string.IsNullOrWhiteSpace(fullTemplateFilePath))
                {
                    string auth0LoginTemplate = _stringHelper.GetContentTemplateFromFile(fullTemplateFilePath);

                    if (!string.IsNullOrWhiteSpace(auth0LoginTemplate))
                    {
                        return auth0LoginTemplate;
                    }
                }
            }

            return null;
        }
    }
}
