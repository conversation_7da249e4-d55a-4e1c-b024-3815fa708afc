using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using Sacrra.Membership.Reporting.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;

namespace Sacrra.Membership.Business.Services.ReplacementFileSubmissionService
{
    public class ReplacementFileSubmissionService
    {
        private AppDbContext _dbContext;
        private readonly ConfigSettings _configSettings;
        private readonly EmailService _emailService;
        private DataWarehouseService.DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;

        public ReplacementFileSubmissionService(AppDbContext dbContext, IOptions<ConfigSettings> configSettings, EmailService emailService, DataWarehouseService.DataWarehouseService dataWarehouseService)
        {
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _emailService = emailService;
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
        }

        public RestResponse RequestReplacementFileSubmission(ReplacementFileSubmissionInputDTO inputDto)
        {
            Member replacementFileMember;
            var restClient = new RestClient();
            object variables;
            var user = Helpers.Helpers.GetUserByAuth0IdNonAsync(_dbContext);
            var replacementFileSpNumber = _dbContext.SPGroups.Result.FirstOrDefault(x => x.SPNumber == inputDto.SPNumber);
            var replacementFileSrn = _dbContext.SRNs.Result.FirstOrDefault(x => x.SRNNumber == inputDto.SRNNumber);
            
            if (replacementFileSrn == null)
            {
                throw new Exception($"SRN ({inputDto.SRNNumber}) was not found.");
            }

            if (replacementFileSpNumber == null)
            {
                replacementFileMember = _dbContext.Members.Result.Where(x => x.Id == replacementFileSrn.MemberId)
                    .Include(x => x.StakeholderManager)
                    .Result.FirstOrDefault();
            } else
            {
                replacementFileMember = _dbContext.Members.Result.Where(x => x.Id == replacementFileSpNumber.MemberId)
                    .Include(x => x.StakeholderManager)
                    .Result.FirstOrDefault();
            }

            if (replacementFileMember == null)
            {
                throw new Exception($"Member linked to SRN ({inputDto.SRNNumber}) was not found.");
            }

            if (replacementFileMember.IndustryClassificationId != null)
            {
                var replacementFileSubmission = new ReplacementFileSubmission
                {
                    SRNId = replacementFileSrn.Id,
                    SPId = replacementFileSpNumber?.Id,
                    MemberId = replacementFileMember.Id,
                    FileName = inputDto.FileName,
                    ReplacementFileName = inputDto.ReplacementFileName,
                    NumberOfRecords = inputDto.NumberOfRecords,
                    NumberOfFiles = inputDto.NumberOfFiles,
                    ReplacementFileSubmissionStatusId = (int)ReplacementFileSubmissionStatuses.Requested,
                    FileSubmissionTypeId = 1,
                    SubmissionStatusDate = DateTime.Now,
                    ReplacementFileSubmissionReasonId = inputDto.ReasonForReplacementFileId,
                    SACRRAIndustry = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)replacementFileMember.IndustryClassificationId)?.Value,
                    SACRRAAccountType = _dbContext.AccountTypes.Result.FirstOrDefault(x => x.Id == replacementFileSrn.AccountTypeId)?.Name,
                    IsDeleted = false,
                    CreatedAt = DateTime.Now,
                    LastUpdatedAt = DateTime.Now,
                    PlannedSubmissionDate = DateTime.Parse(inputDto.PlannedSubmissionDate),
                    ActualSubmissionDate = null,
                    DWDateCreated = null,
                    ReplacementFileSubmissionDeclineReasonId = null,
                    SubmissionLastCheckedDate = null
                };

                try
                {
                    _dbContext.ReplacementFileSubmissions.Add(replacementFileSubmission);
                    _dbContext.SaveChanges();
                
                    var updateDetailsBlob = JsonConvert.SerializeObject(replacementFileSubmission);
                    var stagingChangeLog = new StagingChange
                    {
                        Name = "Adhoc Replacement",
                        OldValue = "",
                        NewValue = inputDto.FileName
                    };
                    var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    //Update EventLog
                    Helpers.Helpers.CreateEventLogNonAsync(_dbContext, user.Id, "Adhoc Replacement", inputDto.FileName, updateDetailsBlob, stagingDetailsBlob, replacementFileMember.Id, "Adhoc");
                } catch (Exception exception)
                {
                    throw new Exception("Unable to save replacement file request to DB.", exception);
                }

                if (replacementFileSrn != null && replacementFileSpNumber == null)
                {
                    variables = new
                    {
                        variables = new
                        {
                            SRNId = new
                            {
                                value = replacementFileSrn.Id,
                                type = "long"
                            },
                            SHMId = new
                            {
                                value = replacementFileMember.StakeholderManagerId,
                                type = "long"
                            },
                            FileSubmissionRequestId = new
                            {
                                value = replacementFileSubmission.Id,
                                type = "long"
                            },
                            plannedSubmissionDate = new
                            {
                                value = DateTime.Parse(inputDto.PlannedSubmissionDate).AddDays(1).ToString("yyyy-MM-dd"),
                                type = "string"
                            }
                        }
                    };
                } else
                {
                    variables = new
                    {
                        variables = new
                        {
                            SPId = new
                            {
                                value = replacementFileSpNumber.Id,
                                type = "long"
                            },
                            SHMId = new
                            {
                                value = replacementFileMember.StakeholderManagerId,
                                type = "long"
                            },
                            FileSubmissionRequestId = new
                            {
                                value = replacementFileSubmission.Id,
                                type = "long"
                            },
                            plannedSubmissionDate = new
                            {
                                value = DateTime.Parse(inputDto.PlannedSubmissionDate).AddDays(1).ToString("yyyy-MM-dd"),
                                type = "string"
                            }
                        }
                    };
                }

                try
                {
                    return restclient.Execute(new RestRequest(new RestRequest(_configSettings.CamundaBaseAddress + "/process-definition/key/Replacement-File-Submissions/start")
                        .AddJsonBody(JsonConvert.SerializeObject(variables)));
                } catch (Exception exception)
                {
                    replacementFileSubmission.IsDeleted = true;
                    replacementFileSubmission.ReasonForDeletion = "Camunda task creation failed.";

                    _dbContext.ReplacementFileSubmissions.Update(replacementFileSubmission);
                    _dbContext.SaveChanges();

                    throw new Exception("Camunda task creation failed.", Method.Post).AddJsonBody(exception));
                }
            }

            return null;
        }

        public List<ReplacementFileSubmissionOutputDTO> GetApprovedReplacementFiles()
        {
            var approvedReplacementFileRequestList = new List<ReplacementFileSubmissionOutputDTO>();
            var approvedReplacementFileRequests = _dbContext.ReplacementFileSubmissions
                .Result.Where(x => x.ReplacementFileSubmissionStatusId == (int)ReplacementFileSubmissionStatuses.Submitted)
                .Include(x => x.SRN)
                .Include(x => x.Member)
                .ThenInclude(x => x.StakeholderManager)
                .ToList();

            approvedReplacementFileRequests.ForEach(approvedReplacementFileRequest =>
            {
                var dto = new ReplacementFileSubmissionOutputDTO
                {
                    Id = approvedReplacementFileRequest.Id,
                    NumberOfFiles = approvedReplacementFileRequest.NumberOfFiles,
                    NumberOfRecords = approvedReplacementFileRequest.NumberOfRecords,
                    SRNNumber = approvedReplacementFileRequest.SRN == null ? "N/A" : approvedReplacementFileRequest.SRN.SRNNumber,
                    ReplacementFileName = approvedReplacementFileRequest.ReplacementFileName,
                    MemberName = approvedReplacementFileRequest.Member.RegisteredName,
                    FileSubmissionReason = _dbContext.ReplacementFileSubmissionReasons.Result.Where(reason => reason.Id == approvedReplacementFileRequest.ReplacementFileSubmissionReasonId).Result.FirstOrDefault().Name,
                    SRNDisplayName = approvedReplacementFileRequest.SRN == null ? "N/A" : approvedReplacementFileRequest.SRN.TradingName,
                    StakeHolderManager = approvedReplacementFileRequest.Member.StakeholderManager.Result.FullName,
                    PlannedSubmissionDate = approvedReplacementFileRequest.PlannedSubmissionDate.ToString("yyyy-MM-dd"),
                    OriginalFileName = approvedReplacementFileRequest.FileName,
                    SPNumber = approvedReplacementFileRequest.SRN == null ? _dbContext.SPGroups.Result.Where(x => x.Id == approvedReplacementFileRequest.SPId).Result.FirstOrDefault().SPNumber : "N/A",
                    ActualSubmissionDate = approvedReplacementFileRequest.ActualSubmissionDate == null ? "N/A" : approvedReplacementFileRequest.ActualSubmissionDate?.ToString("yyyy-MM-dd")
                };

                approvedReplacementFileRequestList.Add(dto);
            });

            return approvedReplacementFileRequestList;
        }

        public void SubmitUnsuccessfulReplacementLoad(BureauUnsuccessfulLoadInputDTO inputDTO, ClaimsPrincipal user)
        {
            try
            {
                var auth0User = Helpers.Helpers.GetLoggedOnUser(_dbContext, user);
                var currentUser = _dbContext.Users
                    .Result.Where(x => x.Auth0Id == auth0User.Auth0Id)
                    .Include(x => x.Members)
                    .Result.FirstOrDefault();
                var bureauUnsuccessfulLoadModel = _dbContext.ReplacementFileSchedule
                    .Result.Where(x => x.ReplacementFileSubmissionId == inputDTO.AdhocFileSubmissionId && x.BureauId == currentUser.Members.First().MemberId)
                    .Result.FirstOrDefault();
                var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                    .Result.Where(x => x.ReplacementFileName == inputDTO.AdHocFileName)
                    .Include(x => x.SRN)
                    .Result.FirstOrDefault();

                if (replacementFileSubmission != null)
                {
                    var spNumber = _dbContext.SPGroups
                    .Result.Where(x => x.Id == replacementFileSubmission.SPId)
                    .Result.FirstOrDefault();
                    var srn = _dbContext.SRNs
                        .Result.Where(x => x.Id == replacementFileSubmission.SRNId)
                        .Include(x => x.Contacts)
                        .Result.FirstOrDefault();
                    var member = srn == null ? _dbContext.Members
                        .Result.Where(x => x.Id == spNumber.MemberId)
                        .Include(x => x.Contacts)
                        .Result.FirstOrDefault() : _dbContext.Members
                        .Result.Where(x => x.Id == srn.MemberId)
                        .Include(x => x.Contacts)
                        .Result.FirstOrDefault();

                    bureauUnsuccessfulLoadModel.UnnsuccessfulLoadReasonId = inputDTO.UnsuccessfullLoadReasonId;
                    bureauUnsuccessfulLoadModel.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadUnsuccessful;

                    _dbContext.ReplacementFileSchedule.Update(bureauUnsuccessfulLoadModel);
                    _dbContext.SaveChanges();

                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[Bureau]", member.RegisteredName),
                        new KeyValuePair<string, string>("[FileName]", inputDTO.AdHocFileName)
                    };
                    if (srn == null)
                    {
                        var dataContact = member.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 1);
                        _emailService.SendEmail(dataContact.Email, dataContact.FirstName, "Bureau Unsuccessful Load", "BureauUnsuccessfulLoad.html", placeholders, null, "", "", spNumber.Id, WorkflowEnum.NotApplicable, EmailReasonEnum.BureauUnsuccessfulLoad, EmailRecipientTypeEnum.Member);
                    }

                    if (spNumber == null)
                    {
                        var dataContact = srn.Contacts.Result.FirstOrDefault(x => x.ContactTypeId == 5);
                        _emailService.SendEmail(dataContact.Email, dataContact.FirstName, "Bureau Unsuccessful Load", "BureauUnsuccessfulLoad.html", placeholders, null, "", "", srn.Id, WorkflowEnum.NotApplicable, EmailReasonEnum.BureauUnsuccessfulLoad, EmailRecipientTypeEnum.Member);
                    }
                }
                else
                {
                    throw new Exception($"Replacement file with name {inputDTO.AdHocFileName} was not found.");
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Unable to create unsuccessful load.");
            }
        }

        public ActionResult SetSuccessfulFileLoad(BureauSuccessfulLoadInputDTO data)
        {
            var bureauSuccessfulLoadModel = _dbContext.ReplacementFileSchedule
                    .Result.Where(x => x.ReplacementFileSubmissionId == data.ReplacementFileSubmissionId)
                    .Result.FirstOrDefault();

            bureauSuccessfulLoadModel.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadSuccessful;
            bureauSuccessfulLoadModel.UnnsuccessfulLoadReasonId = null;

            _dbContext.ReplacementFileSchedule.Update(bureauSuccessfulLoadModel);
            _dbContext.SaveChanges();

            return new OkResult();

        }

        public async Task<List<ReplacementFileScheduleOutputDTO>> GetReplacementFileSchedule(ClaimsPrincipal user)
        {
            try
            {
                var auth0User = Helpers.Helpers.GetLoggedOnUser(_dbContext, user);
                var currentUser = _dbContext.Users.Result.Where(x => x.Auth0Id == auth0User.Auth0Id)
                    .Include(x => x.Members)
                    .ThenInclude(x => x.Member.StakeholderManager)
                    .Result.FirstOrDefault();
                var replacementFileScheduleList = new List<ReplacementFileSchedule>();

                //var dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause = $"1 = 1";
                //DataWarehouseAPIModel dailyAndMonthlyFileSubmissionsPerBureauAndStatusApiCallModel;

                switch (auth0User.RoleId)
                {
                    case UserRoles.StakeHolderManager:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Result.Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.StakeHolderAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Result.Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.FinancialAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Result.Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.Member:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.Bureau)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .Result.Where(x => x.ReplacementFileSubmission.MemberId == currentUser.Members.Result.FirstOrDefault().Member.Id)
                            .ToList();
                        break;

                    case UserRoles.SACRRAAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Result.Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.Bureau:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .Result.Where(x => x.BureauId == currentUser.Members.Result.FirstOrDefault().Member.Id)
                            .ToList();
                        break;

                    case UserRoles.User:
                        throw new UnauthorizedException();

                    case UserRoles.ALGLeader:
                        var replacementFileScheduleALGLeaderList = _dbContext.ReplacementFileSchedule
                            .Result.Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();

                        var currentUserMembers = currentUser.Members.ToList();

                        for (int i = 0; i < currentUserMembers.Count; i++)
                        {
                            var member = currentUserMembers[i];

                            for (int j = 0; j < replacementFileScheduleALGLeaderList.Count; j++)
                            {
                                var replacementFileScheduleItem = replacementFileScheduleALGLeaderList[j];

                                if (replacementFileScheduleItem.ReplacementFileSubmission.MemberId == member.MemberId)
                                {
                                    replacementFileScheduleList.Add(replacementFileScheduleItem);
                                }
                            }
                        }
                        break;

                    case UserRoles.SystemAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Result.Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    default:
                        break;
                }

                if (replacementFileScheduleList.Result.Count > 0)
                {
                    //var counter = 0;
                    //var previousFileName = "";

                    //dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $" AND IsLatest = 1 AND FileName IN (";

                    //foreach (var item in replacementFileScheduleList)
                    //{
                    //    if (previousFileName == "")
                    //    {
                    //        if (counter < (replacementFileScheduleList.Count - 1))
                    //        {
                    //            dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $"'{item.ReplacementFileName}'";
                    //        }
                    //    }

                    //    if (previousFileName != item.ReplacementFileName && previousFileName != "")
                    //    {
                    //        if (counter < (replacementFileScheduleList.Count - 1))
                    //        {
                    //            dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $", '{item.ReplacementFileName}'";
                    //        }
                    //    }

                    //    previousFileName = item.ReplacementFileName;
                    //    counter++;
                    //}

                    //dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $")";
                    //dailyAndMonthlyFileSubmissionsPerBureauAndStatusApiCallModel = new DataWarehouseAPIModel()
                    //{
                    //    Columns = "*",
                    //    Where = dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause
                    //};

                    var replacementFileScheduleOutputDTOList = new List<ReplacementFileScheduleOutputDTO>();
                    //var dwReplacementFile = _dataWarehouseService.GetResultArray<ReplacementFileBureauOutputDTO>(_reportTables.DailyAndMonthlyFileSubmissionsPerBureauAndStatus, dailyAndMonthlyFileSubmissionsPerBureauAndStatusApiCallModel)
                    //    .ToList();
                    //var replacementFileSubmissionReasons = _dbContext.ReplacementFileSubmissionReasons.ToList();
                    //var algClientLeaders = _dbContext.ALGClientLeaders.Include(x => x.Leader).ToList();

                    foreach (var replacementFileSchedule in replacementFileScheduleList)
                    {
                        //var dwReplacementFileForBureau = dwReplacementFile
                        //    .Result.Where(x => x.FileName == replacementFileSchedule.ReplacementFileName)
                        //    .Result.Where(x => x.BureauName.ToLower() == bureauName.ToLower())
                        //    .Result.FirstOrDefault();

                        //if (dwReplacementFileForBureau != null && dwReplacementFileForBureau.IsFileLoaded)
                        //{
                        //    replacementFileSchedule.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadSuccessful;
                        //}
                        //else
                        //{
                        //    if (replacementFileSchedule.ReplacementFileBureauStatusId != ReplacementFileBureauStatuses.BureauLoadPending
                        //        && replacementFileSchedule.ReplacementFileBureauStatusId != ReplacementFileBureauStatuses.BureauLoadUnsuccessful
                        //        && replacementFileSchedule.ReplacementFileBureauStatusId != null)
                        //    {
                        //        replacementFileSchedule.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadPending;
                        //    }
                        //}

                        var replacementFileAlgLeader = _dbContext.ALGClientLeaders
                            .Result.Where(x => x.ClientId == replacementFileSchedule.ReplacementFileSubmission.Member.Id)
                            .Include(x => x.Leader)
                            .Result.FirstOrDefault();

                        var replacementFileScheduleOutputDTO = new ReplacementFileScheduleOutputDTO
                        {
                            ActualSubmissionDate = replacementFileSchedule.ReplacementFileSubmission.ActualSubmissionDate == null
                            ? "N/A"
                            : replacementFileSchedule.ReplacementFileSubmission.ActualSubmissionDate?.ToString("dd-MM-yyyy"),
                            AlgLeader = replacementFileAlgLeader == null ? "N/A" : replacementFileAlgLeader.Leader.RegisteredName,
                            BureauReasonForUnsuccessfulLoad = replacementFileSchedule.UnnsuccessfulLoadReason == null ? "N/A" : replacementFileSchedule.UnnsuccessfulLoadReason.Name,
                            MemberName = replacementFileSchedule.ReplacementFileSubmission.Member.RegisteredName,
                            NumberOfRecords = replacementFileSchedule.ReplacementFileSubmission.NumberOfRecords,
                            ProposedSubmissionDate = replacementFileSchedule.ReplacementFileSubmission.PlannedSubmissionDate.ToString("dd-MM-yyyy"),
                            ReplacementFileName = replacementFileSchedule.ReplacementFileSubmission.ReplacementFileName,
                            ReSubmissionReason = replacementFileSchedule.ReplacementFileSubmission.ReplacementFileSubmissionReason.Name,
                            SACRRAAccountType = replacementFileSchedule.ReplacementFileSubmission.SACRRAAccountType,
                            SACRRAIndustry = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)replacementFileSchedule.ReplacementFileSubmission.Member.IndustryClassificationId).Value,
                            SRNDisplayName = replacementFileSchedule.ReplacementFileSubmission.SRN == null ? "N/A" : replacementFileSchedule.ReplacementFileSubmission.SRN.TradingName,
                            SRNNumber = replacementFileSchedule.ReplacementFileSubmission.SRN == null ? "N/A" : replacementFileSchedule.ReplacementFileSubmission.SRN.SRNNumber,
                            StakeholderManager = replacementFileSchedule.ReplacementFileSubmission.Member.StakeholderManager.Result.FullName,
                            SubmissionStatus = EnumHelper.GetEnumIdValuePair<ReplacementFileSubmissionStatuses>(replacementFileSchedule.ReplacementFileSubmission.ReplacementFileSubmissionStatusId).Value,
                            SubmissionStatusDate = replacementFileSchedule.ReplacementFileSubmission.SubmissionStatusDate.ToString("dd-MM-yyyy"),
                            BureauLoadStatus = replacementFileSchedule.ReplacementFileBureauStatusId == null ? "N/A" : EnumHelper.GetEnumIdValuePair<ReplacementFileBureauStatuses>((int)replacementFileSchedule.ReplacementFileBureauStatusId).Value,
                            BureauName = replacementFileSchedule.Bureau == null ? "N/A" : replacementFileSchedule.Bureau.RegisteredName,
                            RequestDate = replacementFileSchedule.ReplacementFileSubmission.CreatedAt.ToString("dd-MM-yyyy")
                        };

                        replacementFileScheduleOutputDTOList.Add(replacementFileScheduleOutputDTO);
                    }

                    //_dbContext.SaveChanges();
                    return replacementFileScheduleOutputDTOList;
                }

                return new List<ReplacementFileScheduleOutputDTO>();
            }
            catch (Exception ex)
            {
                throw new ReplacementFileScheduleException(0, null, ex.StackTrace);
            }
        }

        public ReplacementFileSubmissionOutputDTO GetReplacementFileByName(string FileName)
        {
            var ReplacementFileOutput = new ReplacementFileSubmissionOutputDTO();
            var replacementFileSubmissions = _dbContext.ReplacementFileSubmissions
                .Include(x => x.SRN)
                .Include(x => x.Member)
                .ThenInclude(x => x.StakeholderManager)
                .Result.FirstOrDefault(x => x.ReplacementFileName == FileName);

            if (replacementFileSubmissions != null)
            {
                ReplacementFileOutput.Id = replacementFileSubmissions.Id;
                ReplacementFileOutput.NumberOfFiles = replacementFileSubmissions.NumberOfFiles;
                ReplacementFileOutput.NumberOfRecords = replacementFileSubmissions.NumberOfRecords;
                ReplacementFileOutput.SRNNumber = replacementFileSubmissions.SRN == null ? "N?A" : replacementFileSubmissions.SRN.SRNNumber;
                ReplacementFileOutput.ReplacementFileName = replacementFileSubmissions.ReplacementFileName;
                ReplacementFileOutput.MemberName = replacementFileSubmissions.Member.RegisteredName;
                ReplacementFileOutput.FileSubmissionReason = _dbContext.ReplacementFileSubmissionReasons.Result.Where(reason => reason.Id == replacementFileSubmissions.ReplacementFileSubmissionReasonId).Result.FirstOrDefault().Name;
                ReplacementFileOutput.SRNDisplayName = replacementFileSubmissions.SRN == null ? "N/A" : replacementFileSubmissions.SRN.TradingName;
                ReplacementFileOutput.StakeHolderManager = replacementFileSubmissions.Member.StakeholderManager.FullName;
                ReplacementFileOutput.PlannedSubmissionDate = replacementFileSubmissions.PlannedSubmissionDate.ToString("yyyy-MM-dd");
                ReplacementFileOutput.OriginalFileName = replacementFileSubmissions.FileName;
                ReplacementFileOutput.SPNumber = replacementFileSubmissions.SRN == null ? _dbContext.SPGroups.Result.Where(x => x.Id == replacementFileSubmissions.SPId).Result.FirstOrDefault().SPNumber : "N/A";
                ReplacementFileOutput.ActualSubmissionDate = replacementFileSubmissions.ActualSubmissionDate == null ? "N/A" : replacementFileSubmissions.ActualSubmissionDate?.ToString("yyyy-MM-dd");

                return ReplacementFileOutput;
            }
            else
            {
                return ReplacementFileOutput;
            }
        }
    }
}