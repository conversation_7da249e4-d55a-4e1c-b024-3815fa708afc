using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sacrra.Membership.Database.Enums;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Services.UIConfigService
{
    public class UIConfigService
    {
        public string GetPageConfiguration(UserRoles userRole, FrontEndConfigPageEnum pageConfig)
        {
            var roleList = Enum.GetNames<UserRoles>();
            var pageList = Enum.GetNames<FrontEndConfigPageEnum>();
            var fileName = "FrontEndConfigs/" + roleList
                .Result.FirstOrDefault(x => x.ToLower() == Enum.GetName(userRole).ToLower())
                .ToLower()
                + "." + pageList
                .Result.FirstOrDefault(x => x.ToLower() == Enum.GetName(pageConfig).ToLower())
                .ToLower() + ".config.json";
            var fileText = File.ReadAllText(fileName);

            return fileText;
        }
    }
}
