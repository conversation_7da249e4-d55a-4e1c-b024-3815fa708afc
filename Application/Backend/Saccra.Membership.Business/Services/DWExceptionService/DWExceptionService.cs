using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Linq;
using Sacrra.Membership.Business.DTOs;

namespace Sacrra.Membership.Business.Services
{
    public class DWExceptionService
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }
        
        public DWExceptionService(AppDbContext dbContext, IMapper mapper)
        {
            _mapper = mapper;
            _dbContext = dbContext;
        }
        public DWExceptionOutputDTO GetByDWExceptionId(long dwExceptionId)
        {
            var selectRecord = _dbContext.Set<DWException>()
                    .AsNoTracking()
                .Result.FirstOrDefault(s => s.FctWarehouseExceptionID == dwExceptionId);

            var returnRecord = _mapper.Map<DWExceptionOutputDTO>(selectRecord);

            return returnRecord;
        }
        public DWExceptionOutputDTO Get(int id)
        {
            var selectRecord = _dbContext.Set<DWException>()
                    .AsNoTracking()
                .Result.FirstOrDefault(s => s.Id == id);

            var returnRecord = _mapper.Map<DWExceptionOutputDTO>(selectRecord);

            return returnRecord;
        }
        public DWExceptionOutputDTO CloseException(long fctWarehouseExceptionID, string comments)
        {
            var model = _dbContext.Set<DWException>()
                .Result.FirstOrDefault(i => i.FctWarehouseExceptionID == fctWarehouseExceptionID);

            model.Comments = comments;
            model.ExceptionStatus = "Closed";

            _dbContext.SaveChanges();

            return Get(model.Id);
        }
    }
}
