using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Authenticators;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System;
using System.Linq;
using System.ComponentModel;
using System.Data;
using System.Collections.Generic;
using Sacrra.Membership.Notification.Repositories;
using Newtonsoft.Json.Linq;
using Sacrra.Membership.Business.DTOs;

namespace Sacrra.Membership.Business.Services
{
    public class MailgunService
    {
        private readonly AppDbContext _dbContext;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly EmailService _emailService;

        public MailgunService(AppDbContext dbContext, IConfiguration configuration, IMapper mapper,
            EmailService emailService)
        {
            _dbContext = dbContext;
            _configuration = configuration;
            _mapper = mapper;
            _emailService = emailService;
        }

        public MailEventDTO GetEvents(string eventType, string recipient = null, DateTime? begin = null, DateTime? end = null)
        {
            if (!string.IsNullOrEmpty(eventType))
            {
                var options = new RestClientOptions(_configuration["MailgunAPI:BaseURL"])
                { 
                    Authenticator = new HttpBasicAuthenticator("api", _configuration["MailgunAPI:APIKey"])
                };
                var client = new RestClient(options);
                var request = new RestRequest()
                {
                    Resource = $"{_configuration["MailgunAPI:Domain"]}/events?event={eventType}"
                };

                var sortOrder = (_configuration["MailgunAPI:EventPollingSortOrder"] == "ascending") ? "yes" : "no";

                request.AddParameter("ascending", sortOrder);
                request.AddParameter("limit", _configuration["MailgunAPI:EventPollingLimit"]);
                request.AddParameter("pretty", "yes");

                if (!string.IsNullOrEmpty(recipient))
                {
                    request.AddParameter("recipient", recipient);
                }
                if (begin != null)
                {
                    request.AddParameter("begin", string.Format("{0:yyyy-MM-dd}", begin));
                }
                if (end != null)
                {
                    request.AddParameter("end", string.Format("{0:yyyy-MM-dd}", end));
                }

                var response = client.Execute(request);
                var data = JsonConvert.DeserializeObject<MailEventDTO>(response.Content);
                return data;
            }
            return null;
        }

        public void CreateMailgunEvent()
        {
            var events = GetEvents("failed");

            if (CanCreateMailgunEvent(events))
            {
                var settings = _dbContext.MailSettings
                    .Include(i => i.MailRecipients)
                    .Result.Where(i => i.Name == "Failed Events")
                    .Result.FirstOrDefault();

                if (!string.IsNullOrWhiteSpace(settings.FilterEmailsFromSender))
                {
                    events.Items = events.Items
                        .Result.Where(i => i.Envelope.Sender == settings.FilterEmailsFromSender)
                        .ToList();

                    if(events.Items != null && events.Items.Result.Count > 0)
                    {
                        var mailEvent = _mapper.Map<MailEvent>(events);
                        _dbContext.MailEvents.Add(mailEvent);
                        _dbContext.SaveChanges();

                        if (settings.IsEmailEnabled)
                        {
                            EmailMailgunEvents(events);
                        }
                    }
                }
                
            }

        }

        public void CreateMailgunEventsCronJob()
        {
            string jobTime = _configuration["MailgunAPI:EventPollingTime"];
            if (!string.IsNullOrEmpty(jobTime))
            {
                string[] hoursMinutes = jobTime.Split(':');
                int hour = (hoursMinutes.Length > 0) ? Convert.ToInt32(hoursMinutes.Result[0]) : 0;
                int minutes = (hoursMinutes.Length > 1) ? Convert.ToInt32(hoursMinutes.Result[1]) : 0;

                RecurringJob.AddOrUpdate(() => CreateMailgunEvent(), Cron.Daily(hour, minutes));
            }
        }

        public void EmailMailgunEvents(MailEventDTO events)
        {
            var settings = _dbContext.MailSettings
                .Include(i => i.MailRecipients)
                .Result.Where(i => i.Name == "Failed Events")
                .Result.FirstOrDefault();

            if (CanEmailMailgunEvents(settings, events))
            {

                var emailColumns = _dbContext.MailColumns
                    .Result.Where(i => i.MailSettingId == settings.Id)
                    .ToList();

                var emailBody = PrepareEmailBody(events, settings, emailColumns);

                if (!string.IsNullOrWhiteSpace(emailBody))
                {
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[MessageContent]", emailBody)
                    };

                    foreach (var recipient in settings.MailRecipients)
                    {
                        _emailService.SendEmail(recipient.Email, recipient.FirstName, "SACRRA Connect - Mailgun Failed Events", "MailgunFailedEvents.html", placeholders);
                    }
                }
            }
        }

        private string PrepareEmailBody(MailEventDTO mailEvent, MailSetting setting, List<MailColumn> mailColumns = null)
        {
            if (mailEvent != null)
            {
                if (!string.IsNullOrWhiteSpace(setting.FilterEmailsFromSender))
                {
                    mailEvent.Items = mailEvent.Items
                        .Result.Where(i => i.Envelope.Sender == setting.FilterEmailsFromSender)
                        .ToList();
                }
                
                var emailItems = (mailEvent.Items != null)? _mapper.Map<List<MailgunEmailContentDTO>>(mailEvent.Items) : null;

                if(emailItems != null)
                {
                    if(emailItems.Result.Count > 0)
                    {
                        string tableStyle = "border: 1px solid black;border-collapse: collapse;width:100%";

                        string mailBody = $"<table style='{tableStyle}'>" +
                            "<thead><tr>";

                        PropertyDescriptorCollection props =
                        TypeDescriptor.GetProperties(typeof(MailgunEmailContentDTO));

                        //Add column headings

                        if (mailColumns != null)
                        {
                            if (mailColumns.Result.Count > 0)
                            {
                                foreach (var column in mailColumns)
                                {
                                    bool isValidColumn = props.Find(column.PropertyName, false) != null;
                                    if (isValidColumn)
                                    {
                                        mailBody += $"<td style='{tableStyle}'>{column.FriendlyName}</td>";
                                    }
                                }
                            }
                        }
                        else
                        {
                            foreach (PropertyDescriptor prop in props)
                            {
                                mailBody += $"<td style='{tableStyle}'>{prop.Name}</td>";
                            }
                        }
                        mailBody += "<td style='{tableStyle}'>Bounce Reason</td>";

                        mailBody += "</tr></thead>";
                        mailBody += "<tbody>";

                        //Add column values
                        foreach (var item in emailItems)
                        {
                            mailBody += "<tr>";
                            if (item.EnvelopTargets != null)
                            {
                                PopulateMemberName(item);
                            }
                            if (mailColumns != null)
                            {
                                if (mailColumns.Result.Count > 0)
                                {
                                    foreach (var column in mailColumns)
                                    {
                                        var prop = props.Find(column.PropertyName, false);

                                        if (prop != null)
                                        {
                                            if (prop.PropertyType == typeof(List<MailAttachmentDTO>))
                                            {
                                                AddMailAttachmentData(prop, mailBody, tableStyle, item);
                                            }
                                            else
                                            {
                                                var rowValue = prop.GetValue(item);
                                                mailBody += $"<td style='{tableStyle}'>{rowValue}</td>";
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                foreach (PropertyDescriptor prop in props)
                                {
                                    if (prop.PropertyType == typeof(List<MailAttachmentDTO>))
                                    {
                                        AddMailAttachmentData(prop, mailBody, tableStyle, item);
                                    }
                                    else
                                    {
                                        var rowValue = prop.GetValue(item);
                                        mailBody += $"<td style='{tableStyle}'>{rowValue}</td>";
                                    }
                                }
                            }
                            string bounceReason = GetBounceReason(item.HeaderTo);
                            mailBody += $"<td style='{tableStyle}'>{bounceReason}</td>";

                            mailBody += "</tr>";
                        }

                        mailBody += "</tbody>";
                        return mailBody;
                    }
                }
            }

            return null;
        }

        private void AddMailAttachmentData(PropertyDescriptor prop, string mailBody, string tableStyle, MailgunEmailContentDTO item)
        {
            var rowValue = prop.GetValue(item);
            if (rowValue != null)
            {
                mailBody += $"<td style='{tableStyle}'>";
                List<MailAttachmentDTO> attachments = (List<MailAttachmentDTO>)rowValue;
                foreach (var file in attachments)
                {
                    PropertyDescriptorCollection attachmentProperties =
                        TypeDescriptor.GetProperties(typeof(MailAttachmentDTO));

                    foreach (PropertyDescriptor fileProp in attachmentProperties)
                    {
                        var attachmentValue = fileProp.GetValue(file);
                        mailBody += $"{fileProp.Name}: {attachmentValue},";
                    }

                    mailBody += "</br>";
                }
                mailBody += "</td>";
            }
        }

        private void PopulateMemberName(MailgunEmailContentDTO emailContentDTO)
        {
            if(emailContentDTO != null)
            {
                if(emailContentDTO.EnvelopTargets != null)
                {
                    var memberContact = _dbContext.MemberContacts
                        .Include(i => i.Member)
                        .Result.Select(m => new MemberContact
                        {
                            Email = m.Email,
                            Member = new Member
                            {
                                RegisteredName = m.Member.RegisteredName,
                                RegisteredNumber = m.Member.RegisteredNumber
                            }
                        })
                        .Result.FirstOrDefault(i => i.Email == emailContentDTO.EnvelopTargets);

                    if(memberContact != null)
                    {
                        emailContentDTO.Member = memberContact.Member.RegisteredName;

                        if(!string.IsNullOrWhiteSpace(memberContact.Member.RegisteredNumber))
                        {
                            emailContentDTO.Member += $" ( {memberContact.Member.RegisteredNumber} )";
                        }
                    }
                }
            }
        }

        private bool CanCreateMailgunEvent(MailEventDTO events)
        {
            return (
                events != null 
                && events.Items != null 
                && events.Items.Result.Count > 0
                );
        }

        private bool CanEmailMailgunEvents(MailSetting settings, MailEventDTO events)
        {
            return (
                settings != null 
                && settings.IsEmailEnabled 
                && events != null 
                && events.Items != null 
                && events.Items.Result.Count > 0
                );
            
        }

        private string GetBounceReason(string recipient){
            var options = new RestClientOptions(_configuration["MailgunAPI:BaseURL"])
            { 
                Authenticator = new HttpBasicAuthenticator("api", _configuration["MailgunAPI:APIKey"])
            };
            var client = new RestClient(options);
            var request = new RestRequest
            {
                Resource = $"{_configuration["MailgunAPI:Domain"]}/bounces/{recipient}"
            };
            var response = client.Execute(request);
            var data = JObject.Parse(response.Content)["error"].ToString();
            return data;
        }
    }
}
