using AutoMapper;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Resources.DocumentCategory;

namespace Sacrra.Membership.Business.Services
{
    public class DocumentCategoryService
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public DocumentCategoryService(AppDbContext dbContext, IMapper mapper)
        {
            _mapper = mapper;
            _dbContext = dbContext;
        }

        public List<DocumentCategoryOutputDTO> NewDocumentCategory(DocumentCategoryInputDTO categoryInputDTO)
        {
            _dbContext.DocumentCategories.Add(_mapper.Map<DocumentCategory>(categoryInputDTO));
            _dbContext.SaveChanges();

            return _mapper.Map<IEnumerable<DocumentCategoryOutputDTO>>(_dbContext.DocumentCategories.ToList()).ToList();
        }

        public List<DocumentCategoryOutputDTO> ListDocumentCategories()
        {
            return _mapper.Map<IEnumerable<DocumentCategoryOutputDTO>>(_dbContext.DocumentCategories.ToList()).ToList();
        }

        public List<DocumentCategoryOutputDTO> Update(int id, DocumentCategoryInputDTO inputDTO)
        {
            var category = _dbContext.Set<DocumentCategory>()
                .Result.FirstOrDefault(i => i.Id == id);

            var data = _mapper.Map(inputDTO, category);

            _dbContext.Update(data);
            _dbContext.SaveChanges();

            return _mapper.Map<IEnumerable<DocumentCategoryOutputDTO>>(_dbContext.DocumentCategories.ToList()).ToList();
        }

        public List<DocumentCategoryOutputDTO> Delete(int id)
        {
            var category = _dbContext.Set<DocumentCategory>()
                .Result.FirstOrDefault(i => i.Id == id);

            if(category != null)
            {
                _dbContext.Remove(category);
                _dbContext.SaveChanges();
            }
            return _mapper.Map<IEnumerable<DocumentCategoryOutputDTO>>(_dbContext.DocumentCategories.ToList()).ToList();
        }
    }
}
