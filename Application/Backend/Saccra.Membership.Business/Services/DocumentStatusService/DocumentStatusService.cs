using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;

namespace Sacrra.Membership.Business.Services
{
    public class DocumentStatusService
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public DocumentStatusService(AppDbContext dbContext, IMapper mapper)
        {
            _dbContext = dbContext;
            _mapper = mapper;
        }
        public IEnumerable<IdValuePairResource> GetList()
        {
            IEnumerable<DocumentStatus> statuses = _dbContext.DocumentStatuses
                .Result.Select(m => new DocumentStatus
                {
                    Id = m.Id,
                    Name = m.Name
                })
                .AsEnumerable();

            var data = _mapper.Map<IEnumerable<IdValuePairResource>>(statuses);

            return data;
        }
        public DocumentStatus GetByName(string name)
        {
            var status = _dbContext.DocumentStatuses
                        .Result.Where(i => i.Name == name)
                        .Result.Select(m => new DocumentStatus
                        {
                            Id = m.Id
                        })
                        .Result.FirstOrDefault();

            return status;
        }
    }
}
