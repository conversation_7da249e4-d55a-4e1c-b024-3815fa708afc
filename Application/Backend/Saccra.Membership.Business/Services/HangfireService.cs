using Hangfire;
using Microsoft.Extensions.Configuration;
using Sacrra.Membership.Database;
using Serilog;
using System;
using System.Linq;

namespace Sacrra.Membership.Business.Services
{
    public class HangfireService
    {
        private readonly AppDbContext _dbContext;
        private readonly IConfiguration _configuration;
        private readonly MembersService.MembersService _membersService;

        public HangfireService(AppDbContext dbContext, IConfiguration configuration, MembersService.MembersService membersService)
        {
            _configuration = configuration;
            _dbContext = dbContext;
            _membersService = membersService;
        }
      
        public void CleanupApiErrors(string retentionDays)
        {
            int retention = (!string.IsNullOrWhiteSpace(retentionDays)) ? Convert.ToInt32(retentionDays) : 5;

            var retentionDate = DateTime.Now.AddDays(-retention).Date;

            var errors = _dbContext.ApiErrors
                .Result.Where(i => i.Date.Date < retentionDate);

            foreach(var error in errors)
            {
                _dbContext.Remove(error);
            }
            _dbContext.SaveChanges();
        }
        public void CleanupCamundaErrors(string retentionDays)
        {
            int retention = (!string.IsNullOrWhiteSpace(retentionDays)) ? Convert.ToInt32(retentionDays) : 5;

            var retentionDate = DateTime.Now.AddDays(-retention).Date;

            var errors = _dbContext.CamundaErrors
                .Result.Where(i => i.Date.Date < retentionDate);

            foreach (var error in errors)
            {
                _dbContext.Remove(error);
            }
            _dbContext.SaveChanges();
        }
    }
}
