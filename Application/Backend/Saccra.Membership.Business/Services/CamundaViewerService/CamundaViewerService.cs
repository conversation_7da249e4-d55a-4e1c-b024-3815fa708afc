using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Npgsql;
using Renci.SshNet;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database;
using Sacrra.Membership.Notification.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.CamundaViewerDTOs;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace Sacrra.Membership.Business.Services.CamundaViewerService
{
    public class CamundaViewerService
    {
        private readonly ConfigSettings _configSettings;
        private readonly AppDbContext _appDbContext;
        private NpgsqlConnection connection;

        public CamundaViewerService( IOptions<ConfigSettings> configSettings, AppDbContext appDbContext)
        {
            _configSettings = configSettings.Value;
            _appDbContext = appDbContext;
        }

        //Get process definition
        public  IEnumerable<ProcessDefinitionOutputDTO> GetProcessDefinition()
        {
            using (connection = new NpgsqlConnection(_configSettings.CamundaConnectionString))
            {
                connection.Open();
                string commandText = "SELECT DISTINCT key_ AS Key,name_ AS Name FROM act_re_procdef";
                var ProcessDefinitionList = connection.Query<ProcessDefinitionOutputDTO>(commandText);

                connection.Close();

                return ProcessDefinitionList;
            }

        }

        //Get Tasks
        public  IEnumerable<dynamic> GetTasksList(string procDefinitionKey)
        {
            using (connection = new NpgsqlConnection(_configSettings.CamundaConnectionString))
            {
                connection.Open();
                procDefinitionKey = procDefinitionKey.Replace(' ', '-');
                string variableCommandText = @"SELECT DISTINCT v.name_
                        FROM act_ru_variable v
                        INNER JOIN act_re_procdef d ON v.proc_def_id_ = d.id_
                        WHERE d.key_ = '{process_definition_key}' "; 

                variableCommandText = variableCommandText.Replace("{process_definition_key}", procDefinitionKey);
                var variableList = connection.Query<string>(variableCommandText);

                var variableCoulmnString = "";
                var variableAggregationString = ""; 
                foreach (var variable in variableList)
                {
                    variableCoulmnString = variableCoulmnString + ",\"Variable_"+ variable +"\"";
                    variableAggregationString = variableAggregationString + ",MAX(CASE WHEN name_ = '" + variable + "' THEN COALESCE(double_::character varying, long_::character varying, text_) END) AS \"Variable_"+ variable +"\"";
                }

                string commandText =
                         @"SELECT
	                     ai.proc_inst_id_ AS ""ProcessInstanceID""
	                    ,d.version_ AS ""WorkFlowVersion""
                        ,TO_CHAR(pi.start_time_, 'YYYY-MM-DD"" ""HH24:MI:SS') AS ""ProcessStartTime""
	                    ,TO_CHAR(ai.start_time_, 'YYYY-MM-DD"" ""HH24:MI:SS') AS ""LastTaskStartTime""
	                    ,TO_CHAR(ai.end_time_, 'YYYY-MM-DD"" ""HH24:MI:SS') AS ""LastTaskEndTime""
	                    ,COALESCE(etl.worker_id_, ai.assignee_) AS ""AssigneeID""
	                    ,u.email_ AS ""AssigneeEmail""
	                    ,ai.act_id_ AS ""ActionKey""
	                    ,ai.act_type_ AS ""ActionType""
	                    ,COALESCE(etl.topic_name_, ai.act_name_) AS ""ActionName""
	                    ,pi.state_ AS ""ProcessState""
                        {variable_column_string}
                    FROM
                    (
	                    SELECT
		                    ROW_NUMBER() OVER (PARTITION BY proc_inst_id_ ORDER BY COALESCE(end_time_, '9999-12-01') DESC) AS ""RowNumber"",
		                    start_time_,
		                    end_time_,
		                    act_id_,
		                    act_type_,
		                    act_name_,
		                    proc_inst_id_,
		                    assignee_,
		                    task_id_,
		                    execution_id_
	                    FROM act_hi_actinst
                    ) as ai
                    LEFT JOIN (
                        SELECT
                            proc_inst_id_
                            {variable_aggregation_string}
                        FROM act_hi_varinst
                        GROUP BY proc_inst_id_
                    ) AS v ON v.proc_inst_id_ = ai.proc_inst_id_
                    INNER JOIN act_hi_procinst pi ON ai.proc_inst_id_ = pi.proc_inst_id_ AND ai.""RowNumber"" = 1
                    INNER JOIN act_re_procdef d ON pi.proc_def_id_ = d.id_
                    LEFT OUTER JOIN act_id_user AS u ON ai.assignee_ = u.id_
                    LEFT OUTER JOIN act_hi_taskinst AS ti ON ai.task_id_ = ti.id_
                    LEFT OUTER JOIN act_hi_ext_task_log AS etl ON
	                    ai.proc_inst_id_ = etl.proc_inst_id_
	                    AND ai.execution_id_ = etl.execution_id_
	                    AND etl.worker_id_ IS NOT NULL
                    WHERE d.key_ = '{process_definition_key}'
                    ORDER BY COALESCE(pi.start_time_, '9999-12-01') DESC;";

                commandText = commandText.Replace("{process_definition_key}", procDefinitionKey);
                commandText = commandText.Replace("{variable_column_string}", variableCoulmnString);
                commandText = commandText.Replace("{variable_aggregation_string}", variableAggregationString);

                var TaskList = connection.Query<dynamic>(commandText);
                connection.Close();

                foreach(var task in TaskList)
                {
                    int srnId = Convert.ToInt32(task.Variable_SRNId);
                    if ( srnId != 0)
                    {
                        task.SrnNumber = _appDbContext.SRNs.Result.Where(x => x.Id == srnId).Result.Select(x => x.SRNNumber).Result.FirstOrDefault();
                    }
                }

                return TaskList;
            }

        }

        //Get Task History
        public IEnumerable<dynamic> GetTaskHistory(string procInstanceId)
        {
            using (connection = new NpgsqlConnection(_configSettings.CamundaConnectionString))
            {
                connection.Open();
                var variableHistoryCoommandText = @"SELECT
                         name_ AS ""Name""
                        ,COALESCE(double_::character varying, long_::character varying, text_) AS ""Value""
                    FROM act_hi_varinst
                    WHERE proc_inst_id_ = '{process_instance_id}'";
                variableHistoryCoommandText = variableHistoryCoommandText.Replace("{process_instance_id}", procInstanceId);
                var variableHistoryList = connection.Query<string>(variableHistoryCoommandText);

                string commandText = "SELECT" +
                    " TO_CHAR(ai.start_time_, 'YYYY-MM-DD HH24:MI:SS') AS StartTime" +
                    ",TO_CHAR(ai.end_time_, 'YYYY-MM-DD HH24:MI:SS') AS EndTime" +
                    ",COALESCE(etl.worker_id_, ai.assignee_) AS AssigneeID" +
                    ",u.email_ AS AssigneeEmail" +
                    ",ai.act_id_ AS ActionKey" +
                    ",ai.act_type_ AS ActionType" +
                    ",COALESCE(etl.topic_name_, ai.act_name_) AS ActionName " +
                    "FROM act_hi_actinst ai " +
                    "LEFT OUTER JOIN act_id_user AS u ON ai.assignee_ = u.id_ " +
                    "LEFT OUTER JOIN act_hi_taskinst AS ti ON ai.task_id_ = ti.id_ " +
                    "LEFT OUTER JOIN act_hi_ext_task_log AS etl ON " +
                    "ai.proc_inst_id_ = etl.proc_inst_id_ " +
                    "AND ai.execution_id_ = etl.execution_id_ " +
                    "AND etl.worker_id_ IS NOT NULL " +
                    "WHERE ai.proc_inst_id_ = '{process_definition_key}' " +
                    "ORDER BY COALESCE(ai.end_time_, '9999-12-01') DESC";
                commandText = commandText.Replace("{process_definition_key}", procInstanceId);
                var TaskHistoryList = connection.Query<dynamic>(commandText);

                connection.Close();

                return TaskHistoryList;
            }
        }

        //Get variable history
        /*public void GetVariableHistory(int procInstanceId)
        {
            bool boolfound = false;
            using (NpgsqlConnection conn = new NpgsqlConnection("Server=<ip>; Port=5432; User Id=ewx@sacrra-automation-uat-postgres-server; Password=********************; Database=Sacrra.Camunda"))
            {
                conn.Open();

                NpgsqlCommand cmd = new NpgsqlCommand("SELECT" +
                    "name_ AS Name" +
                    ",COALESCE(double_::character varying" +
                    ", long_::character varying, text_) AS Value" +
                    "FROM act_hi_varinst" +
                    "WHERE proc_inst_id_ = %(process_instance_id)s", conn);
                NpgsqlDataReader dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    boolfound = true;
                    Console.WriteLine("connection established");
                }
                if (boolfound == false)
                {
                    Console.WriteLine("Data does not exist");
                }
                dr.Close();
            }
        }*/

        //Get variable list
        /*public void GetVariableList(int procInstanceId)
        {
            bool boolfound = false;
            {
                conn.Open();

                NpgsqlCommand cmd = new NpgsqlCommand("SELECT DISTINCT v.name_" +
                    "FROM act_ru_variable v" +
                    "INNER JOIN act_re_procdef d ON v.proc_def_id_ = d.id_" +
                    "WHERE d.key_ = %(process_definition_key)s", conn);
                NpgsqlDataReader dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    boolfound = true;
                    Console.WriteLine("connection established");
                }
                if (boolfound == false)
                {
                    Console.WriteLine("Data does not exist");
                }
                dr.Close();
            }
        }*/

    }
}
