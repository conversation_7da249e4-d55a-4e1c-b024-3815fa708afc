using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
public class AsyncLogQueueService
{
    private readonly ConcurrentQueue<LogEntry> _logQueue = new ConcurrentQueue<LogEntry>();
    private readonly SemaphoreSlim _signal = new SemaphoreSlim(0);
    private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
    private readonly AppDbContext _dbContext;
    public AsyncLogQueueService(AppDbContext dbContext)
    {
        ProcessLogQueue();
        _dbContext = dbContext;
    }

    public void EnqueueLog(LogEntry logEntry)
    {
        _logQueue.Enqueue(logEntry);
        _signal.Release();
    }

    private void ProcessLogQueue()
    {
        while (!_cancellationTokenSource.IsCancellationRequested)
        {
            _signal.Wait(_cancellationTokenSource.Token);

            while (_logQueue.TryDequeue(out LogEntry logEntry))
            {
                WriteLogToDatabase(logEntry);
            }
        }
    }

    private void WriteLogToDatabase(LogEntry logEntry)
    {
        _dbContext.HangfireJobLogs.Add(new HangfireLog {
                Id = 0,
                JobName = logEntry.JobName,
                Status = logEntry.Status,
                Details = logEntry.Details,
                Time = logEntry.Time
            });
            _dbContext.SaveChanges();
    }

    public void Stop()
    {
        _cancellationTokenSource.Cancel();
    }
}

public class LogEntry
{
    public string JobName { get; set; }
    public string Status { get; set; }
    public string Details { get; set; }
    public DateTime Time { get; set; }
}