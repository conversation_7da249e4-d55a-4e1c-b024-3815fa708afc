using AutoMapper;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Exceptions;

namespace Sacrra.Membership.Business.Services
{
    public class DocumentsService
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }
        private readonly IConfiguration _configuration;
        private readonly DocumentStatusService _documentStatus;
        private readonly GlobalHelper _globalHelper;

        public DocumentsService(AppDbContext dbContext, IMapper mapper, IConfiguration configuration, 
            DocumentStatusService documentStatus, GlobalHelper globalHelper)
        {
            _dbContext = dbContext;
            _mapper = mapper;
            _configuration = configuration;
            _documentStatus = documentStatus;
            _globalHelper = globalHelper;
        }

        public void Create(DocumentInputDTO inputDTO, User user)
        {
            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
                    var data = _mapper.Map<Document>(inputDTO);

                    //We set the BlobName to empty so the DB transaction does not fail
                    //as we don't have the value yet at this stage
                    data.BlobName = String.Empty;

                    //Set default verion to ONE
                    data.Version = 1;
                    data.LastUpdatedAt = DateTime.Now;
                    data.CreatedAt = DateTime.Now;
                    
                    _dbContext.Documents.Add(data);
                    _dbContext.SaveChanges();

                    SaveToBlobStorage(data, inputDTO);

                    //We update the Document (BlobName) model because we have the value now
                    _dbContext.Update(data);
                    _dbContext.SaveChanges();

                    CreateEventLog(data, inputDTO, false, user);

                    transaction.Commit();
                }
                catch(Exception ex)
                {
                    Log.Error(ex, "Unable to create a document");
                    throw new DocumentCreationException();
                }
            }
        }

        private Azure.Response<BlobContentInfo> SaveToBlobStorage(Document document, DocumentInputDTO inputDTO)
        {
            var connectionString = _configuration.GetSection("Documents:ConnectionString").Value;
            var container = _configuration.GetSection("Documents:StorageContainerName").Value;

            BlobContainerClient containerClient = new(connectionString, container);

            string fileNameSufix = Guid.NewGuid().ToString().Substring(0, 8).ToLower();
            string fileName = $"{inputDTO.Name.ToLower()}-{document.Id}-{fileNameSufix}";
            string fileExtesion = (inputDTO.File.FileName.Split(".").Length > 1)? inputDTO.File.FileName.Split(".")[1] : null;
            string blobName = $"{fileName}.{fileExtesion}";

            if(string.IsNullOrWhiteSpace(document.BlobName))
                document.BlobName = blobName;

            BlobClient blobClient = containerClient.GetBlobClient(document.BlobName);

            BlobUploadOptions options = new();
            options.Metadata = new Dictionary<string, string>
            {
                { "DocumentId", document.Id.ToString() },
                { "Type", inputDTO.File.FileType },
                { "Name", inputDTO.File.FileName }
            };

            BlobHttpHeaders blobHttpHeaders = new();
            blobHttpHeaders.ContentType = inputDTO.File.FileType;

            options.HttpHeaders = blobHttpHeaders;

            var memoryStream = CreateMemoryStreamFromBase64(inputDTO.File.Value);

            var response = blobClient.Upload(memoryStream, options);

            return response;
        }
        private MemoryStream CreateMemoryStreamFromBase64(string base64String)
        {
            var outputStream = new MemoryStream(Convert.FromBase64String(base64String));
            return outputStream;
        }

        public IEnumerable<DocumentOutputDTO> GetList(User user)
        {

            var documents = from doc in _dbContext.Documents
                            where !doc.IsDeleted
                            join docCategory in _dbContext.DocumentCategories on doc.CategoryId equals docCategory.Id into categoryJoin
                            from docCategory in categoryJoin.DefaultIfEmpty()
                            join docStatus in _dbContext.DocumentStatuses on doc.StatusId equals docStatus.Id into statusJoin
                            from docStatus in statusJoin.DefaultIfEmpty()
                            join docUserAccess in _dbContext.DocumentUserAccess
                                on new { DocumentId = doc.Id, Version = doc.Version, UserId = user.Id }
                                equals new { DocumentId = docUserAccess.DocumentId, Version = docUserAccess.Version, UserId = docUserAccess.UserId }
                                into userAccessJoin
                            from docUserAccess in userAccessJoin.DefaultIfEmpty()

                            select new DocumentOutputDTO
                            { 
                                Id = doc.Id,
                                Name = doc.Name,
                                Description = doc.Description,
                                Category = new()
                                {
                                    Id = docCategory.Id,
                                    Value = docCategory.Name
                                },
                                Status = new()
                                {
                                    Id = docStatus.Id,
                                    Value = docStatus.Name
                                },
                                LastUpdatedAt = string.Format("{0:yyyy-MM-dd}", doc.LastUpdatedAt),
                                IsRead = (docUserAccess.UserId == user.Id)? true : false
                            };

            //If user is not SACRRA Admin, lets filter for active documents only
            if (user.RoleId != UserRoles.SACRRAAdministrator)
            {
                var status = _documentStatus.GetByName("Active");
                documents = documents.Result.Where(i => i.Status.Id == status.Id);
            }

            return documents.AsEnumerable();
        }

        public void Update(int id, DocumentInputDTO inputDTO, User user)
        {
            var document = _dbContext.Set<Document>()
                .Result.FirstOrDefault(i => i.Id == id);

            if(document != null)
            {
                using (var transaction = _dbContext.Database.BeginTransaction())
                {
                    try
                    {
                        CreateEventLog(document, inputDTO, true, user);

                        var data = _mapper.Map(inputDTO, document);
                        data.LastUpdatedAt = DateTime.Now;
                        data.Version += 1;

                        _dbContext.SaveChanges();

                        SaveToBlobStorage(data, inputDTO);

                        //We update the Document (BlobName) model because we have the value now
                        _dbContext.Update(data);
                        _dbContext.SaveChanges();

                        transaction.Commit();
                    }
                    catch
                    {
                        throw new DocumentUpdateException();
                    }
                }
            }
        }
        public DocumentOutputSingleDTO Get(int id, User user)
        {
            Document document = _dbContext.Documents
                .Result.Where(i => !i.IsDeleted)
                .Result.Select(m => new Document
                {
                    Id = m.Id,
                    Name = m.Name,
                    Description = m.Description,
                    CategoryId = m.CategoryId,
                    StatusId = m.StatusId,
                    LastUpdatedAt = m.LastUpdatedAt,
                    BlobName = m.BlobName,
                    Version = m.Version
                })
                .Result.FirstOrDefault(i => i.Id == id);

            if(document == null)
            {
                return null;
            }

            var data = _mapper.Map<DocumentOutputSingleDTO>(document);

            if (!string.IsNullOrWhiteSpace(document.BlobName))
                data.File = GetBlobFile(document.BlobName);

            CreateDocumentAccess(document, user);

            return data;
        }

        private DocumentFile GetBlobFile(string blobName)
        {
            var connectionString = _configuration.GetSection("Documents:ConnectionString").Value;
            var container = _configuration.GetSection("Documents:StorageContainerName").Value;

            BlobContainerClient containerClient = new(connectionString, container);

            BlobClient blobClient = containerClient.GetBlobClient(blobName);

            var response = blobClient.DownloadContent();
            var result = response.Value;

            string base64Strig = null;

            if (result.Content != null)
            {
                base64Strig = GetBase64FromBinaryData(result.Content);
            }
            
            return new DocumentFile
            {
                FileName = blobName,
                FileType = (response.Value.Details.Metadata.Result.Count > 0)? response.Value.Details.Metadata["Type"] : null,
                Value = base64Strig
            };
        }

        private string GetBase64FromBinaryData(BinaryData binaryData)
        {
            var base64String = Convert.ToBase64String(binaryData);
            return base64String;
        }

        public IEnumerable<DocumentOutputDTO> Delete(int id, User user)
        {
            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
                    Document document = _dbContext.Documents
                        .Result.FirstOrDefault(i => i.Id == id);

                    //We do a soft delete in the DB
                    document.IsDeleted = true;
                    document.LastUpdatedAt = DateTime.Now;

                    var status = _documentStatus.GetByName("Inactive");

                    if(status != null)
                    {
                        document.StatusId = status.Id;
                    }
                    
                    _dbContext.Update(document);
                    _dbContext.SaveChanges();

                    //We delete the actual file in Azure Blob Storage
                    if (!string.IsNullOrWhiteSpace(document.BlobName))
                    {
                        DeleteFromBlobStaorage(document.BlobName);
                    }

                    transaction.Commit();
                    return GetList(user);
                }
                catch
                {
                    throw new DocumentDeleteException();
                }
            }
        }

        private Azure.Response DeleteFromBlobStaorage(string blobName)
        {
            var connectionString = _configuration.GetSection("Documents:ConnectionString").Value;
            var container = _configuration.GetSection("Documents:StorageContainerName").Value;

            BlobContainerClient containerClient = new(connectionString, container);

            BlobClient blobClient = containerClient.GetBlobClient(blobName);

            var response = blobClient.Delete();
            
            return response;
        }

        private void CreateDocumentAccess(Document document, User user)
        {
            if(document != null)
            {
                if(document.Id > 0 && document.Version > 0)
                {

                    if (user != null)
                    {
                        var recordExists = _dbContext.DocumentUserAccess.Result.Any(i => i.DocumentId == document.Id && i.UserId == user.Id && i.Version == document.Version);

                        if (!recordExists)
                        {
                            var documentAccess = new DocumentUserAccess
                            {
                                DocumentId = document.Id,
                                UserId = user.Id,
                                AccessedAt = DateTime.Now,
                                Version = document.Version
                            };

                            _dbContext.DocumentUserAccess.Add(documentAccess);
                            _dbContext.SaveChanges();
                        }
                    }
                }
            }
        }

        public DocumentUserAccessOutputDTO GetDocumentUserAccess(User user)
        {

            if(user != null)
            {
                var totalDocuments = _dbContext.Documents
                    .Include(i => i.Status)
                    .Result.Where(i => !i.IsDeleted && i.Status.Name != "Inactive")
                    .Count();

                var totalAccessed = _dbContext.DocumentUserAccess
                    .Include(i => i.Document)
                        .ThenInclude(x => x.Status)
                    .Result.Where(i => i.UserId == user.Id 
                        && i.Document.Version == i.Version
                        && !i.Document.IsDeleted
                        && i.Document.Status.Name != "Inactive")
                    .Count();

                var totalNotAccessed = totalDocuments - totalAccessed;

                var documentAccess = new DocumentUserAccessOutputDTO
                {
                    TotalDocuments = totalDocuments,
                    TotalNotAccessed = (totalNotAccessed < 0)? 0 : totalNotAccessed,
                };

                return documentAccess;
            }

            return null;
        }
        
        private void CreateEventLog(Document document, DocumentInputDTO inputDTO, bool isUpdate, User user)
        {
            string changeType = (isUpdate) ? "Document Update" : "Document Create";
            var  entityId = document.Id;

            var stagingChangeLog = new MemberStagingChangeLogResource();

            if(document != null && inputDTO != null)
            {
                List<StagingChange> changes = new List<StagingChange>();

                if (!isUpdate)
                    document = new Document();

                if (document.Name != inputDTO.Name)
                {
                    StagingChange stagingChange = new()
                    {
                        Name = "Document Name",
                        OldValue = document.Name ?? "",
                        NewValue = inputDTO.Name
                    };

                    changes.Add(stagingChange);
                }
                if (document.Description != inputDTO.Description)
                {
                    StagingChange stagingChange = new()
                    {
                        Name = "Document Description",
                        OldValue = document.Description ?? "",
                        NewValue = inputDTO.Description
                    };

                    changes.Add(stagingChange);
                }

                if (document.CategoryId != inputDTO.CategoryId)
                {
                    var newCategory = _dbContext.DocumentCategories
                        .Result.FirstOrDefault(c => c.Id == inputDTO.CategoryId);

                    var oldCategory = _dbContext.DocumentCategories
                        .Result.FirstOrDefault(c => c.Id == document.CategoryId);

                    StagingChange stagingChange = new()
                    {
                        Name = "Document Category",
                        OldValue = (oldCategory != null) ? oldCategory.Name : "",
                        NewValue = (newCategory != null) ? newCategory.Name : ""
                    };

                    changes.Add(stagingChange);
                }

                if (document.StatusId != inputDTO.StatusId)
                {
                    var newStatus = _dbContext.DocumentStatuses
                        .Result.FirstOrDefault(c => c.Id == inputDTO.StatusId);

                    var oldStatus = _dbContext.DocumentStatuses
                        .Result.FirstOrDefault(c => c.Id == document.StatusId);

                    StagingChange stagingChange = new()
                    {
                        Name = "Document Status",
                        OldValue = (oldStatus != null) ? oldStatus.Name : "",
                        NewValue = (newStatus != null) ? newStatus.Name : ""
                    };

                    changes.Add(stagingChange);
                }

                stagingChangeLog.Changes.AddRange(changes);
            }

            var entityBlob = JsonConvert.SerializeObject(document);

            var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog);

            _globalHelper.CreateEventLog(_dbContext, user.Id, changeType, inputDTO.Name, entityBlob, stagingBlob, entityId, "Document");
        }

        public DateTime? GetTsAndCsLastUpdatedDate()
        {
            var ts_n_cs = _dbContext.TermsConditionsDocuments.Result.FirstOrDefault();

            if (ts_n_cs != null)
            {
                return ts_n_cs.UpdatedAt;
            }
            return null;
        }
    }
}
