using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.DataWarehouseDTO;
using Sacrra.Membership.Reporting.Helper;
using System;
using System.Collections.Generic;
using System.Net.Http;
using Sacrra.Membership.Business.Helpers;
using System.Text;
using System.Net;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Resources;

namespace Sacrra.Membership.Business.Services.DataWarehouseService
{
    public class DataWarehouseExceptionOutputDTO
    {
        public int StatusCode { get; set; }
        public string Message { get; set; }
    }

    public class DataWarehouseService
    {
        private string _baseApiUrl;
        private string API_KEY;
        private string _dataset;
        public ReportTables ReportTables { get; }

        public DataWarehouseService(IConfiguration configuration)
        {
            _baseApiUrl = configuration.GetSection("ReportingAPISettings")["BaseApiUrl"];
            API_KEY = configuration.GetSection("ReportingAPISettings")["ApiKey"];
            _dataset = configuration.GetSection("ReportingAPISettings")["Dataset"];
            ReportTables = new ReportTables();
        }

        private RestClient getRestClient()
        {
            RestClientOptions options = new($"{_baseApiUrl}/api");
            // disable SSL errors
            options.RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            var restClient = new RestClient(options);
            
            return restClient;
        }

        public T[] GetResultArray<T>(string tableName, DataWarehouseAPIModel apiCallModel)
        {
            apiCallModel.ApiKey = API_KEY;
            var httpClientHandler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) =>
                {
                    return true;
                }
            };
            using var client = new HttpClient(httpClientHandler);
            var webRequest = new HttpRequestMessage(HttpMethod.Post, $"{_baseApiUrl}/api/Report/{_dataset}/{tableName}")
            {
                Content = new StringContent(JsonConvert.SerializeObject(apiCallModel), Encoding.UTF8, "application/json"),
            };
            var result = client.Send(webRequest);
            var resultContent = result.Content.ReadAsStringAsync();

            if (result.StatusCode != HttpStatusCode.OK && result.StatusCode != HttpStatusCode.NoContent)
            {
                throw new DataWarehouseException(0, "Data Warehouse Error: " + JsonConvert.DeserializeObject<DataWarehouseExceptionOutputDTO>(resultContent).Message, null);
            }

            var restResults = JsonConvert.DeserializeObject<List<T>>(resultContent, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                FloatParseHandling = FloatParseHandling.Decimal
            });

            return restResults.ToArray();
        }

        private static void checkForError(RestResponse restRequest)
        {
            if (restRequest.StatusCode != System.Net.HttpStatusCode.OK)
            {
                // If the API throws an exception or we cannot access the API
                if (restRequest.ErrorException != null)
                    throw restRequest.ErrorException;

                // Otherwise return the error we get back from the API
                var message = restRequest.Content;
                var ex = new Exception(message);
                throw ex;
            }
        }

        public JArray GetResultJsonArray(string tableName, DataWarehouseAPIModel apiCallModel)
        {
            apiCallModel.ApiKey = API_KEY;
            var httpClientHandler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) =>
                {
                    return true;
                }
            };
            var webRequest = new HttpRequestMessage(HttpMethod.Post, $"{_baseApiUrl}/api/Report/{_dataset}/{tableName}")
            {
                Content = new StringContent(JsonConvert.SerializeObject(apiCallModel), Encoding.UTF8, "application/json"),
            };
            var result = client.Send(webRequest);
            var resultContent = result.Content.ReadAsStringAsync();

            if (result.StatusCode != HttpStatusCode.OK && result.StatusCode != HttpStatusCode.NoContent)
            {
                throw new DataWarehouseException(0, "Data Warehouse Error: " + JsonConvert.DeserializeObject<DataWarehouseExceptionOutputDTO>(resultContent).Message, null);
            }

            return JArray.Parse(resultContent);
        }

        public void UpdateTable(string updateSQL, DataWarehouseAPIModel apiCallModel)
        {
            apiCallModel.ApiKey = API_KEY;
            string requestUrlString = $"Exceptions/{_dataset}/{updateSQL}";

            var restClient = getRestClient();
            var request = new RestRequest(requestUrlString, Method.Get);
            request.AddJsonBody(apiCallModel);
            var restRequest = restclient.Execute(new RestRequest(request, Method.Post, Method.Get));
            checkForError(restRequest);
        }

        public T[] GetBenchmarkReportData<T>(BenchmarkReportAPICallModel apiCallModel)
        {
            apiCallModel.ApiKey = API_KEY;
            apiCallModel.StoredProcedureName = "API.spBenchmarkReport_DynamicSQL_BureauLevel";
            apiCallModel.DatasetName = _dataset;

            string requestUrlString = $"Report/GetIndustryBenchmarkReport";

            var restClient = getRestClient();
            var request = new RestRequest(requestUrlString, Method.Get);
            request.AddJsonBody(apiCallModel);
            var restRequest = restclient.Execute(new RestRequest(request, Method.Post, Method.Get));
            checkForError(restRequest);

            var restResults = JsonConvert.DeserializeObject<List<T>>(restRequest.Content);
            return restResults.ToArray();
        }
    }
}
