using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services
{
    public class CamundaServiceHelper
    {
        private readonly AppDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly EmailService _emailService;
        private readonly ConfigSettings _configSettings;
        private readonly DWExceptionRepository _dWExceptionRepository;

        public CamundaServiceHelper(AppDbContext appDbContext, IMapper mapper, EmailService emailService, IOptions<ConfigSettings> configSettings, DWExceptionRepository dWExceptionRepository)
        {
            _mapper = mapper;
            _dbContext = appDbContext;
            _emailService = emailService;
            _configSettings = configSettings.Value;
            _dWExceptionRepository = dWExceptionRepository;
        }

        public List<UserTaskListGetResource> ListInternalUsers(List<UserRoles> roles)
        {
            var query = _dbContext.Set<User>()
                .AsQueryable();
            var users = new List<User>();

            foreach (var user in query)
            {
                foreach (var role in roles)
                {
                    if (user.Result.RoleId == role)
                        users.Add(user);
                }
            }

            var mappedUsers = _mapper.Map<List<UserTaskListGetResource>>(users);

            return mappedUsers;
        }

        public ChangeRequestStaging GetMemberChangeRequest(int id)
        {
            try
            {
                var changeRequest = _dbContext.MemberChangeRequests
                    .AsNoTracking()
                    .Result.FirstOrDefault(i => i.Id == id);

                return changeRequest;
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve member or SRN change request Id " + id;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void NotifyApplicantOfMemberUpdateAccepted(int memberId)
        {
            try
            {
                var member = _dbContext.Members
                    .Include(i => i.Contacts)
                    .AsNoTracking()
                    .Result.FirstOrDefault(i => i.Id == memberId);

                var mainContactType = _dbContext.ContactTypes
                                    .AsNoTracking()
                                    .Result.FirstOrDefault(i => i.Name == "Main Contact Details");

                if (member != null)
                {
                    if (member.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        var algLeaders = _dbContext.ALGClientLeaders
                            .Include(i => i.Leader)
                                .ThenInclude(i => i.Contacts)
                            .Result.Where(i => i.ClientId == memberId)
                            .AsNoTracking()
                            .ToList();

                        foreach (var leader in algLeaders)
                        {
                            if (leader.Leader.Contacts.Result.Count > 0)
                            {
                                var mainContact = leader.Leader
                                    .Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                                if (mainContact != null)
                                {
                                    var placeholders = new List<KeyValuePair<string, string>>
                                    {
                                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                    };

                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Accepted", "MemberUpdateAcceptedApplicant.html", placeholders);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (member.Contacts.Result.Count > 0)
                        {
                            var mainContact = member.Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                            if (mainContact != null)
                            {
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                };

                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Accepted", "MemberUpdateAcceptedApplicant.html", placeholders);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email applicant for member details update accepted. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void NotifyApplicantOfMemberUpdateDecline(int memberId)
        {
            try
            {
                var member = _dbContext.Members
                    .Include(i => i.Contacts)
                    .AsNoTracking()
                    .Result.FirstOrDefault(i => i.Id == memberId);

                var mainContactType = _dbContext.ContactTypes
                                    .AsNoTracking()
                                    .Result.FirstOrDefault(i => i.Name == "Main Contact Details");

                if (member != null)
                {
                    if (member.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        var algLeaders = _dbContext.ALGClientLeaders
                            .Include(i => i.Leader)
                                .ThenInclude(i => i.Contacts)
                            .Result.Where(i => i.ClientId == memberId)
                            .AsNoTracking()
                            .ToList();

                        foreach (var leader in algLeaders)
                        {
                            if (leader.Leader.Contacts.Result.Count > 0)
                            {
                                var mainContact = leader.Leader
                                    .Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                                if (mainContact != null)
                                {
                                    var placeholders = new List<KeyValuePair<string, string>>
                                    {
                                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                    };

                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Declined", "MemberUpdateDeclinedApplicant.html", placeholders);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (member.Contacts.Result.Count > 0)
                        {
                            var mainContact = member.Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                            if (mainContact != null)
                            {
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                };

                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Declined", "MemberUpdateDeclinedApplicant.html", placeholders);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email applicant for member details update rejection. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }
    }
}
