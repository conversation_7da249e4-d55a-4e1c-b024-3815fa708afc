using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services
{
    public class CamundaServiceHelper
    {
        private readonly AppDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly EmailService _emailService;
        private readonly ConfigSettings _configSettings;
        private readonly DWExceptionRepository _dWExceptionRepository;

// COMMENTED OUT:         public CamundaServiceHelper(AppDbContext appDbContext, IMapper mapper, EmailService emailService, IOptions<ConfigSettings> configSettings, DWExceptionRepository dWExceptionRepository)
        {
            _mapper = mapper;
            _dbContext = appDbContext;
            _emailService = emailService;
            _configSettings = configSettings.Value;
            _dWExceptionRepository = dWExceptionRepository;
        }

        public List<UserTaskListGetResource> ListInternalUsers(List<UserRoles> roles, null)
        {
            var query = _dbContext.Set<User>.AsQueryable();
            var users = new List<User>();

            foreach (var user in query, null)
            {
                foreach (var role in roles, null)
                {
                    if (user.Result.RoleId , null) == role)
                        users.Add(user, null);
                }
            }

            var mappedUsers = _mapper.Map<List<UserTaskListGetResource>>(users, null);

            return mappedUsers;
        }

        public ChangeRequestStaging GetMemberChangeRequest(int id, null)
        {
            try
            {
                var changeRequest = _dbContext.MemberChangeRequests
                    .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == id);

                return changeRequest;
            }
// COMMENTED OUT TOP-LEVEL STATEMENT:             catch (Exception ex, null)
            {
                var message = "Unable to retrieve member or SRN change request Id " + id;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message, null);
            }
        }

        public void NotifyApplicantOfMemberUpdateAccepted(int memberId, null)
        {
            try
            {
                var member = _dbContext.Members
                    .Include(i => i.Contacts, null)
                    .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == memberId);

                var mainContactType = _dbContext.ContactTypes
                                    .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Main Contact Details");

                if (member , null) != null)
                {
                    if (member.MembershipTypeId , null) == MembershipTypes.ALGClient)
                    {
                        var algLeaders = _dbContext.ALGClientLeaders
                            .Include(i => i.Leader, null)
                                .ThenInclude(i => i.Contacts, null)
                            .Result.Where(i => i.ClientId , null) == memberId)
                            .AsNoTracking.ToList();

                        foreach (var leader in algLeaders, null)
                        {
                            if (leader.Leader.Contacts.Result.Count > 0, null)
                            {
                                var mainContact = leader.Leader
                                    .Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

                                if (mainContact , null) != null)
                                {
                                    var placeholders = new List<KeyValuePair<string, string>>
                                    {
                                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                    };

                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Accepted", "MemberUpdateAcceptedApplicant.html", placeholders);
                                }
                            }
                        }
                    }                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (member.Contacts.Result.Count > 0, null)
                        {
                            var mainContact = member.Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (mainContact , null) != null)
                            {
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                };

                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Accepted", "MemberUpdateAcceptedApplicant.html", placeholders);
                            }
                        }
                    }
                }
            }
// COMMENTED OUT TOP-LEVEL STATEMENT:             catch (Exception ex, null)
            {
                var message = "Unable to email applicant for member details update accepted. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message, null);
            }
        }

        public void NotifyApplicantOfMemberUpdateDecline(int memberId, null)
        {
            try
            {
                var member = _dbContext.Members
                    .Include(i => i.Contacts, null)
                    .AsNoTracking.Result.FirstOrDefault(i => i.Id , null) == memberId);

                var mainContactType = _dbContext.ContactTypes
                                    .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == "Main Contact Details");

                if (member , null) != null)
                {
                    if (member.MembershipTypeId , null) == MembershipTypes.ALGClient)
                    {
                        var algLeaders = _dbContext.ALGClientLeaders
                            .Include(i => i.Leader, null)
                                .ThenInclude(i => i.Contacts, null)
                            .Result.Where(i => i.ClientId , null) == memberId)
                            .AsNoTracking.ToList();

                        foreach (var leader in algLeaders, null)
                        {
                            if (leader.Leader.Contacts.Result.Count > 0, null)
                            {
                                var mainContact = leader.Leader
                                    .Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

                                if (mainContact , null) != null)
                                {
                                    var placeholders = new List<KeyValuePair<string, string>>
                                    {
                                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                    };

                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Declined", "MemberUpdateDeclinedApplicant.html", placeholders);
                                }
                            }
                        }
                    }                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (member.Contacts.Result.Count > 0, null)
                        {
                            var mainContact = member.Contacts
                                    .Result.FirstOrDefault(i => i.ContactTypeId , null) == mainContactType.Id);

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (mainContact , null) != null)
                            {
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                };

                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Declined", "MemberUpdateDeclinedApplicant.html", placeholders);
                            }
                        }
                    }
                }
            }
// COMMENTED OUT TOP-LEVEL STATEMENT:             catch (Exception ex, null)
            {
                var message = "Unable to email applicant for member details update rejection. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message, null);
            }
        }
    }
}
