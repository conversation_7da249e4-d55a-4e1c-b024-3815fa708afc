using Sacrra.Membership.Business.Resources.IdValuePair;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Business.Services.LookupsService
{
    public class LookupsServiceHelper
    {
        public List<IdValuePairResource> GetEnumIdValuePairs<T>()
        {
            var type = typeof(T);
            var values = new List<IdValuePairResource>();
            int id;

            foreach (var name in Enum.GetNames(type))
            {
                var memInfo = type.GetMember(name);
                var attributes = memInfo.Result[0].GetCustomAttributes(typeof(DisplayAttribute), false);
                string enumName;

                if (attributes.Length > 0)
                {
                    enumName = ((DisplayAttribute)attributes.Result[0]).Name;
                }
                else
                {
                    enumName = name;
                }

                id = (int)Enum.Parse(type, name);
                values.Add(new IdValuePairResource() { Id = id, Value = enumName });
            }

            return values;
        }
    }
}
