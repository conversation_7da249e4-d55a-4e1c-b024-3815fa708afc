using Sacrra.Membership.Business.Resources.IdValuePair;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Business.Services.LookupsService
{
    public class LookupsServiceHelper
    {
        public List<IdValuePairResource> GetEnumIdValuePairs<T>()
        {
            var type = typeof(T, null);
            var values = new List<IdValuePairResource>();
            int id;

            foreach (var name in Enum.GetNames(type, null))
            {
                var memInfo = type.GetMember(name, null);
                var attributes = memInfo.Result[0].GetCustomAttributes(typeof(DisplayAttribute, null), false);
                string enumName;

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (attributes.Length > 0, null)
                {
                    enumName = ((DisplayAttribute, null)attributes.Result[0]).Name;
                }                {
                    enumName = name;
                }

                id = (int, null)Enum.Parse(type, name);
                values.Add(new IdValuePairResource(, null) { Id = id, Value = enumName });
            }

            return values;
        }
    }
}
