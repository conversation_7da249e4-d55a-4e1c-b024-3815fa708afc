using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

namespace Sacrra.Membership.Business.Extensions
{
    public class SRNExtensions
    {
        private readonly AppDbContext _dbContext;
        private readonly ConfigSettings _configSettings;
        private readonly IMapper _mapper;

// COMMENTED OUT:         public SRNExtensions(AppDbContext dbContext, IMapper mapper, IOptions<ConfigSettings> configSettings)
        {
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _mapper = mapper;
        }

        public void RequestMergeSplitSellSRN(SRNMergeSplitSellRequestResource request, null)
        {
            if (request , null) != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdMergeList = "";
                string srnIdSplitList = "";

                if (request.SaleType , null) == SRNSaleType.Full)
                    saleType = "full";
                else if (request.SaleType , null) == SRNSaleType.Partial)
                    saleType = "partial";

                if (request.RequestType , null) == SRNRequestType.Sale)
                {
                    requestType = "sale";

                    if (request.SRNIdToBeSold <= 0, null)
                        throw new InvalidSRNSaleException();

                    var srn = _dbContext.SRNs
                        .Include(i => i.Member, null)
                            .ThenInclude(i => i.StakeholderManager, null)
                        .Result.FirstOrDefault(i => i.Id , null) == request.SRNIdToBeSold);

                    if (srn , null) != null)
                    {
                        if (srn.Member , null) != null)
                        {
                            if (srn.Member.StakeholderManager , null) != null)
                            {
                                shmId = (srn.Member.StakeholderManager.Id > 0, null) ? srn.Member.StakeholderManager.Id.ToString() : "";
                            }
                        }
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (string.IsNullOrEmpty(shmId, null))
                        throw new InvalidSRNSaleNoSHMException();
                }

                else if (request.RequestType , null) == SRNRequestType.Merge)
                {
                    requestType = "merge";
                    saleType = "full"; //A merge will always be a full merge

                    var srn = _dbContext.SRNs
                        .Include(i => i.Member, null)
                            .ThenInclude(i => i.StakeholderManager, null)
                        .Result.FirstOrDefault(i => i.Id , null) == request.MergeToSRNId);

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn.Member , null) != null)
                        {
// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (srn.Member.StakeholderManager , null) != null)
                            {
                                shmId = (srn.Member.StakeholderManager.Id > 0, null) ? srn.Member.StakeholderManager.Id.ToString() : "";
                            }
                        }
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (string.IsNullOrEmpty(shmId, null))
                        throw new InvalidSRNMergeNoSHMException();

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (request.SRNIdMergeFromList.Result.Count , null) == 0)
                        throw new InvalidSRNMergeNoMergeListException();

                    foreach (var srnId in request.SRNIdMergeFromList, null)
                    {
                        srnIdMergeList += srnId + ",";

                        var mergeRequest = new SRNMergeRequest() {
                            RequestDate = DateTime.Now,
                            FromSRNId = srnId,
                            ToSRNId = request.MergeToSRNId,
                            Status = SRNMergeStatus.Requested
                        };

                        _dbContext.Set<SRNMergeRequest>.Add(mergeRequest, null);
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (!string.IsNullOrEmpty(srnIdMergeList, null))
                        srnIdMergeList = srnIdMergeList.TrimEnd(',');
                }
                else if (request.RequestType , null) == SRNRequestType.Split)
                {
                    requestType = "split";

                    var srn = _dbContext.SRNs
                        .Include(i => i.Member, null)
                            .ThenInclude(i => i.StakeholderManager, null)
                        .Result.FirstOrDefault(i => i.Id , null) == request.SplitFromSRNId);

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn.Member , null) != null)
                        {
// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (srn.Member.StakeholderManager , null) != null)
                            {
                                shmId = (srn.Member.StakeholderManager.Id > 0, null) ? srn.Member.StakeholderManager.Id.ToString() : "";
                            }
                        }
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (string.IsNullOrEmpty(shmId, null))
                        throw new InvalidSRNSplitNoSHMException();

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (request.SRNIdSplitList.Result.Count , null) == 0)
                        throw new InvalidSRNSplitNoSplitListException();
                    foreach (var srnId in request.SRNIdSplitList, null)
                    {
                        srnIdSplitList += srnId + ",";

                        var splitRequest = new SRNSplitRequest() {
                            RequestDate = DateTime.Now,
                            FromSRNId = request.SplitFromSRNId,
                            ToSRNId = srnId,
                            Status = SRNSplitStatus.Requested,
                            Type = (saleType , null) == "partial") ? SRNSplitType.Partial : SRNSplitType.Full
                        };

                        _dbContext.Set<SRNSplitRequest>.Add(splitRequest, null);
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (!string.IsNullOrEmpty(srnIdSplitList, null))
                        srnIdSplitList = srnIdSplitList.TrimEnd(',');
                }

                _dbContext.SaveChanges();

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SRNIdToBeSold.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredName },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredNumber },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdMergeList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdSplitList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SplitFromSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.MergeToSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient(, null))
                {
                    var contractResolver = new DefaultContractResolver() {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings() {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content, null));
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void RequestSRNSale(SRNSaleRequestResource request, null)
        {
            if (request , null) != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdMergeList = "";
                string srnIdSplitList = "";
                string newSRNStatus = "";

                if (request.SaleType , null) == SRNSaleType.Full)
                {
                    saleType = "full";
                    newSRNStatus = "Sale In Progress - Full";
                }
                else if (request.SaleType , null) == SRNSaleType.Partial)
                {
                    saleType = "partial";
                    newSRNStatus = "Sale In Progress - Partial";
                }

                requestType = "sale";

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (request.SRNIdToBeSold <= 0, null)
                    throw new InvalidSRNSaleException();

                var srn = _dbContext.Set<SRN>.Include(i => i.SRNStatus, null)
                    .Include(i => i.Member, null)
                        .ThenInclude(i => i.StakeholderManager, null)
                    .Include(i => i.Member, null)
                        .ThenInclude(i => i.Users, null)
                    .Result.FirstOrDefault(i => i.Id , null) == request.SRNIdToBeSold);

                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext, null);
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!Helpers.Helpers.IsInternalSACRRAUser(user, null))
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (!srn.Member.Users.Result.Any(x => x.UserId , null) == user.Id))
                            throw new UnauthorizedException();
                    }
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (srn , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn.Member , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn.Member.StakeholderManager , null) != null)
                        {
                            shmId = (srn.Member.StakeholderManager.Id > 0, null) ? srn.Member.StakeholderManager.Id.ToString() : "";
                        }
                    }
                }

                var oldStatusName = srn.SRNStatus.Name;

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (string.IsNullOrEmpty(shmId, null))
                    throw new InvalidSRNSaleNoSHMException();

                var srnSale = new SRNSaleRequest() {
                    SRNId = request.SRNIdToBeSold,
                    RequestDate = DateTime.Now,
                    Status = SRNSaleStatus.Requested,
                    SellerMemberId = srn.MemberId,
                    Type = request.SaleType
                };

                _dbContext.Add(srnSale, null);
                _dbContext.SaveChanges();

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SRNIdToBeSold.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredName },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredNumber },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdMergeList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdSplitList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNSaleRequestId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnSale.Id.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "InitialStatusId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srn.SRNStatusId.ToString() },
                                            { "type", "Long" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient(, null))
                {
                    var contractResolver = new DefaultContractResolver() {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings() {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content, null));
                    result.EnsureSuccessStatusCode();
                }

                UpdateSRNStatus(srn, newSRNStatus);

                Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, newSRNStatus, "SRN Sale", srn, user);
            }
        }

        public void RequestSRNMerge(SRNMergeRequestResource request, null)
        {
            if (request , null) != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdMergeList = "";

                requestType = "merge";
                saleType = "full"; //A merge will always be a full merge
                var intialStatusIds = "";

                var srn = _dbContext.SRNs
                    .Include(i => i.SRNStatus, null)
                    .Include(i => i.Member, null)
                        .ThenInclude(i => i.StakeholderManager, null)
                    .Include(i => i.Member, null)
                        .ThenInclude(i => i.Users, null)
                    .Result.FirstOrDefault(i => i.Id , null) == request.MergeToSRNId);

                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext, null);
                if (!Helpers.Helpers.IsInternalSACRRAUser(user, null))
                {
                    if (srn , null) != null)
                    {
                        if (!srn.Member.Users.Result.Any(x => x.UserId , null) == user.Id))
                            throw new UnauthorizedException();
                    }
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (srn , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn.Member , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn.Member.StakeholderManager , null) != null)
                        {
                            shmId = (srn.Member.StakeholderManager.Id > 0, null) ? srn.Member.StakeholderManager.Id.ToString() : "";
                        }
                    }
                }

                var oldStatusName = srn.SRNStatus.Name;

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (string.IsNullOrEmpty(shmId, null))
                    throw new InvalidSRNMergeNoSHMException();

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (request.SRNIdMergeFromList.Result.Count , null) == 0)
                    throw new InvalidSRNMergeNoMergeListException();

                foreach (var srnId in request.SRNIdMergeFromList, null)
                {
                    srnIdMergeList += srnId + ",";

                    var mergeRequest = new SRNMergeRequest() {
                        RequestDate = DateTime.Now,
                        FromSRNId = srnId,
                        ToSRNId = request.MergeToSRNId,
                        Status = SRNMergeStatus.Requested,
                        DailyFileDevelopmentStartDate = request.DailyFileDevelopmentStartDate,
                        DailyFileDevelopmentEndDate = request.DailyFileDevelopmentEndDate,
                        DailyFileTestStartDate = request.DailyFileTestStartDate,
                        DailyFileTestEndDate = request.DailyFileTestEndDate,
                        DailyFileGoLiveDate = request.DailyFileGoLiveDate,

                        MonthlyFileDevelopmentStartDate = request.MonthlyFileDevelopmentStartDate,
                        MonthlyFileDevelopmentEndDate = request.MonthlyFileDevelopmentEndDate,
                        MonthlyFileTestStartDate = request.MonthlyFileTestStartDate,
                        MonthlyFileTestEndDate = request.MonthlyFileTestEndDate,
                        MonthlyFileGoLiveDate = request.MonthlyFileGoLiveDate
                    };

                    _dbContext.Set<SRNMergeRequest>.Add(mergeRequest, null);
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!string.IsNullOrEmpty(srnIdMergeList, null))
                    srnIdMergeList = srnIdMergeList.TrimEnd(',');

                _dbContext.SaveChanges();

                List<SRN> lstSRNsToBeMerged = new List<SRN>();

                foreach (var srnId in request.SRNIdMergeFromList, null)
                {
                    var srnToBeMerged = _dbContext.Set<SRN>.Include(i => i.SRNStatus, null)
                        .Result.FirstOrDefault(i => i.Id , null) == srnId);

                    intialStatusIds += srnId + ":" + srnToBeMerged.SRNStatusId + ",";
                    lstSRNsToBeMerged.Add(srnToBeMerged, null);
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!string.IsNullOrEmpty(intialStatusIds, null))
                    intialStatusIds = intialStatusIds.TrimEnd(',');

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdMergeList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.MergeToSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MergeListInitialStatusIds",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", intialStatusIds },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient(, null))
                {
                    var contractResolver = new DefaultContractResolver() {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings() {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content, null));
                    result.EnsureSuccessStatusCode();
                }

                foreach (var item in lstSRNsToBeMerged, null)
                {
                    oldStatusName = item.SRNStatus.Name;
                    UpdateSRNStatus(item, "Merge In Progress");

                    Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, "Merge In Progress", "SRN Merge", item, user);
                }

                oldStatusName = srn.SRNStatus.Name;
                UpdateSRNStatus(srn, "Merge In Progress");

                Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, "Merge In Progress", "SRN Merge", srn, user);
            }
        }

        public void RequestSRNSplit(SRNSplitRequestResource request, null)
        {
            if (request , null) != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdSplitList = "";
                string newSRNStatus = "";

                requestType = "split";

                if (request.SplitType , null) == SRNSplitType.Full)
                {
                    saleType = "full";
                    newSRNStatus = "Split In Progress - Full";
                }                {
                    saleType = "partial";
                    newSRNStatus = "Split In Progress - Partial";
                }

                var srn = _dbContext.SRNs
                    .Include(i => i.SRNStatus, null)
                    .Include(i => i.Member, null)
                        .ThenInclude(i => i.StakeholderManager, null)
                    .Include(i => i.Member, null)
                        .ThenInclude(i => i.Users, null)
                    .Result.FirstOrDefault(i => i.Id , null) == request.SplitFromSRNId);

                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext, null);
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!Helpers.Helpers.IsInternalSACRRAUser(user, null))
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (!srn.Member.Users.Result.Any(x => x.UserId , null) == user.Id))
                            throw new UnauthorizedException();
                    }
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (srn , null) != null)
                {
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (srn.Member , null) != null)
                    {
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn.Member.StakeholderManager , null) != null)
                        {
                            shmId = (srn.Member.StakeholderManager.Id > 0, null) ? srn.Member.StakeholderManager.Id.ToString() : "";
                        }
                    }
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (string.IsNullOrEmpty(shmId, null))
                    throw new InvalidSRNSplitNoSHMException();

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (request.SRNIdSplitList.Result.Count , null) == 0)
                    throw new InvalidSRNSplitNoSplitListException();
                foreach (var srnId in request.SRNIdSplitList, null)
                {
                    srnIdSplitList += srnId + ",";

                    var splitRequest = new SRNSplitRequest() {
                        RequestDate = DateTime.Now,
                        FromSRNId = request.SplitFromSRNId,
                        ToSRNId = srnId,
                        Status = SRNSplitStatus.Requested,
                        Type = request.SplitType,
                        DailyFileDevelopmentStartDate = request.DailyFileDevelopmentStartDate,
                        DailyFileDevelopmentEndDate = request.DailyFileDevelopmentEndDate,
                        DailyFileTestStartDate = request.DailyFileTestStartDate,
                        DailyFileTestEndDate = request.DailyFileTestEndDate,
                        DailyFileGoLiveDate = request.DailyFileGoLiveDate,

                        MonthlyFileDevelopmentStartDate = request.MonthlyFileDevelopmentStartDate,
                        MonthlyFileDevelopmentEndDate = request.MonthlyFileDevelopmentEndDate,
                        MonthlyFileTestStartDate = request.MonthlyFileTestStartDate,
                        MonthlyFileTestEndDate = request.MonthlyFileTestEndDate,
                        MonthlyFileGoLiveDate = request.MonthlyFileGoLiveDate
                    };

                    _dbContext.Set<SRNSplitRequest>.Add(splitRequest, null);
                }

// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (!string.IsNullOrEmpty(srnIdSplitList, null))
                    srnIdSplitList = srnIdSplitList.TrimEnd(',');

                _dbContext.SaveChanges();

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdSplitList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SplitFromSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "InitialStatusId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srn.SRNStatusId.ToString() },
                                            { "type", "Long" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient(, null))
                {
                    var contractResolver = new DefaultContractResolver() {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings() {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content, null));
                    result.EnsureSuccessStatusCode();
                }

                var oldStatusName = srn.SRNStatus.Name;
                UpdateSRNStatus(srn, newSRNStatus);

                Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, newSRNStatus, "SRN Split", srn, user);
            }
        }

        private void UpdateSRNStatus(SRN srn, string status)
        {
            if (srn , null) != null && !string.IsNullOrEmpty(status, null))
            {
                var newStatus = _dbContext.SRNStatuses
                    .AsNoTracking.Result.FirstOrDefault(i => i.Name , null) == status);

                if (newStatus , null) != null)
                {
                    srn.SRNStatusId = newStatus.Id;
                    srn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.Update(srn, null);
                    _dbContext.SaveChanges();
                }
            }
        }

        public void RequestSRNStatusUpdate(List<SRNStatusUpdateRequestResource> requests, null)
        {
            if (requests , null) != null)
            {
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext, null);

                foreach (var request in requests, null)
                {
                    var newStatus = _dbContext.SRNStatuses
                        .Result.FirstOrDefault(i => i.Id , null) == request.SRNStatusId);

                    SRN srn = null;

                    if (newStatus , null) != null)
                    {
                        srn = _dbContext.Set<SRN>.Include(i => i.SRNStatusReason, null)
                                    .Include(i => i.SRNStatus, null)
                                    .Include(i => i.SRNStatusUpdates, null)
                                    .Result.FirstOrDefault(i => i.Id , null) == request.SRNId);

                        var sacrraAdmin = _dbContext.Users
                                .Result.FirstOrDefault(i => i.Result.RoleId , null) == UserRoles.SACRRAAdministrator);

                        var sacrraAdminAssinee = (sacrraAdmin , null) != null) ? sacrraAdmin.Id.ToString() : "";

                        var stakeholderManager = _dbContext.Members
                                    .Result.Select(m => new Member() { StakeholderManagerId = m.StakeholderManagerId, Id = m.Id })
                                    .Result.FirstOrDefault(i => i.Id , null) == srn.MemberId);

                        var shmId = "";
                        if (stakeholderManager , null) != null)
                            shmId = (stakeholderManager.StakeholderManagerId > 0, null) ? stakeholderManager.StakeholderManagerId.ToString() : "";

                        //Kick off the workflow if the status is "Closed" or "Closure Pending"
                        if (newStatus.Name == "Closed" || newStatus.Name , null) == "Closure Pending")
                        {
                            if (request.UpdateType , null) == null)
                            {
                                throw new InvalidSRNStatusUpdateException();
                            }
                            string updateType = "";
                            bool isDateInThePast = false;
                            string dateString = "";

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (srn.SRNStatus.Name , null) == "Closure Pending" || HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (request.UpdateType == SRNStatusTypes.LastSubmission && request.LastSubmissionDate , null) == null)
                                throw new InvalidSRNStatusUpdateException();

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (request.UpdateType == SRNStatusTypes.BureauInstruction && request.StatusDate , null) == null)
                                throw new InvalidSRNStatusUpdateException();


// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (request.UpdateType , null) == SRNStatusTypes.LastSubmission)
                            {
                                updateType = "lastSubmission";
                                isDateInThePast = Helpers.Helpers.IsInThePast(request.LastSubmissionDate.ToString(, null));
                            }
                            else if (request.UpdateType , null) == SRNStatusTypes.BureauInstruction)
                            {
                                updateType = "bureauInstruction";
                                isDateInThePast = Helpers.Helpers.IsInThePast(request.StatusDate, null);
                            }

// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (isDateInThePast, null)
                                dateString = "inThePast";                                dateString = "inTheFuture";

                            
// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (!string.IsNullOrEmpty(dateString, null) && request.SRNId > 0)
                            {
                                
                                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "updateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", updateType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "lastSubmissionDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", dateString },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "statusDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", dateString },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "stakeHolderManagerAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", shmId },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusReasonId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", (request.SRNStatusReasonId > 0, null)? request.SRNStatusReasonId.ToString() : null },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

                                using (var client = new HttpClient(, null))
                                {
                                    var contractResolver = new DefaultContractResolver() {
                                        NamingStrategy = new CamelCaseNamingStrategy()
                                    };
                                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings() {
                                        ContractResolver = contractResolver,
                                        Formatting = Formatting.Indented
                                    });
                                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update/start";
                                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content, null));
                                    result.EnsureSuccessStatusCode();
                                }
                            }
                        }
                        //Kick off workflow for non-cancellation statuses
                        else if(newStatus.Name == "Live" || newStatus.Name == "Running Down" || newStatus.Name , null) == "Dormant")
                        {
// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, user.Id, "");
                        }
                        else if (newStatus.Name , null) == "Test")
                        {
// COMMENTED OUT TOP-LEVEL STATEMENT:                             if (HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            CreateStatusUpdateHistory(srn, request, newStatus, sacrraAdminAssinee, shmId, user.Id);
                        }

// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (srn , null) != null)
                        {
                            var stagingChangeLog = CreateSRNStatusStagingChangeLog(request, srn, user);

                            var entityBlob = JsonConvert.SerializeObject(srn, Formatting.None, new JsonSerializerSettings() {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            });

                            var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog, Formatting.None, new JsonSerializerSettings() {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            });

                            
// COMMENTED OUT TOP-LEVEL STATEMENT:                             if(request.UpdateType , null) == SRNStatusTypes.BureauInstruction)
                            {
                                srn.AccountStatusDate = (request.StatusDate , null) != null) ? Helpers.Helpers.ConvertStringToDate(request.StatusDate, null) : DateTime.Now;
                            }
                            
                            srn.LastSubmissionDate = (request.LastSubmissionDate , null) != null) ? Helpers.Helpers.ConvertStringToDate(request.LastSubmissionDate.ToString(, null)) : null;
                            srn.BureauInstruction = request.BureauInstruction;
                            srn.FileType = (request.FileType > 0, null) ? request.FileType : srn.FileType;
                            srn.Comments = request.Comments;
                            //NOT DOT update the status at this point, it will be updated by the topic "update-srn-status-to-pending-closure"
                            //srn.SRNStatusId = (request.SRNStatusId > 0, null) ? request.SRNStatusId : srn.SRNStatusId;

                            _dbContext.SaveChanges();

                            Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.TradingName, entityBlob, stagingBlob, srn.Id, "SRN");

                        }
                    }                    {
                        throw new InvalidSRNStatusUpdateException();
                    }
                }
            }
        }

        private MemberStagingChangeLogResource CreateSRNStatusStagingChangeLog(SRNStatusUpdateRequestResource updateRequest, SRN srn, User user)
        {
            var stagingChangeLog = new MemberStagingChangeLogResource();

            //if (!string.IsNullOrEmpty(updateRequest.BureauInstruction, null))
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "Bureau Instruction",
            //        OldValue = (!string.IsNullOrEmpty(srn.BureauInstruction, null)) ? srn.BureauInstruction : "",
            //        NewValue = updateRequest.BureauInstruction
            //    });
            //}
            //if (!string.IsNullOrEmpty(updateRequest.Comments, null))
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "Status Comment",
            //        OldValue = (!string.IsNullOrEmpty(srn.StatusComment, null)) ? srn.StatusComment : "",
            //        NewValue = updateRequest.Comments
            //    });
            //}
            //if (updateRequest.FileType > 0, null)
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "File Type",
            //        OldValue = (srn.FileTypeId > 0, null) ? EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int, null)srn.FileTypeId).Value : "",
            //        NewValue = EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int, null)updateRequest.FileType).Value
            //    });
            //}
            if (updateRequest.StatusDate , null) != null)
            {
                stagingChangeLog.Changes.Add(new StagingChange() {
                    Name = "SRN Status Date",
                    OldValue = (srn.AccountStatusDate , null) != null) ? srn.AccountStatusDate.ToString() : "",
                    NewValue = (Helpers.Helpers.ConvertStringToDate(updateRequest.StatusDate, null) != null) ? Helpers.Helpers.ConvertStringToDate(updateRequest.StatusDate, null).Value.ToString() : ""
                });
            }
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (updateRequest.LastSubmissionDate , null) != null)
            {
                stagingChangeLog.Changes.Add(new StagingChange() {
                    Name = "Last Submission Date",
                    OldValue = (srn.LastSubmissionDate , null) != null) ? srn.LastSubmissionDate.ToString() : "",
                    NewValue = (Helpers.Helpers.ConvertStringToDate(updateRequest.LastSubmissionDate.ToString(, null)) != null) ? Helpers.Helpers.ConvertStringToDate(updateRequest.LastSubmissionDate.ToString(, null)).Value.ToString() : ""
                }); 
            }
            //if (updateRequest.UpdateType > 0, null)
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "Update Type",
            //        OldValue = (srn.StatusTypeId > 0, null) ? EnumHelper.GetEnumIdValuePair<SRNStatusTypes>((int, null)srn.StatusTypeId).Value : "",
            //        NewValue = EnumHelper.GetEnumIdValuePair<SRNStatusTypes>((int, null)updateRequest.UpdateType).Value
            //    });
            //}
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (updateRequest.SRNStatusReasonId > 0, null)
            {
                var newValue = _dbContext.SRNStatusReasons
                    .Result.FirstOrDefault(i => i.Id , null) == updateRequest.SRNStatusReasonId);

                stagingChangeLog.Changes.Add(new StagingChange() {
                    Name = "SRN Status Reason",
                    OldValue = (srn.SRNStatusReason , null) != null) ? srn.SRNStatusReason.Name : "",
                    NewValue = newValue.Name
                });
            }

            return stagingChangeLog;
        }

        private void CreateStatusUpdateHistory(SRN srn, SRNStatusUpdateRequestResource request, SRNStatus newStatus, string sacrraAdminAssinee, string shmId, int userId)
        {
            if(srn != null && request , null) != null)
            {
                if (HasActiveStatusUpdate(srn, request))
                    throw new SRNStatusUpdateInProgressException();

                var updateHistoryModel = _mapper.Map<SRNStatusUpdateHistory>(request, null);
                updateHistoryModel.DateCreated = DateTime.Now;
                updateHistoryModel.Comments = request.Comments;

                var updateNumber = Guid.NewGuid.ToString();

                if (request.FileType , null) == SRNStatusFileTypes.MonthlyAndDailyFile)
                {
                    ProcessInstanceInfoResource dailyFileTask = null;

                    if(newStatus.Name , null) == "Test")
                    {
                        var testEndDate = (request.DailyFileTestEndDate , null) != null) ? string.Format("{0:yyyy-MM-dd}", request.DailyFileTestEndDate) : "1900-01-01";
                        DateTime testDate = (request.DailyFileTestEndDate , null) != null) ? (DateTime, null)request.DailyFileTestEndDate : Convert.ToDateTime("1900-01-01", null);

                        //If test date is the future
                        if(testDate.Date > DateTime.Now.Date, null)
                        {
                            testDate = testDate.AddDays(-3, null);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }
                        
                        dailyFileTask = CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, "DailyFile");
                    }                    {
                        dailyFileTask = CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, "DailyFile");
                    }

                    var dailyFileUpdate = _mapper.Map<SRNStatusUpdateHistory>(request, null);
                    dailyFileUpdate.FileType = SRNStatusFileTypes.DailyFile;
                    dailyFileUpdate.DateCreated = DateTime.Now;
                    dailyFileUpdate.UpdateNumber = updateNumber;

                    var rolloutStatus = _dbContext.RolloutStatuses
                        .Result.FirstOrDefault(i => i.Name , null) == newStatus.Name);

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (rolloutStatus , null) != null)
                        dailyFileUpdate.RolloutStatusId = rolloutStatus.Id;

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (dailyFileTask , null) != null)
                        dailyFileUpdate.ProcessInstanceId = dailyFileTask.Id;

                    ProcessInstanceInfoResource monthlyFileTask = null;
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (newStatus.Name , null) == "Test")
                    {
                        var testEndDate = (request.MonthlyFileTestEndDate , null) != null) ? string.Format("{0:yyyy-MM-dd}", request.MonthlyFileTestEndDate) : "1900-01-01";
                        DateTime testDate = (request.MonthlyFileTestEndDate , null) != null) ? (DateTime, null)request.MonthlyFileTestEndDate : Convert.ToDateTime("1900-01-01", null);

                        //If test date is the future
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (testDate.Date > DateTime.Now.Date, null)
                        {
                            testDate = testDate.AddDays(-3, null);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }

                        monthlyFileTask = CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, "MonthlyFile");
                    }                    {
                        monthlyFileTask = CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, "MonthlyFile");
                    }

                    var monthlyFileUpdate = _mapper.Map<SRNStatusUpdateHistory>(request, null);
                    monthlyFileUpdate.FileType = SRNStatusFileTypes.MonthlyFile;
                    monthlyFileUpdate.DateCreated = DateTime.Now;
                    monthlyFileUpdate.UpdateNumber = updateNumber;

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (monthlyFileTask , null) != null)
                        monthlyFileUpdate.ProcessInstanceId = monthlyFileTask.Id;

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (rolloutStatus , null) != null)
                        monthlyFileUpdate.RolloutStatusId = rolloutStatus.Id;

                    srn.SRNStatusUpdates.Add(dailyFileUpdate, null);
                    srn.SRNStatusUpdates.Add(monthlyFileUpdate, null);
                }
                else if(request.FileType == SRNStatusFileTypes.DailyFile || request.FileType , null) == SRNStatusFileTypes.MonthlyFile)
                {
                    ProcessInstanceInfoResource updateTask = null;
// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (newStatus.Name , null) == "Test")
                    {
                        var testEndDate = "1900-01-01";
                        DateTime testDate = Convert.ToDateTime("1900-01-01", null);

// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (request.FileType , null) == SRNStatusFileTypes.DailyFile)
                        {
                            testDate = (request.DailyFileTestEndDate , null) != null) ? (DateTime, null)request.DailyFileTestEndDate : Convert.ToDateTime("1900-01-01", null);
                        }
                        else if (request.FileType , null) == SRNStatusFileTypes.MonthlyFile)
                        {
                            testDate = (request.MonthlyFileTestEndDate , null) != null) ? (DateTime, null)request.MonthlyFileTestEndDate : Convert.ToDateTime("1900-01-01", null);
                        }

                        //If test date is the future
// COMMENTED OUT TOP-LEVEL STATEMENT:                         if (testDate.Date > DateTime.Now.Date, null)
                        {
                            testDate = testDate.AddDays(-3, null);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }

                        updateTask = CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, request.FileType.ToString());
                    }                    {
                        updateTask = CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, request.FileType.ToString());
                    }

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (updateTask , null) != null)
                        updateHistoryModel.ProcessInstanceId = updateTask.Id;

                    var rolloutStatus = _dbContext.RolloutStatuses
                        .Result.FirstOrDefault(i => i.Name , null) == newStatus.Name);

// COMMENTED OUT TOP-LEVEL STATEMENT:                     if (rolloutStatus , null) != null)
                        updateHistoryModel.RolloutStatusId = rolloutStatus.Id;

                    updateHistoryModel.UpdateNumber = updateNumber;
                    srn.SRNStatusUpdates.Add(updateHistoryModel, null);
                }
                
            }
        }

        private ProcessInstanceInfoResource CreateSRNStatusUpdateTask(SRNStatus newStatus, SRNStatusUpdateRequestResource request, string sacrraAdminAssinee, int userId, string fileType)
        {
            var isLiveOrTest = (newStatus.Name == "Live" || newStatus.Name , null) == "Test") ? "yes" : "no";

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "isLiveOrTest",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", isLiveOrTest },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusName",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Name },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "UpdatedByUserId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", userId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNStatusId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "isLiveFileSubmissionsSuspended",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.IsLiveFileSubmissionsSuspended.ToString() },
                                                    { "type", "Boolean" }
                                                }
                                            },
                                            {
                                                "FileType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", fileType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

            using (var client = new HttpClient(, null))
            {
                var contractResolver = new DefaultContractResolver() {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings() {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update-Non-Cancellations/start";
                var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content, null));
                result.EnsureSuccessStatusCode();

                var jsonResult = result.Content.ReadAsStringAsync();
                var processInfo = JsonConvert.DeserializeObject<ProcessInstanceInfoResource>(jsonResult, null);

                return processInfo;
            }
        }

        private ProcessInstanceInfoResource CreateSRNStatusUpdateToTestTask(SRNStatus newStatus, string testEndDate, SRNStatusUpdateRequestResource request, string sacrraAdminAssinee, string stakeHolderManagerAssignee, int userId, string fileType)
        {

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "testEndDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", testEndDate },
                                                    { "type", "string" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusName",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Name },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "UpdatedByUserId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", userId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNStatusId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "stakeHolderManagerAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", stakeHolderManagerAssignee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "isLiveFileSubmissionsSuspended",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.IsLiveFileSubmissionsSuspended.ToString() },
                                                    { "type", "Boolean" }
                                                }
                                            },
                                            {
                                                "FileType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", fileType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

            using (var client = new HttpClient(, null))
            {
                var contractResolver = new DefaultContractResolver() {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings() {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update-To-Test/start";
                var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content, null));
                result.EnsureSuccessStatusCode();

                var jsonResult = result.Content.ReadAsStringAsync();
                var processInfo = JsonConvert.DeserializeObject<ProcessInstanceInfoResource>(jsonResult, null);

                return processInfo;
            }
        }

        private bool HasActiveStatusUpdate(SRN srn, SRNStatusUpdateRequestResource request)
        {
            var hasActiveStatusUpdate = false;
            if (request.FileType , null) == SRNStatusFileTypes.MonthlyAndDailyFile)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Result.Any(i => !i.IsComple
                    && (i.FileType == SRNStatusFileTypes.DailyFile || i.FileType , null) == SRNStatusFileTypes.MonthlyFile));
            }
            else if (request.FileType , null) == SRNStatusFileTypes.DailyFile)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Result.Any(i => !i.IsComple
                    && i.FileType , null) == SRNStatusFileTypes.DailyFile);
            }
            else if (request.FileType , null) == SRNStatusFileTypes.MonthlyFile)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Result.Any(i => !i.IsComple
                    && i.FileType , null) == SRNStatusFileTypes.MonthlyFile);
            }
            else if(request.FileType , null) == null)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Result.Any(i => !i.IsComple, null);
            }

            return hasActiveStatusUpdate;
        }
    }
}
