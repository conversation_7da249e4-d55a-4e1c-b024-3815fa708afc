using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class ContactTypeRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public ContactTypeRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public ContactTypeGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<ContactType>()
                .AsNoTracking()
                .FirstOrDefault(s => s.Id == id);

            var returnRecord = _mapper.Map<ContactTypeGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<ContactType>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public ContactTypeGetResource Update(ContactTypeUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<ContactType>(modelForUpdate);

            _dbContext.Set<ContactType>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(ContactTypeCreateResource modelForCreate)
        {
            var model = _mapper.Map<ContactType>(modelForCreate);

            _dbContext.Set<ContactType>().Add(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<ContactType>().Find(id);

            _dbContext.Set<ContactType>().Remove(entity);
            _dbContext.SaveChanges();
        }
        public List<IdValuePairResource> List(string requestType, MembershipTypes membershipTypeId)
        {
            var query = _dbContext.Set<ContactType>()
                .AsNoTracking()
                .ToList();

            if(requestType == "MemberRegistration" || requestType == "MemberUpdate")
            {
                if(membershipTypeId == MembershipTypes.ALGLeader || membershipTypeId == MembershipTypes.Bureau)
                {
                    query = query.Where(i => i.ApplicableTo == ContactTypeEnum.Member || i.ApplicableTo == ContactTypeEnum.SRN).ToList();
                }
                else if(membershipTypeId == MembershipTypes.Affiliate)
                {
                    var dataContact = query.FirstOrDefault(i => i.Name == "Data Contact Details");

                    query = query.Where(i => i.ApplicableTo == ContactTypeEnum.Member).ToList();
                    query = query.Where(i => i.Name != "Financial Contact Details").ToList();

                    query.Add(dataContact);
                }
                else
                {
                    query = query.Where(i => i.ApplicableTo == ContactTypeEnum.Member).ToList();
                }
            }
            if (requestType == "SRNRegistration" || requestType == "SRNUpdate")
            {
                if (membershipTypeId != MembershipTypes.ALGLeader && membershipTypeId != MembershipTypes.Bureau && membershipTypeId != MembershipTypes.Affiliate)
                    query = query.Where(i => i.ApplicableTo == ContactTypeEnum.SRN).ToList();
                else
                    query = new List<ContactType>();
            }

            var resource = _mapper.Map<List<IdValuePairResource>>(query).ToList();
            return resource;
        }
    }
}
