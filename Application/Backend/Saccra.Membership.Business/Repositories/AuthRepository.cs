using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Auth;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;

using RestSharp;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Repositories
{
    public class AuthRepository
    {
        private readonly AppDbContext dbContext;
        private readonly UserRepository _userRepository;
        private readonly Auth0APIManagement _auth0APIManagementSettings;
        private readonly Auth0 _auth0Settings;
        private readonly EmailService _emailService;

        public IMapper _mapper { get; }
        private readonly ConfigSettings _configSettings;

        public AuthRepository(AppDbContext dbContext, IMapper mapper, UserRepository userRepository,
             IOptions<Auth0> auth0Settings, IOptions<Auth0APIManagement> auth0APIManagementSettings,
             EmailService emailService, IOptions<ConfigSettings> configSettings)
        {
            _mapper = mapper;
            _userRepository = userRepository;
            _auth0APIManagementSettings = auth0APIManagementSettings.Value;
            _auth0Settings = auth0Settings.Value;
            this.dbContext = dbContext;
            _emailService = emailService;
            _configSettings = configSettings.Value;
        }

        public AuthUserResource Signin(AuthUserSigninResource AuthUserSigninResource)
        {
            var user = dbContext.Users
                .Result.Where(u => u.Email == AuthUserSigninResource.Email.ToLower())
                .AsNoTracking()
                .Result.FirstOrDefault();

            if (user == null)
                throw new InvalidUserException();

            if (!user.IsEmailConfirmed)
                throw new EmailNotConfirmedException();

            if (user.RequirePasswordChange)
                throw new PasswordChangeRequiredException();

            var authResource = _mapper.Map<AuthUserResource>(user);

            var accessToken = DoAuth0Athentication(AuthUserSigninResource.Email.ToLower(), AuthUserSigninResource.Password);

            if (string.IsNullOrEmpty(user.Auth0Id))
            {
                var authUser = GetAuth0UserByEmail(user.Email.ToLower());
                if (authUser != null)
                {
                    user.Auth0Id = authUser.user_id;
                    dbContext.Update(user);
                    dbContext.SaveChanges();
                }
            }

            authResource.AccessToken = accessToken;

            return authResource;
        }

        public UserGetResource Signup(UserCreateSimpleResource signupUser)
        {
            var foundUser = GetAuth0UserByEmail(signupUser.Email);

            if(foundUser != null)
            {
                if (!string.IsNullOrEmpty(foundUser.user_id))
                {
                    var foundLocalUser = _userRepository.GetByEmail(signupUser.Email.Trim());
                    if(foundLocalUser == null)
                    {
                        DeleteAuth0User(foundUser.user_id);
                    }
                    else
                    {
                        throw new UserExistsException();
                    }
                }
            }

            var userId = CreateAuth0User(signupUser);
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings).Result;
            var defaultRole = GetDefaultAuth0Role(token);

            AssignDefaultAuth0UserRole(userId, defaultRole, token);

            var localUser = CreateLocalUser(signupUser, userId);
            return localUser;
        }

        public string SignupInternalUser(UserCreateSimpleResource signupUser, string roleName)
        {
            var userId = CreateAuth0User(signupUser);
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings).Result;
            var auth0RoleId = GetAuth0RoleIDByName(roleName);

            AssignDefaultAuth0UserRole(userId, auth0RoleId, token);
            return userId;
        }

        public string CreateAuth0User(UserCreateSimpleResource signupUser)
        {
            using (var client = new HttpClient())
            {
                var auth0User = new
                {
                    email = signupUser.Email,
                    password = signupUser.Password,
                    client_id = _auth0APIManagementSettings.ClientID,
                    connection = _auth0APIManagementSettings.Connection
                };
                var json = JsonConvert.SerializeObject(auth0User);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = "https://" + _auth0APIManagementSettings.Domain + "/dbconnections/signup";
                var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content));

                if(result.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var responseString = result.Content.ReadAsStringAsync();
                    var userCreateResponse = JsonConvert.DeserializeObject<Auth0UserCreateResponse>(responseString);
                    throw new Exception(string.Format("Error: {0}. Policy: {1}", userCreateResponse.Message, userCreateResponse.Policy));
                }

                var userId = JObject.Parse(result.Content.ReadAsStringAsync())["_id"].ToString();

                return "auth0|" + userId;
            }
        }

        public JToken GetDefaultAuth0Role(string token)
        {
            using (var client = new HttpClient())
            {
                var uri = _auth0APIManagementSettings.APIBaseURL + "/roles";
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var result = client.Execute(new RestRequest(uri, Method.Get);
                result.EnsureSuccessStatusCode();

                var roles = JArray.Parse(result.Content.ReadAsStringAsync());
                JToken role = roles.Result.FirstOrDefault(i => i.SelectToken("name").ToString() == "User");

                return role;
            }
        }

        public void AssignDefaultAuth0UserRole(string userId, JToken role, string token)
        {
            using (var client = new HttpClient())
            {
                if (role != null)
                {
                    var roleId = JObject.Parse(role.ToString())["id"].ToString();

                    if (!string.IsNullOrEmpty(roleId))
                    {
                        var userRole = new
                        {
                            roles = new[] { roleId }
                        };

                        var uri = _auth0APIManagementSettings.APIBaseURL + "/users/" + userId + "/roles";
                        var json = JsonConvert.SerializeObject(userRole);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                        var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content));
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public void AssignDefaultAuth0UserRole(string userId, string roleId, string token)
        {
            using (var client = new HttpClient())
            {
                if (!string.IsNullOrEmpty(roleId))
                {
                    var userRole = new
                    {
                        roles = new[] { roleId }
                    };

                    var uri = _auth0APIManagementSettings.APIBaseURL + "/users/" + userId + "/roles";
                    var json = JsonConvert.SerializeObject(userRole);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content));
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public UserGetResource CreateLocalUser(UserCreateSimpleResource signupUser, string auth0Id)
        {
            if (signupUser != null)
            {
                var createUserResource = _mapper.Map<UserCreateResource>(signupUser);
                var userId = _userRepository.Create(createUserResource, Database.Enums.UserRoles.User, auth0Id);

                var user = _userRepository.Get(userId);
                return user;
            }

            return null;
        }

        private string DoAuth0Athentication(string email, string password)
        {
            if (!string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(password))
            {
                using (var client = new HttpClient())
                {
                    var loginResource = new
                    {
                        username = email,
                        password,
                        grant_type = _auth0Settings.GrantType,
                        client_id = _auth0Settings.ClientID,
                        client_secret = _auth0Settings.ClientSecret,
                        audience = _auth0Settings.Audience
                    };

                    string parameters = "grant_type=password&username=" + email + "&password=" + password
                        + "&audience=" + loginResource.audience + "&scope=openid&client_id=" + loginResource.client_id
                        + "&client_secret=" + loginResource.client_secret;

                    var uri = "https://" + _auth0Settings.Domain + "/oauth/token";
                    var content = new StringContent(parameters, Encoding.UTF8, "application/x-www-form-urlencoded");

                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content));
                    if(!result.IsSuccessStatusCode)
                        throw new InvalidUserException();

                    result.EnsureSuccessStatusCode();

                    var stringResult = JObject.Parse(result.Content.ReadAsStringAsync());
                    var accessToken = stringResult["access_token"].ToString();

                    return accessToken;
                }
            }

            return null;
        }

        public string GetAuth0RoleIDByName(string roleName)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings).Result;

            if (!string.IsNullOrEmpty(roleName) && !string.IsNullOrEmpty(token))
            {
                using (var client = new HttpClient())
                {
                    var uri = _auth0APIManagementSettings.APIBaseURL + "/roles";
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    var result = client.Execute(new RestRequest(uri, Method.Get);
                    result.EnsureSuccessStatusCode();

                    var roles = JArray.Parse(result.Content.ReadAsStringAsync());
                    JToken role = roles.Result.FirstOrDefault(i => i.SelectToken("name").ToString() == roleName);

                    if (role != null)
                    {
                        var roleObject = JObject.Parse(role.ToString());
                        var roleId = roleObject["id"].ToString();
                        return roleId;
                    }
                }
            }
            return null;
        }

        public void AssignAuth0UserRole(string userId, string roleId)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings).Result;

            if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(roleId) && !string.IsNullOrEmpty(token))
            {
                using (var client = new HttpClient())
                {
                    var userRole = new
                    {
                        roles = new[] { roleId }
                    };

                    var uri = _auth0APIManagementSettings.APIBaseURL + "/users/" + userId + "/roles";
                    var json = JsonConvert.SerializeObject(userRole);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    var result = client.Execute(new RestRequest(uri, Method.Post).AddJsonBody(content));
                    result.EnsureSuccessStatusCode();
                }
            }
        }
        public Auth0UserGetResource GetAuth0UserByEmail(string email)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings).Result;

            if (!string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(token))
            {
                using (var client = new HttpClient())
                {
                    var uri = _auth0APIManagementSettings.APIBaseURL + "/users-by-email?email=" + email;
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    var result = client.Execute(new RestRequest(uri, Method.Get);
                    result.EnsureSuccessStatusCode();

                    var users = JArray.Parse(result.Content.ReadAsStringAsync());
                    JToken userToken = users.Result.FirstOrDefault(i => i.SelectToken("email").ToString() == email);

                    if (userToken != null)
                    {
                        var userObject = userToken.ToObject<Auth0UserGetResource>();
                        return userObject;
                    }
                }
            }
            return null;
        }

        public void CreateInternalUsersInAuth0()
        {
            var users = dbContext.Users
                .Result.Where(i => string.IsNullOrEmpty(i.Auth0Id))
                .ToList();

            if (users.Result.Any())
            {
                foreach (var user in users)
                {
                    if (!string.IsNullOrEmpty(user.Email))
                    {
                        var foundUser = GetAuth0UserByEmail(user.Email.Trim().ToLower());

                        if (foundUser == null)
                        {
                            var userResource = new UserCreateSimpleResource
                            {
                                Email = user.Email.Trim(),
                                FirstName = user.FirstName,
                                LastName = user.LastName,
                                Password = "P@ssw0rd1"
                            };

                            var userRole = EnumHelper.GetEnumShortName<UserRoles>((int)user.RoleId);
                            string roleName = (userRole != null) ? userRole.Value : "User";

                            if (userRole != null)
                            {
                                var auth0UserId = SignupInternalUser(userResource, roleName);
                                if (!string.IsNullOrEmpty(auth0UserId))
                                {
                                    user.Auth0Id = auth0UserId;
                                    dbContext.Update(user);

                                    //Send user credentials via email
                                    var uiBaseURL = _configSettings.FrontEndBaseUri; //_config["ConfigSettings:FrontEndBaseUri"];
                                    List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>
                                    {
                                        new KeyValuePair<string, string>("[PasswordResetLink]", string.Format("{0}{1}", uiBaseURL, "/resetPasswordEmail"))
                                    };

                                    _emailService.SendEmail(user.Email, user.FirstName, "SACRRA User Account Created", "UserAccountCreated.html", placeholders);
                                }
                            }
                        }
                        else
                        {
                            user.Auth0Id = foundUser.user_id;
                            dbContext.Update(user);
                        }
                    }
                }

                dbContext.SaveChanges();
            }
        }

        public void DeleteAuth0User(string id)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings).Result;

            if (!string.IsNullOrEmpty(id) && !string.IsNullOrEmpty(token))
            {
                using (var client = new HttpClient())
                {
                    var uri = _auth0APIManagementSettings.APIBaseURL + "/users/" + id;
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    var result = client.Execute(new RestRequest(uri, Method.Delete, Method.Get));
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void PostChangePasswordAuth0Hook(Auth0PostChangePasswordResource changePasswordResource)
        {
            if(changePasswordResource != null)
            {
                if (!string.IsNullOrEmpty(changePasswordResource.UserId))
                {
                    var apiKey = _configSettings.JwtKey; //_config["ConfigSettings:JwtKey"];

                    //To ensure that request came from a trusted source
                    if (apiKey == changePasswordResource.ApiKey)
                    {
                        var user = dbContext.Users
                        .Result.Where(u => u.Auth0Id == changePasswordResource.UserId)
                        .AsNoTracking()
                        .Result.FirstOrDefault();

                        if (user != null)
                        {
                            user.RequirePasswordChange = false;
                            dbContext.Update(user);
                            dbContext.SaveChanges();
                        }
                    }
                }
            }
        }
    }
}