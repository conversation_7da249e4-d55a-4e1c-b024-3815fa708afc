using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.Member;
using Sacrra.Membership.Business.Resources.User;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Repositories
{
    public class UserRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }
        private readonly EmailService _emailService;
        private readonly Auth0APIManagement _auth0APIManagementSettings;
        private readonly ConfigSettings _configSettings;

        public UserRepository(AppDbContext dbContext, IMapper mapper, EmailService emailService,
            IOptions<ConfigSettings> configSettings, IOptions<Auth0APIManagement> auth0APIManagementSettings)
        {
            _dbContext = dbContext;
            _mapper = mapper;
            _emailService = emailService;
            _auth0APIManagementSettings = auth0APIManagementSettings.Value;
            _configSettings = configSettings.Value;
        }

        public UserGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<User>()
                  .AsNoTracking()
                  .Include(i => i.MembersIManage)
                  .FirstOrDefault(s => s.Id == id);
            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public UserGetResource GetByEmail(string email)
        {
            var selectRecord = _dbContext.Set<User>()
                  .AsNoTracking()
                  .Include(i => i.MembersIManage)
                  .FirstOrDefault(s => s.Email == email);
            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public UserGetResource GetCamundaUsers(int id)
        {
            var selectRecord = _dbContext.Set<User>()
                  .AsNoTracking()
                  .FirstOrDefault(s => s.Id == id);
            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public int Create(UserCreateResource user, UserRoles roleId, string auth0Id)
        {
            byte[] passwordHash, passwordSalt;
            byte[] emailConfirmHash, emailConfirmSalt;
            var newUser = new User();

            if (!string.IsNullOrEmpty(user.Email))
                user.Email = user.Email.ToLower();

            if (_dbContext.Users.Any(x => x.Email == user.Email))
                throw new UserExistsException();

            //TODO: add email confirmation string/token
            string emailConfirmToken = Guid.NewGuid().ToString();
            Helpers.Helpers.CreateHashAndSalt(user.Password, out passwordHash, out passwordSalt);
            Helpers.Helpers.CreateHashAndSalt(emailConfirmToken, out emailConfirmHash, out emailConfirmSalt);

            newUser = _mapper.Map<User>(user);
            newUser.PasswordHash = passwordHash;
            newUser.PasswordSalt = passwordSalt;
            newUser.EmailConfirmationHash = emailConfirmHash;
            newUser.EmailConfirmationSalt = emailConfirmSalt;
            newUser.RoleId = roleId;
            newUser.Auth0Id = auth0Id;
            newUser.IsEmailConfirmed = false;
            _dbContext.Users.Add(newUser);
            _dbContext.SaveChanges();

            SendEmail(newUser.Email, newUser.FirstName, "SACRRA Email Confirmation", emailConfirmToken);

            return newUser.Id;
        }

        public void Delete(int id)
        {
            var entity = _dbContext.Set<User>().Find(id);

            _dbContext.Set<User>().Remove(entity);
            _dbContext.SaveChanges();
        }

        public UserGetResource Update(UserUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<User>(modelForUpdate);

            _dbContext.Set<User>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public async Task<Helpers.PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<User>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.FullName.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.FullName);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.FullName);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new Helpers.PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }

        public async Task<List<IdValuePairResource>> ListAllUsers(string auth0Id)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (user == null)
                throw new InvalidUserException();

            var query = _dbContext.Set<User>()
                    .ToList();

            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                query = query.Where(x => x.Id == user.Id).ToList();

            var users = _mapper.Map<List<IdValuePairResource>>(query);

            return users;
        }
        public async Task<List<IdValuePairResource>> ListAllUsersCamunda()
        {
            var query = _dbContext.Set<User>()
                    .ToList();

            var users = _mapper.Map<List<IdValuePairResource>>(query);

            return users;
        }
        public async Task<List<string>> GetAllUserRoles()
        {
            var users = _dbContext.Users.AsQueryable();
            var userRoles = _dbContext.Users  // Start with your table
                .GroupBy(r => r.RoleId) // Group by the key of your choice
                .Select(g => new { RoleId = g.Key, Count = g.Count() }) // Create an anonymous type w/results
                .ToList();

            //var userRoles = from count= user in users

            List<string> roles = new List<string>();
            foreach (var role in userRoles)
            {
                roles.Add(role.RoleId.ToString());
            }

            return roles;
        }

        public List<UserTaskListGetResource> ListInternalUsers()
        {
            var query = _dbContext.Set<User>()
                .AsQueryable();

            query = query.Where(i => i.RoleId == Database.Enums.UserRoles.StakeHolderAdministrator
                || i.RoleId == Database.Enums.UserRoles.FinancialAdministrator
                || i.RoleId == Database.Enums.UserRoles.StakeHolderManager);

            var users = _mapper.Map<List<UserTaskListGetResource>>(query);

            return users;
        }
        public List<UserTaskListGetResource> ListInternalUsers(List<UserRoles> roles)
        {
            var query = _dbContext.Set<User>()
                .AsQueryable();

            var users = new List<User>();
            foreach (var user in query)
            {
                foreach (var role in roles)
                {
                    if (user.RoleId == role)
                        users.Add(user);
                }
            }

            var mappedUsers = _mapper.Map<List<UserTaskListGetResource>>(users);

            return mappedUsers;
        }

        public UserTaskListGetResource Get(string userName)
        {
            if (string.IsNullOrEmpty(userName))
                throw new InvalidUserNameException();

            var selectRecord = _dbContext.Set<User>()
                    .AsNoTracking()
                    .FirstOrDefault(s => s.Id == Convert.ToInt32(userName));

            var returnRecord = _mapper.Map<UserTaskListGetResource>(selectRecord);

            return returnRecord;
        }
        private void SendEmail(string recepientAddress, string recepientName, string subject, string emailConfirmToken)
        {
            var emailConfirmLink = _configSettings.FrontEndBaseUri + "/confirmEmail/" + recepientAddress + "/" + emailConfirmToken;
            var placeholders = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("[EmailConfirmationLink]", emailConfirmLink)
            };

            _emailService.SendEmail(recepientAddress, recepientName, subject, "EmailConfirmation.html", placeholders);
        }

        public bool ConfirmEmail(string email, string token)
        {
            bool isConfirmed = false;

            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(token))
                return false;

            var user = _dbContext.Users
                .Where(u => u.Email == email)
                .AsNoTracking()
                .FirstOrDefault();

            if (user == null)
                isConfirmed = false;

            if (!Helpers.Helpers.VerifyHashAndSaft(token, user.EmailConfirmationHash, user.EmailConfirmationSalt))
                isConfirmed = false;
            else
            {
                user.IsEmailConfirmed = true;
                _dbContext.Set<User>().Update(user);
                _dbContext.SaveChanges();
                isConfirmed = true;

                ConfirmAuth0Email(user.Auth0Id);
            }

            return isConfirmed;
        }

        public async Task<IEnumerable<MemberGetCustomResource>> ListMembers(string auth0Id)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (user == null)
                throw new InvalidUserException();

            int userId = user.Id;
            if (userId > 0)
            {
                var members = _dbContext.Members
                    .Include(i => i.Users)
                    .Include(i => i.StakeholderManager)
                    .AsNoTracking()
                    .Where(i => i.Users.Any(x => x.UserId == userId) && i.MembershipTypeId != MembershipTypes.ALGClient).ToList();

                var partialMembers = _dbContext.PartialMembers
                    .AsNoTracking()
                    .Where(s => s.UserId == userId).ToList();

                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    partialMembers = partialMembers.Where(x => x.UserId == userId).ToList();

                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    members = members.Where(x => x.Users.Any(i => i.UserId == userId)).ToList();

                var partialMembersList = _mapper.Map<List<MemberGetCustomResource>>(partialMembers);

                var memberResourceList = _mapper.Map<List<MemberGetCustomResource>>(members);

                memberResourceList.AddRange(partialMembersList);

                foreach (var item in memberResourceList)
                {
                    var memberType = (item.MembershipTypeId > 0) ? EnumHelper.GetEnumIdValuePair<MembershipTypes>(item.MembershipTypeId) : null;
                    var appStatus = (item.MembershipTypeId > 0) ? EnumHelper.GetEnumIdValuePair<ApplicationStatuses>(item.ApplicationStatusId) : null;
                    item.MemberType = (memberType != null) ? memberType.Value : "";
                    item.ShmName = item.StakeholderManager.Value;
                    item.ApplicationStatus = (appStatus != null) ? appStatus.Value : "";

                    var srns = _dbContext.SRNs
                        .Include(i => i.SRNStatus)
                        .Where(i => i.MemberId == item.Id || i.ALGLeaderId == item.Id);

                    if (srns != null)
                    {
                        item.TotalSRNs = srns.Count();

                        string[] activeStatuses = new string[] { "Live", "Live - Missing information",
                        "Test - DTH user info to be updated", "Sale In Progress - Partial",
                        "Sale In Progress - Full", "Split In Progress - Partial", "Split In Progress - Full",
                        "Merge In Progress"};

                        item.TotalActiveSRNs = srns.Count(i => activeStatuses.Contains(i.SRNStatus.Name));
                    }
                }

                return memberResourceList;
            }
            return null;
        }

        public void ResetPassword(PasswordResetResource passwordResetResource)
        {
            if (passwordResetResource != null)
            {
                if (!string.IsNullOrEmpty(passwordResetResource.Email))
                {
                    using (var client = new HttpClient())
                    {
                        var auth0User = new
                        {
                            email = passwordResetResource.Email,
                            client_id = _auth0APIManagementSettings.ClientID,
                            connection = _auth0APIManagementSettings.Connection
                        };
                        var json = JsonConvert.SerializeObject(auth0User);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var uri = "https://" + _auth0APIManagementSettings.Domain + "/dbconnections/change_password";
                        var result = client.Post(uri, content);
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public async Task<List<IdValuePairResource>> ListUsersByRole(UserRoles roleId)
        {
            var users = _dbContext.Set<User>()
                    .Where(i => i.RoleId == roleId)
                    .ToList();

            var resource = _mapper.Map<List<IdValuePairResource>>(users);

            return resource;
        }

        public UserGetResource GetByRole(UserRoles userRole)
        {
            var selectRecord = _dbContext.Set<User>()
                        .AsNoTracking()
                        .FirstOrDefault(s => s.RoleId == userRole);

            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public List<IdValuePairResource> ListAllRoles()
        {
            var roles = EnumHelper.GetEnumIdValuePairs<UserRoles>();
            return roles;
        }
        public void ResendEmailConfirmation(string email)
        {
            if (!string.IsNullOrEmpty(email))
            {
                var user = _dbContext.Users
                    .FirstOrDefault(i => i.Email == email);

                if(user != null)
                {
                    string emailConfirmToken = Guid.NewGuid().ToString();
                    Helpers.Helpers.CreateHashAndSalt(emailConfirmToken, out byte[] emailConfirmHash, out byte[] emailConfirmSalt);

                    user.EmailConfirmationHash = emailConfirmHash;
                    user.EmailConfirmationSalt = emailConfirmSalt;
                    _dbContext.Set<User>().Update(user);
                    _dbContext.SaveChanges();

                    SendEmail(email, user.FirstName, "SACRRA Email Confirmation", emailConfirmToken);
                }
            }
        }

        public void ConfirmAuth0Email(string auth0Id)
        {
            if (!string.IsNullOrEmpty(auth0Id))
            {
                using (var client = new HttpClient())
                {
                    var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

                    if (!string.IsNullOrEmpty(token))
                    {
                        var data = new
                        {
                            email_verified = true
                        };

                        var uri = _auth0APIManagementSettings.APIBaseURL + "/users/" + auth0Id;
                        var json = JsonConvert.SerializeObject(data);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                        var result = client.PatchAsync(uri, content);
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public void RemoveAuth0RoleFromUser(string userId, string roleId)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

            if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(roleId) && !string.IsNullOrEmpty(token))
            {
                var userRole = new
                {
                    roles = new[] { roleId }
                };

                var json = JsonConvert.SerializeObject(userRole);

                var client = new RestClient();
                var request = new RestRequest(_auth0APIManagementSettings.APIBaseURL + "/users/" + userId + "/roles", Method.Delete);
                request.AddHeader("content-type", "application/json");
                request.AddHeader("authorization", "Bearer " + token);
                request.AddHeader("cache-control", "no-cache");
                request.AddParameter("application/json", json, ParameterType.RequestBody);
                client.Execute(request);
            }
        }
        public UserGetResource GetSignedInUser(string auth0Id)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            var authResource = _mapper.Map<UserGetResource>(user);

            return authResource;
        }

        public void EnableOrDisableUser(string userId, bool isBlocked)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

            if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(token))
            {
                var userUpdate = new
                {
                    blocked = isBlocked
                };

                var client = new RestClient(_auth0APIManagementSettings.APIBaseURL + "/users/" + userId);
                var request = new RestRequest(_auth0APIManagementSettings.APIBaseURL + "/users/" + userId, Method.Patch);
                request.AddHeader("content-type", "application/json");
                request.AddHeader("authorization", "Bearer " + token);
                request.AddHeader("cache-control", "no-cache");
                request.AddJsonBody(userUpdate);
                client.Execute(request);
            }
        }

        public void UpdateLastReadTcsAndCs()
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if(user != null)
            {
                user.LastReadTsAndCsAt = DateTime.Now;
                _dbContext.Update(user);
                _dbContext.SaveChanges();
            }
        }

        public void UpdateUserProfile(UserUpdateProfileDTO profileDTO)
        {
            if(profileDTO != null)
            {
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                if(user != null )
                {
                    //Update user profile in Auth0
                    var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

                    if (!string.IsNullOrEmpty(token))
                    {
                        var userUpdate = new
                        {
                            given_name = profileDTO.FirstName,
                            family_name = profileDTO.LastName,
                            name = $"{profileDTO.FirstName} {profileDTO.LastName}"
                        };

                        var client = new RestClient(_auth0APIManagementSettings.APIBaseURL + "/users/" + user.Auth0Id);
                        var request = new RestRequest(_auth0APIManagementSettings.APIBaseURL + "/users/" + user.Auth0Id, Method.Patch);
                        request.AddHeader("content-type", "application/json");
                        request.AddHeader("authorization", "Bearer " + token);
                        request.AddHeader("cache-control", "no-cache");
                        request.AddJsonBody(userUpdate);
                        var response = client.Execute(request);

                        if (response.IsSuccessful)
                        {
                            var auth0User = JsonConvert.DeserializeObject<Auth0UserGetResource>(response.Content);

                            //Update user in the DB
                            user.FirstName = profileDTO.FirstName;
                            user.LastName = profileDTO.LastName;
                            user.DateCreated = Convert.ToDateTime(auth0User.created_at);

                            _dbContext.Update(user);
                            _dbContext.SaveChanges();
                        }
                    }
                }
            }
        }
    }
}
