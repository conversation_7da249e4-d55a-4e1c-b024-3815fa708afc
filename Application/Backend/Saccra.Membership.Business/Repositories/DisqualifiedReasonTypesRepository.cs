using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saccra.Membership.Business.Repositories
{
    public class DisqualifiedReasonTypesRepository
    {
        private readonly AppDbContext _dbContext;

        public DisqualifiedReasonTypesRepository(AppDbContext dbContext)
        {
            this._dbContext = dbContext;
        }

        public DisqualifiedReason GetById(int Id)
        {
            var selectRecord = _dbContext.DisqualifiedReasonTypes
                .Result.FirstOrDefault(s => s.Id == Id);
            return selectRecord;
        }

        public async Task<List<IdValuePairResource>> GetAllDisqualifiedReasons()
        {
            var reasonsList = _dbContext.DisqualifiedReasonTypes
                 .Result.Select(x => new IdValuePairResource
                  {
                      Id = x.Id,
                      Value = x.Name
                  })
                    .ToList();

            return reasonsList;
        }

        public DisqualifiedReasonMapping GeMappingtById(int Id)
        {
            var selectRecord = _dbContext.DisqualifiedReasonMapping
                .Result.FirstOrDefault(s => s.Id == Id);
            return selectRecord;
        }


        // Update / Add a mapping
        public DisqualifiedReasonMapping? UpdateMappingAsync(DisqualifiedReasonMapping mapping)
        {
            // Check if it's an update or a new insert
            var existing = _dbContext.DisqualifiedReasonMapping
                .Result.FirstOrDefault(s => s.Id == mapping.Id);
            if (mapping.ReasonId == null)
            {
                if(existing != null)
                {
                    _dbContext.DisqualifiedReasonMapping.Remove(existing);
                    _dbContext.SaveChanges();   
                }
                return null;
            }
            if (existing != null)
            {
                // Update existing
                existing.ReasonId = mapping.ReasonId;
                existing.FreeTextReason = mapping.FreeTextReason;
            }
            else
            {
                // Add new
                mapping = new DisqualifiedReasonMapping
                {
                    Id = 0,
                    ReasonId = mapping.ReasonId,
                    FreeTextReason = mapping.FreeTextReason
                };

                _dbContext.DisqualifiedReasonMapping.Add(mapping);
            }

            _dbContext.SaveChanges();
            return mapping;
        }

    }
}
