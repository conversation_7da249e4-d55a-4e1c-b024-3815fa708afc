using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.Bureau;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class BureauRepository
    {
        private readonly AppDbContext _dbContext;

        public IMapper _mapper { get; }

        public BureauRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public BureauGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<Bureau>()
                .AsNoTracking()
                .FirstOrDefault(s => s.Id == id);

            var returnRecord = _mapper.Map<BureauGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<Bureau>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public BureauGetResource Update(BureauUpdateResource modelForUpdate)
        {
            var foundModel = _dbContext.Bureaus
                    .FirstOrDefault(i => i.Id == modelForUpdate.Id);

            var model = _mapper.Map<Bureau>(modelForUpdate);

            _dbContext.Set<Bureau>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(BureauCreateResource modelForCreate)
        {
            var model = _mapper.Map<Bureau>(modelForCreate);

            _dbContext.Set<Bureau>().Add(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<Bureau>().Find(id);

            _dbContext.Set<Bureau>().Remove(entity);
            _dbContext.SaveChanges();
        }
        public async Task<PagedList<IdValuePairResource>> GetAll(NameListParams listParams)
        {
            var query = _dbContext.Set<Member>()
                    .AsNoTracking()
                    .Where(i => i.MembershipTypeId == Database.Enums.MembershipTypes.Bureau)
                    .AsQueryable();

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
    }
}
