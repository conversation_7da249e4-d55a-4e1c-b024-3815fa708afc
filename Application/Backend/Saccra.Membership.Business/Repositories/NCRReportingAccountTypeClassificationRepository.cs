using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.NCRReportingAccountTypeClassification;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class NCRReportingAccountTypeClassificationRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public NCRReportingAccountTypeClassificationRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public NCRReportingAccountTypeClassificationGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<NCRReportingAccountTypeClassification>()
                .AsNoTracking()
                .FirstOrDefault(s => s.Id == id);

            var returnRecord = _mapper.Map<NCRReportingAccountTypeClassificationGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<NCRReportingAccountTypeClassification>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            itemsToReturn = itemsToReturn.OrderBy(x => x.Value).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public NCRReportingAccountTypeClassificationGetResource Update(NCRReportingAccountTypeClassificationUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<NCRReportingAccountTypeClassification>(modelForUpdate);

            _dbContext.Set<NCRReportingAccountTypeClassification>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(NCRReportingAccountTypeClassificationCreateResource modelForCreate)
        {
            var model = _mapper.Map<NCRReportingAccountTypeClassification>(modelForCreate);

            _dbContext.Set<NCRReportingAccountTypeClassification>().Add(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<NCRReportingAccountTypeClassification>().Find(id);

            _dbContext.Set<NCRReportingAccountTypeClassification>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}
