using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using System;
using System.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Sacrra.Membership.Reporting.Util
{
    public class ReportUtil
    {
        public static int GetKpiTreshold(KpiThresholds[] kpiThreshholds, string measureName)
        {
            var field = kpiThreshholds.Result.FirstOrDefault(k => k.MeasurmentName == measureName);
            if (field == null)
                return 2;

            var result = (int)(field.ThresholdPerc * 100);
            return result;
        }

        public static bool GetKpiIsNCR(KpiThresholds[] kpiThreshholds, string measureName)
        {
            var field = kpiThreshholds.Result.FirstOrDefault(k => k.MeasurmentName == measureName);
            if (field == null)
                return false;

            var result = field.IsNCRQE == "YES";
            return result;
        }

        public static string GetBaseWhereClause(RejectionReportParameters parameters)
        {
            var whereClause = $"TranYear IN ({parameters.Year}) AND TranMonth IN ({parameters.Month})";

            if (parameters.StartDay != null && parameters.EndDay != null)
                whereClause += $" AND TranDay BETWEEN {parameters.StartDay} AND {parameters.EndDay}";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SpNumbers != null && parameters.SpNumbers.Count() > 0)
                whereClause += $" AND SPNumber IN (" + string.Join(",", parameters.SpNumbers.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.TotalRejPercentageStart != null && parameters.TotalRejPercentageEnd != null)
                whereClause += $" AND TotalRejectedPercentage BETWEEN {parameters.TotalRejPercentageStart} AND {parameters.TotalRejPercentageEnd}";

            if (parameters.TotalRejPercentageStart != null && parameters.TotalRejPercentageEnd == null)
                whereClause += $" AND TotalRejectedPercentage IN ({parameters.TotalRejPercentageStart})";

            if (parameters.TotalRejPercentageStart == null && parameters.TotalRejPercentageEnd != null)
                whereClause += $" AND TotalRejectedPercentage IN ({parameters.TotalRejPercentageEnd})";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            return whereClause;
        }

        /// We have the mod because I cannot filter on TotalRejectedPercentage because this column doesn't exist, so we do the filtering in the code
        /// layer and not the database layer in this case
        public static string GetBaseWhereClauseMod(RejectionReportParameters parameters)
        {
            var whereClause = $"TranYear IN ({parameters.Year}) AND TranMonth IN ({parameters.Month})";

            if (parameters.StartDay != null && parameters.EndDay != null)
                whereClause += $" AND TranDay BETWEEN {parameters.StartDay} AND {parameters.EndDay}";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SpNumbers != null && parameters.SpNumbers.Count() > 0)
                whereClause += $" AND SPNumber IN (" + string.Join(",", parameters.SpNumbers.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            return whereClause;
        }

        public static string GetBaseWhereIncludingLast3Months(RejectionReportParameters parameters)
        {
            // get start and end dates
            var initialDate = new DateTime(parameters.Year, parameters.Month, 1);
            var startDate = initialDate.AddMonths(-2);
            var endDate = initialDate.AddMonths(1).AddDays(-1);
            var startDateString = startDate.ToString("yyyyMM");
            var endDateString = endDate.ToString("yyyyMM");
            var whereClause = $"CAST(CAST(TranYear AS VARCHAR(4)) + RIGHT('0' + CAST(TranMonth AS VARCHAR(2)),2) AS INT) BETWEEN {startDateString} AND {endDateString}";

            if (parameters.StartDay != null && parameters.EndDay != null)
                whereClause += $" AND TranDay BETWEEN {parameters.StartDay} AND {parameters.EndDay}";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SpNumbers != null && parameters.SpNumbers.Count() > 0)
                whereClause += $" AND SPNumber IN (" + string.Join(",", parameters.SpNumbers.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.TotalRejPercentageStart != null && parameters.TotalRejPercentageEnd != null)
                whereClause += $" AND TotalRejectedPercentage BETWEEN {parameters.TotalRejPercentageStart} AND {parameters.TotalRejPercentageEnd}";

            if (parameters.TotalRejPercentageStart != null && parameters.TotalRejPercentageEnd == null)
                whereClause += $" AND TotalRejectedPercentage IN ({parameters.TotalRejPercentageStart})";

            if (parameters.TotalRejPercentageStart == null && parameters.TotalRejPercentageEnd != null)
                whereClause += $" AND TotalRejectedPercentage IN ({parameters.TotalRejPercentageEnd})";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            return whereClause;
        }

        public static string GetBaseOutOfSLAWhereClause(RejectionReportParameters parameters)
        {
            var whereClause = $"YearMonth IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.StartDay != null && parameters.EndDay != null)
                whereClause += $" AND FileDay BETWEEN {parameters.StartDay} AND {parameters.EndDay}";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SpNumbers != null && parameters.SpNumbers.Count() > 0)
                whereClause += $" AND SPNumber IN (" + string.Join(",", parameters.SpNumbers.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            return whereClause;
        }
        public static string GetDataWarehouseWhereClause(string[] srnNumbers, RejectionReportParameters parameters)
        {
             var whereClause = $"TranYear IN ({parameters.Year}) AND TranMonth IN ({parameters.Month})";

            if (parameters.StartDay != null && parameters.EndDay != null)
                whereClause += $" AND TranDay BETWEEN {parameters.StartDay} AND {parameters.EndDay}";

            if (parameters.MemberName != null && parameters.MemberName.Result.Any())
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => $"'{m.Replace("'", "''")}'")) + ")";

            if (parameters.TotalRejPercentageStart != null && parameters.TotalRejPercentageEnd != null)
                whereClause += $" AND TotalRejectedPercentage BETWEEN {parameters.TotalRejPercentageStart} AND {parameters.TotalRejPercentageEnd}";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Result.Any())
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => $"'{m.Replace("'", "''")}'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Result.Any())
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Result.Any())
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SpNumbers != null && parameters.SpNumbers.Result.Any())
                whereClause += $" AND SPNumber IN (" + string.Join(",", parameters.SpNumbers.Result.Select(m => "'" + m + "'")) + ")";
            return whereClause;
        }

        public static string GetDataWarehouseOutOfSLAWhereClause(string[] srnNumbers, RejectionReportParameters parameters)
        {
            var whereClause = $"YearMonth IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.StartDay != null && parameters.EndDay != null)
                whereClause += $" AND FileDay BETWEEN {parameters.StartDay} AND {parameters.EndDay}";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";
           
            return whereClause;
        }

        public static string GetDataWarehouseWhereClauseMod(string[] srnNumbers, RejectionReportParameters parameters)
        {
            var whereClause = $"TranYear IN ({parameters.Year}) AND TranMonth IN ({parameters.Month})";
            if (parameters.StartDay != null && parameters.EndDay != null)
                whereClause += $" AND TranDay BETWEEN {parameters.StartDay} AND {parameters.EndDay}";
            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";
            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m + "'")) + ")";
            if (srnNumbers != null && srnNumbers.Length > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", srnNumbers.Result.Select(m => "'" + m + "'")) + ")";
            if (parameters.SpNumbers != null && parameters.SpNumbers.Count() > 0)
                whereClause += $" AND SPNumber IN (" + string.Join(",", parameters.SpNumbers.Result.Select(m => "'" + m + "'")) + ")";
            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";
            return whereClause;
        }
    }
}
