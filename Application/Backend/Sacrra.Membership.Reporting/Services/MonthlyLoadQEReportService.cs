using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Util;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Sacrra.Membership.Reporting.Services
{
    public class MonthlyLoadQEReportService
    {
        List<int> QE_LIST = new List<int>()
        {
            2,3 ,4 ,5 ,6 ,7 ,8 ,
            9 ,10,11,12,13,14,15,
            16,17,18,19,20,21,22
        };
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private ReportCommonService _reportCommonService;
        public MonthlyLoadQEReportService(DataWarehouseService dataWarehouseService, ReportCommonService reportCommonService)
        {
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _reportCommonService = reportCommonService;
        }

        public CardCollectionModel GetMonthlySrnExceptions(RejectionReportParameters parameters)
        {
            // Build column list clause
            string columnClause = "";
            columnClause += "BureauName";
            columnClause += ", RECORDSRECEIVED = SUM(RECORDSRECEIVED)";
            foreach (int qeNumber in QE_LIST)
            {
                columnClause += $", [QE {qeNumber}] = CASE WHEN SUM(RECORDSRECEIVED) <> 0 THEN SUM([#QE{qeNumber}])/SUM(RECORDSRECEIVED)*100 ELSE 0 END";
                columnClause += $", [QE {qeNumber} Total] = SUM([#QE{qeNumber}])";
            }
            // Get filtered SRNs from Connect DB
            var filteredSRNs = _reportCommonService.GetFilteredSRNs(parameters);

            // Build where clause using filtered SRNs
            var whereClause = ReportUtil.GetDataWarehouseWhereClause(filteredSRNs, parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                OrderBy = "BureauName",
                GroupBy = "BureauName",
                Where = whereClause
            };
            var srnPerBureaus = _dataWarehouseService.GetResultJsonArray(_reportTables.MonthlyLoadQEView, apiCallModel);

            // Get thesholds
            apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "MeasurmentName,ThresholdPerc,IsNCRQE"
            };
            var kpiThreshholds = _dataWarehouseService.GetResultArray<KpiThresholds>(_reportTables.DimMeasurementView, apiCallModel);

            var model = new CardCollectionModel()
            {
                ChildCardCollection = new List<CardCollectionModel>()
                {
                   new CardCollectionModel() { Title="COMPUSCAN" },
                   new CardCollectionModel() { Title="CPB" },
                   new CardCollectionModel() { Title="EXPERIAN" },
                   new CardCollectionModel() { Title="TRANSUNION" },
                   new CardCollectionModel() { Title="VCCB" },
                   new CardCollectionModel() { Title="XDS" },
                   new CardCollectionModel() { Title="ITC" },
                }
            };

            foreach (var srnPerBuruau in srnPerBureaus)
            {
                string bureauName = srnPerBuruau["BureauName"].ToObject<string>();

                CardCollectionModel bureauCardCollection = model.ChildCardCollection.First(c => c.Title == bureauName);
                var tableCardChildCollectionNCR = new CardCollectionModel() { Title = "NCR", CssClass = "NCR" };
                var tableCardChildCollectionNonNCR = new CardCollectionModel() { Title = "NON-NCR", CssClass = "NON-NCR" };
                bureauCardCollection.ChildCardCollection.Add(tableCardChildCollectionNCR);
                bureauCardCollection.ChildCardCollection.Add(tableCardChildCollectionNonNCR);

                var totalRecordsReceived = srnPerBuruau["RECORDSRECEIVED"].ToObject<long>();
                double maxQualityExceptionPercentage = 0;

                foreach (var qeNumber in QE_LIST)
                {
                    var cardName = "QE " + qeNumber;
                    string cardMeasureName = "#QE" + qeNumber;
                    string cardPercentageMeasure = "QE " + qeNumber;
                    string cardTotalMeasure = "QE " + qeNumber + " Total";

                    var dataQualityExceptionPercentage = Math.Round(srnPerBuruau[cardPercentageMeasure].ToObject<double>(), 3);
                    maxQualityExceptionPercentage = Math.Max(maxQualityExceptionPercentage, dataQualityExceptionPercentage);

                    var threshold = ReportUtil.GetKpiTreshold(kpiThreshholds, cardMeasureName);
                    bool isNCR = ReportUtil.GetKpiIsNCR(kpiThreshholds, cardMeasureName);
                    bool hasError = dataQualityExceptionPercentage >= threshold;

                    var tableCardModel = new CardModel()
                    {
                        Title = cardName,
                        Value = dataQualityExceptionPercentage,
                        Type = CardModelType.Percentage,
                        HasError = hasError,
                        Total = srnPerBuruau[cardTotalMeasure].ToObject<long>(),
                        Description = QEList.GetQEDescriptions()[qeNumber]
                    };

                    if (isNCR)
                    {
                        tableCardChildCollectionNCR.Cards.Add(tableCardModel);
                    }
                    else
                    {
                        tableCardChildCollectionNonNCR.Cards.Add(tableCardModel);
                    }
                }

                bureauCardCollection.Subtitle = $"(Total Records Received: {totalRecordsReceived:N0}; Max QE %: {maxQualityExceptionPercentage} %)";
            }

            return model;
        }

        public SrnExceptionDetails[] GetMonthlySrnExceptionDetails(RejectionReportParameters parameters)
        {
            // Build column list clause
            string columnClause = "SRNNumber,BureauName,MemberCompanyName,SPNumber,TransactionDate";
            columnClause += ",TotalReceived = SUM(RECORDSRECEIVED)";
            foreach (int i in QE_LIST)
            {
                columnClause += $", qe{i} = SUM([#QE{i}])";
                columnClause += $", qe{i}Percentage = CASE WHEN SUM(RECORDSRECEIVED) <> 0 THEN SUM([#QE{i}])/SUM(RECORDSRECEIVED)*100 ELSE 0 END";
            }
            // Get filtered SRNs from Connect DB
            var filteredSRNs = _reportCommonService.GetFilteredSRNs(parameters);

            // Build where clause using filtered SRNs
            var whereClause = ReportUtil.GetDataWarehouseWhereClauseMod(filteredSRNs, parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                Where = whereClause,
                GroupBy = "SRNNumber,BureauName,MemberCompanyName,SPNumber,TransactionDate"
            };
            var results = _dataWarehouseService.GetResultArray<SrnExceptionDetails>(_reportTables.MonthlyLoadQEView, apiCallModel);

            foreach (var result in results)
            {
                double maxPercentage = 0;
                double maxTotal = 0;

                var json = JObject.Parse(JsonConvert.SerializeObject(result));
                foreach (int i in QE_LIST)
                {
                    var columnName = $"qe{i}Percentage";
                    var currentValue = json[columnName].ToObject<double>();
                    maxPercentage = Math.Max(maxPercentage, currentValue);

                    var qeColumnName = $"qe{i}";
                    var totalCurrentValue = json[qeColumnName].ToObject<double>();
                    maxTotal = Math.Max(maxTotal, totalCurrentValue);
                }
                result.TotalRejectedPercentage = maxPercentage;
                result.TotalRejected = maxTotal;
            }

            // filter out the total rejected percentage
            results = results.Result.Where(p => parameters.TotalRejPercentageStart <= p.TotalRejectedPercentage && p.TotalRejectedPercentage <= parameters.TotalRejPercentageEnd).ToArray();

            // Order by TotalDataQualityRejectedPercentage DESC, TransactionDate DESC
            results = results.OrderByDescending(r => r.TotalRejectedPercentage).ThenByDescending(r => r.TransactionDate).ToArray();

            return results;
        }

        public QEItem[] GetQEList()
        {
            // Get QE Items
            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "MeasurmentName,ThresholdPerc,IsNCRQE",
            };
            var qeDatabaseResult = _dataWarehouseService.GetResultArray<KpiThresholds>(_reportTables.DimMeasurementView, apiCallModel);

            var result = new List<QEItem>();
            foreach (var i in QE_LIST)
            {
                string qeName = "#QE" + i;
                var item = qeDatabaseResult.First(q => q.MeasurmentName == qeName);
                bool isNCR = item.IsNCRQE == "YES";
                result.Add(new QEItem() { ID = i, IsNCR = isNCR });
            }

            return result.ToArray();
        }
    }
}
