using AutoMapper;
using Azure;
using Microsoft.Data.SqlClient.Server;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.DTOs.OSLAReasonDTOs;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models.OutOfSLAReportModels;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using System;
using System.Collections.Generic;
using System.Linq;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services.DataWarehouseService;

namespace Sacrra.Membership.Reporting.Services
{
    public class OSLAManagementService
    {
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public OSLAManagementService(DataWarehouseService dataWarehouseService, AppDbContext appDbContext, IMapper mapper)
        {
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _dbContext = appDbContext;
            _mapper = mapper;
        }

        public OutOfSlaItem[] GetOSLASrns()
        {
            var columnClause = "FileDate,BureauName,SRNNumber,IsSRNLoaded," +
                "IsSRNOutOfSLA,IsPending,TotalDaysOutOfSLA,DTHPushDate," +
                "DTHPullDate, MonthlyFileLoadDate, ConsMonthsOSLA, MonthsPerYearOSLA," +
                "SPNumber, OSLAReason, FctDTHExceptionsID, MemberTradingName, DimDTHReportType, SHM";
            var whereClause = $"IsSRNOutOfSLA = 'YES' AND DimDTHReportType = 'Bureau Analysis'";
            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                Where = whereClause
            };

            var resultList = _dataWarehouseService.GetResultArray<OutOfSlaItem>(_reportTables.MonthlyOutOfSlaView, apiCallModel);

            return resultList;
        }

        public void AddOSLAReason(OSLAReasonInputDTO oslaReasonInputDTO)
        {
            var srn = _dbContext.SRNs.Result.Where(x => x.SRNNumber == oslaReasonInputDTO.SRNNumber)
                .Result.Select(m => new SRN { Id = m.Id })
                .Result.FirstOrDefault();

            if (srn != null)
            {
                oslaReasonInputDTO.SRNId = srn.Id;

                var model = _mapper.Map<SRNMonthlyOSLAReason>(oslaReasonInputDTO);

                model.CreatedAt = DateTime.Now;
                _dbContext.SRNMonthlyOSLAReasons.Add(model);
                _dbContext.SaveChanges();
            }
        }

        public OSLASRNSyncStatus GetOSLASRNStatus(long fctDTHExceptionsID)
        {
            var oslaReason = _dbContext.SRNMonthlyOSLAReasons.Result.FirstOrDefault(x => x.FctDTHExceptionsID == fctDTHExceptionsID);

            if (oslaReason != null)
            {
                var warehouseOSLAReason = this.GetOSLASrns().Result.FirstOrDefault(x => x.FctDTHExceptionsID == fctDTHExceptionsID).OSLAReason;

                if (warehouseOSLAReason == "-")
                {
                    return OSLASRNSyncStatus.NotSynched;
                }

                return OSLASRNSyncStatus.Synched;
            }

            return OSLASRNSyncStatus.Empty;
        }

        public MonthlyOSLAReason GetOSLASrnReason(long fctDTHExceptionsID)
        {
            var oslaReasonId = _dbContext.SRNMonthlyOSLAReasons.Result.FirstOrDefault(x => x.FctDTHExceptionsID == fctDTHExceptionsID).MonthlyOSLAReasonId;
            var oslaReason = _dbContext.MonthlyOSLAReasons.Result.FirstOrDefault(x => x.Id == oslaReasonId);

            return oslaReason;
        }

        public void EditOSLAReason(OSLAReasonInputDTO oslaReasonInputDTO)
        {
            var srn = _dbContext.SRNs.Result.Where(x => x.SRNNumber == oslaReasonInputDTO.SRNNumber)
                .Result.Select(m => new SRN { Id = m.Id })
                .Result.FirstOrDefault();

            if (srn != null)
            {
                oslaReasonInputDTO.SRNId = srn.Id;

                var model = _dbContext.SRNMonthlyOSLAReasons.Result.FirstOrDefault(x => x.FctDTHExceptionsID == oslaReasonInputDTO.FctDTHExceptionsID);

                model.FileDate = oslaReasonInputDTO.FileDate;
                model.MonthlyOSLAReasonId = oslaReasonInputDTO.MonthlyOSLAReasonId;

                _dbContext.SRNMonthlyOSLAReasons.Update(model);
                _dbContext.SaveChanges();
            }
        }
    }
}
