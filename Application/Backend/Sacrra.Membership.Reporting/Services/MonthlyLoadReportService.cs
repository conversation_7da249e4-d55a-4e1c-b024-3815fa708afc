using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.MonthlyLoadReportModels;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services.DataWarehouseService;

namespace Sacrra.Membership.Reporting.Services
{
    public class MonthlyLoadReportService
    {
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private ReportCommonService _reportCommonService;

        public MonthlyLoadReportService(DataWarehouseService dataWarehouseService, ReportCommonService reportCommonService)
        {
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _reportCommonService = reportCommonService;
        }

        public CardCollectionModel GetKpiCards(RejectionReportParameters parameters)
        {
            // Build column list clause
            string columnClause = "";
            columnClause += "BureauName";
            columnClause += ", TotalDataQualityRejectedPercentage = CASE WHEN SUM(TotalReceived) <> 0 THEN SUM(DqRecordsRejected)/SUM(TotalReceived)*100 ELSE 0 END";
            columnClause += ", TotalRecords = CAST(SUM(TotalReceived) AS BIGINT)";
            columnClause += ", TotalDataQualityRejected = CAST(SUM(DqRecordsRejected) AS BIGINT)";

            // Build where clause
            var whereClause = ReportUtil.GetDataWarehouseWhereClause(parameters.SrnNumber, parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                OrderBy = "BureauName",
                GroupBy = "BureauName",
                Where = whereClause
            };
            var kpisPerBureau = _dataWarehouseService.GetResultArray<MonthlyKpiCardDetails>(_reportTables.MonthlyLoadView, apiCallModel);

            // Get thesholds
            apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "MeasurmentName,ThresholdPerc",
                Where = "MeasurmentName IN ('DQREJECTEDPERC')"
            };
            var kpiThreshholds = _dataWarehouseService.GetResultArray<KpiThresholds>(_reportTables.DimMeasurementView, apiCallModel);

            var dataQualityRejectionThreshold = ReportUtil.GetKpiTreshold(kpiThreshholds, "DQREJECTEDPERC");

            // Build the card collection
            var cardModel = new CardCollectionModel()
            {
                Title = "Bureau Key Statistics",
            };

            // Add each of the cards programatically
            long totalRecordsRunning = 0;
            long totalRejectedRunning = 0;
            foreach (var kpiPerBureau in kpisPerBureau)
            {

                var cardCollection = new CardCollectionModel();
                cardCollection.Title = kpiPerBureau.BureauName;
                cardCollection.Cards.Add(new CardModel()
                {
                    Title = "Total Data Quality Rejected %",
                    Type = CardModelType.Percentage,
                    Value = Math.Round(kpiPerBureau.TotalDataQualityRejectedPercentage, 3),
                    HasError = kpiPerBureau.TotalDataQualityRejectedPercentage > dataQualityRejectionThreshold
                });
                cardCollection.Cards.Add(new CardModel()
                {
                    Title = "Total Records",
                    Type = CardModelType.Number,
                    Value = kpiPerBureau.TotalRecords
                });
                cardCollection.Cards.Add(new CardModel()
                {
                    Title = "Total Data Quality Rejected",
                    Type = CardModelType.Number,
                    Value = kpiPerBureau.TotalDataQualityRejected
                });

                totalRecordsRunning += kpiPerBureau.TotalRecords;
                totalRejectedRunning += kpiPerBureau.TotalDataQualityRejected;
                var RejectedPercentString = Math.Round(kpiPerBureau.TotalDataQualityRejectedPercentage, 3).ToString() + "%";
                cardCollection.Subtitle = $"Total Records: {kpiPerBureau.TotalRecords:N0} | Total Data Quality Rejected %: {RejectedPercentString}";

                // Check if we need to highlight any errors
                bool hasCardError = false;
                foreach (var card in cardCollection.Cards)
                {
                    if (card.HasError != null && card.HasError.Value == true)
                    {
                        hasCardError = true;
                        break;
                    }
                }
                cardCollection.HasError = hasCardError;

                cardModel.ChildCardCollection.Add(cardCollection);
            }

            // Check if we have issues in any of the cards
            bool hasCollectionError = false;
            foreach (var cardCollection in cardModel.ChildCardCollection)
            {
                if (cardCollection.HasError)
                {
                    hasCollectionError = true;
                    break;
                }
            }
            cardModel.HasError = hasCollectionError;

            var totalRejected = totalRejectedRunning.ToString();
            cardModel.Subtitle = $"Total Records: {totalRecordsRunning:N0} | Total Rejected: {totalRejected}";

            return cardModel;
        }

        public BarChart PercentTotalDQRejectionsByDayChart(RejectionReportParameters parameters)
        {
            // Build where clause
            string whereClause = ReportUtil.GetBaseWhereClause(parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,TranDay,TotalRejectedPercentage = CASE WHEN SUM(TotalReceived) <> 0 THEN SUM(DqRecordsRejected)/SUM(TotalReceived)*100 ELSE 0 END",
                Where = whereClause,
                OrderBy = "BureauName,TranDay",
                GroupBy = "BureauName,TranDay",
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.MonthlyLoadView, apiCallModel);

            // Get data in a dictionary format
            var dataDictionary = new Dictionary<string, Dictionary<int, double>>();
            foreach (var arrayRow in resultArray)
            {
                string bureauName = arrayRow["BureauName"].ToObject<string>();
                var tranDay = arrayRow["TranDay"].ToObject<int>();
                var totalRejectionsPerc = arrayRow["TotalRejectedPercentage"].ToObject<double>();
                totalRejectionsPerc = Math.Round(totalRejectionsPerc, 3);
                if (totalRejectionsPerc != 0)
                {
                    if (!dataDictionary.ContainsKey(bureauName))
                        dataDictionary[bureauName] = new Dictionary<int, double>();
                    dataDictionary[bureauName][tranDay] = totalRejectionsPerc;
                }
            }

            int[] dayLabels = dataDictionary.SelectMany(k => k.Value.Keys).Distinct().OrderBy(k => k).ToArray();

            // Format data as a line chart
            var lineChart = new BarChart();

            foreach (int i in dayLabels)
            {
                lineChart.ChartLabels.Add(i.ToString());
            }

            foreach (string bureauName in dataDictionary.Keys)
            {
                var lineDataSet = new LineChartDataset();
                lineDataSet.label = bureauName;

                var bureauData = dataDictionary[bureauName];
                foreach (int i in dayLabels)
                {
                    double y = (bureauData.ContainsKey(i)) ? bureauData[i] : 0;
                    lineDataSet.data.Add(y);
                }

                lineChart.ChartData.Add(lineDataSet);
            }

            lineChart.ChartName = "% Total Data Quality Rejections by Day \\ Line Per Bureau";
            return lineChart;
        }

        public BarChart TotalRecordsSubmittedLast3MonthsChart(RejectionReportParameters parameters)
        {
            var whereClause = ReportUtil.GetBaseWhereIncludingLast3Months(parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,TranYear,TranMonth,TotalReceived = SUM(TotalReceived)",
                Where = whereClause,
                OrderBy = "BureauName,TranYear,TranMonth",
                GroupBy = "BureauName,TranYear,TranMonth",
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.MonthlyLoadView, apiCallModel);

            // Get data in a dictionary format
            var dataDictionary = new Dictionary<string, Dictionary<string, double>>();
            foreach (var arrayRow in resultArray)
            {
                string bureauName = arrayRow["BureauName"].ToObject<string>();
                var fullDate = new DateTime(arrayRow["TranYear"].ToObject<int>(), arrayRow["TranMonth"].ToObject<int>(), 1);
                string yearMonthString = fullDate.ToString("MMMM yyyy");
                var totalReceived = arrayRow["TotalReceived"].ToObject<double>();
                if (!dataDictionary.ContainsKey(yearMonthString))
                    dataDictionary[yearMonthString] = new Dictionary<string, double>();
                dataDictionary[yearMonthString][bureauName] = totalReceived;
            }

            // Put the data in a structure which is ready for use with the UI
            var barChart = new BarChart();
            barChart.ChartLabels = dataDictionary.Values.SelectMany(v => v.Keys).Distinct().OrderBy(v => v).ToList();
            foreach (string yearMonthString in dataDictionary.Keys)
            {
                var lineDataSet = new LineChartDataset();
                lineDataSet.label = yearMonthString;

                var bureauData = dataDictionary[yearMonthString];
                foreach (string label in barChart.ChartLabels)
                {
                    double y = (bureauData.ContainsKey(label)) ? bureauData[label] : 0;
                    lineDataSet.data.Add(y);
                }
                barChart.ChartData.Add(lineDataSet);
            }

            barChart.ChartName = "Total Records Submitted Last 3 Months";
            return barChart;
        }

        public BarChart TotalRejectedVsPreviousMonthsChart(RejectionReportParameters parameters)
        {
            // get start and end dates
            var initialDate = new DateTime(parameters.Year, parameters.Month, 1);
            var startDate = initialDate.AddMonths(-2);
            var endDate = initialDate.AddMonths(1).AddDays(-1);
            string startDateString = startDate.ToString("yyyyMM");
            string endDateString = endDate.ToString("yyyyMM");

            // Build where clause
            string whereClause = ReportUtil.GetBaseWhereIncludingLast3Months(parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,TranYear,TranMonth,TotalRejectedPercentage = CASE WHEN SUM(TotalReceived) <> 0 THEN SUM(DqRecordsRejected)/SUM(TotalReceived)*100 ELSE 0 END",
                Where = whereClause,
                OrderBy = "BureauName,TranYear,TranMonth",
                GroupBy = "BureauName,TranYear,TranMonth",
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.MonthlyLoadView, apiCallModel);

            // Get data in a dictionary format
            var dataDictionary = new Dictionary<string, Dictionary<string, double>>();
            foreach (var arrayRow in resultArray)
            {
                string bureauName = arrayRow["BureauName"].ToObject<string>();
                var fullDate = new DateTime(arrayRow["TranYear"].ToObject<int>(), arrayRow["TranMonth"].ToObject<int>(), 1);
                string yearMonthString = fullDate.ToString("MMMM yyyy");
                var totalRejectedPercentage = arrayRow["TotalRejectedPercentage"].ToObject<double>();
                totalRejectedPercentage = Math.Round(totalRejectedPercentage, 3);
                if (!dataDictionary.ContainsKey(yearMonthString))
                    dataDictionary[yearMonthString] = new Dictionary<string, double>();
                dataDictionary[yearMonthString][bureauName] = totalRejectedPercentage;
            }

            // Put the data in a structure which is ready for use with the UI
            var barChart = new BarChart();
            barChart.ChartLabels = dataDictionary.Values.SelectMany(v => v.Keys).Distinct().OrderBy(v => v).ToList();
            foreach (string yearMonthString in dataDictionary.Keys)
            {
                var lineDataSet = new LineChartDataset();
                lineDataSet.label = yearMonthString;

                var bureauData = dataDictionary[yearMonthString];
                foreach (string label in barChart.ChartLabels)
                {
                    double y = (bureauData.ContainsKey(label)) ? bureauData[label] : 0;
                    lineDataSet.data.Add(y);
                }
                barChart.ChartData.Add(lineDataSet);
            }

            barChart.ChartName = "Total Data Quality Rejected % VS previous months";
            return barChart;
        }

        public BarChart SrnRejectionsChart(RejectionReportParameters parameters)
        {
            // Build where clause
            string whereClause = ReportUtil.GetBaseWhereClause(parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "SRNNumber, TotalRejectedPercentage = CASE WHEN SUM(TotalReceived) <> 0 THEN SUM(DqRecordsRejected)/SUM(TotalReceived)*100 ELSE 0 END",
                Where = whereClause,
                OrderBy = "TotalRejectedPercentage DESC",
                GroupBy = "SRNNumber",
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.MonthlyLoadView, apiCallModel);

            // Format data as a bar chart
            var barChart = new BarChart();

            if (resultArray.Result.Count == 0)
                return barChart;

            barChart.ChartData.Add(new LineChartDataset() { label = "" });
            foreach (var arrayRow in resultArray)
            {
                string label = arrayRow["SRNNumber"].ToObject<string>();
                var yAxisTotalRejectedPercentage = arrayRow["TotalRejectedPercentage"].ToObject<double>();
                yAxisTotalRejectedPercentage = Math.Round(yAxisTotalRejectedPercentage, 3);
                barChart.ChartLabels.Add(label);
                barChart.ChartData.Result[0].data.Add(yAxisTotalRejectedPercentage);
            }

            barChart.ChartName = "Total Data Quality Rejected % By SRN (Descending Order)";
            return barChart;
        }

        public BarChart SrnRejectionsByBureauChart(RejectionReportParameters parameters)
        {
            // Build where clause
            string whereClause = ReportUtil.GetBaseWhereClause(parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "SRNNumber, BureauName, DqRecordsRejected = SUM(DqRecordsRejected)",
                Where = whereClause,
                OrderBy = "DqRecordsRejected DESC",
                GroupBy = "SRNNumber, BureauName",
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.MonthlyLoadView, apiCallModel);

            // Format data as a bar chart
            var barChart = new BarChart();

            if (resultArray.Result.Count == 0)
                return barChart;

            // Get data in a dictionary format
            var srnNumberList = new List<string>();
            var dataDictionary = new Dictionary<string, Dictionary<string, double>>();
            foreach (var arrayRow in resultArray)
            {
                string bureauName = arrayRow["BureauName"].ToObject<string>();
                string srnNumber = arrayRow["SRNNumber"].ToObject<string>();
                var totalDqRecordsRejected = arrayRow["DqRecordsRejected"].ToObject<int>();

                if (!dataDictionary.ContainsKey(bureauName))
                    dataDictionary[bureauName] = new Dictionary<string, double>();
                dataDictionary[bureauName][srnNumber] = totalDqRecordsRejected;

                // Get SRN numbers in DESC order
                if (!srnNumberList.Contains(srnNumber))
                {
                    srnNumberList.Add(srnNumber);
                }
            }

            barChart.ChartLabels.AddRange(srnNumberList);
            foreach (var bureauName in dataDictionary.Keys.OrderBy(k => k))
            {
                var dataset = new LineChartDataset();
                dataset.label = bureauName;

                foreach (var srnNumber in barChart.ChartLabels)
                {
                    double value = 0;
                    if (dataDictionary[bureauName].ContainsKey(srnNumber))
                        value = dataDictionary[bureauName][srnNumber];

                    dataset.data.Add(value);
                }
                barChart.ChartData.Add(dataset);
            }

            barChart.ChartName = "Total Data Quality Rejected By SRN By Bureau (Descending Order)";
            return barChart;
        }

        public MonthlySRNDetailsTableModel[] GetSRNDetailTableData(RejectionReportParameters parameters)
        {
          
            var filteredSRNs = _reportCommonService.GetFilteredSRNs(parameters);
            var whereClause = ReportUtil.GetDataWarehouseWhereClause(filteredSRNs, parameters);
            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "TransactionDate,BureauName,MemberCompanyName,SPNumber,SRNNumber,TotalReceived,TotalRejected,TotalRejectedPercentage,DqRecordsRejected",
                Where = whereClause,
                OrderBy = "TotalRejectedPercentage DESC, TransactionDate DESC"
            };
            var results = _dataWarehouseService.GetResultArray<MonthlySRNDetailsTableModel>(_reportTables.MonthlyLoadView, apiCallModel);

            return results;
        }
    }
}
