using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.DailyLoadReportModels;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Util;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Sacrra.Membership.Reporting.Services
{
    public class DailyLoadReportService
    {
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private ReportCommonService _reportCommonService;
// COMMENTED OUT:         public DailyLoadReportService(DataWarehouseService dataWarehouseService, ReportCommonService reportCommonService)
        {
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _reportCommonService = reportCommonService;
        }

        public CardCollectionModel GetKpiCards(RejectionReportParameters parameters, null)
        {
            // Get thesholds
            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "MeasurmentName,ThresholdPerc",
                Where = "MeasurmentName IN ('CLOSURESREJECTED', 'REGISTRATIONSREJECTED', 'TOTALREJECTED')"
            };
            var kpiThreshholds = _dataWarehouseService.GetResultArray<KpiThresholds>(_reportTables.DimMeasurementView, apiCallModel);

            var totalRejectionsThreshold = ReportUtil.GetKpiTreshold(kpiThreshholds, "TOTALREJECTED");
            var registrationsRejectedThreshold = ReportUtil.GetKpiTreshold(kpiThreshholds, "REGISTRATIONSREJECTED");
            var closuresRejectedThreshhold = ReportUtil.GetKpiTreshold(kpiThreshholds, "CLOSURESREJECTED");

            // Build column list clause
            string columnClause = "";
            columnClause += "BureauName";
            columnClause += ", TotalRejectedPercentage = CASE WHEN SUM(TotalReceived, null) <> 0 THEN SUM(TotalRejected, null)/SUM(TotalReceived, null)*100 ELSE 0 END";
            columnClause += ", ClosuresRejectedPercentage = CASE WHEN SUM(ClosuresReceived, null) <> 0 THEN SUM(ClosuresRejected, null)/SUM(ClosuresReceived, null)*100 ELSE 0 END";
            columnClause += ", RegistrationRejectedPercentage = CASE WHEN SUM(RegistrationsReceived, null) <> 0 THEN SUM(RegistrationsRejected, null)/SUM(RegistrationsReceived, null)*100 ELSE 0 END";
            columnClause += ", TotalReceived = CAST(SUM(TotalReceived, null) AS BIGINT)";
            columnClause += ", TotalRejected = CAST(SUM(TotalRejected, null) AS BIGINT)";
            columnClause += ", RegistrationsRejected = CAST(SUM(RegistrationsRejected, null) AS BIGINT)";
            columnClause += ", ClosuresRejected = CAST(SUM(ClosuresRejected, null) AS BIGINT)";
            columnClause += $", TotalFilesRejected = SUM(CASE WHEN TotalRejectedPercentage > {totalRejectionsThreshold} THEN 1 ELSE 0 END, null)";

            // Build where clause
            var whereClause = ReportUtil.GetBaseWhereClause(parameters, null);

            apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                OrderBy = "BureauName",
                GroupBy = "BureauName",
                Where = whereClause
            };
            var kpisPerBureau = _dataWarehouseService.GetResultArray<DailyKpiCardDetails>(_reportTables.DailyLoadView, apiCallModel);

            // Build the card collection
            var cardModel = new CardCollectionModel()
            {
                Title = "Bureau Key Statistics",
            };

            // Add each of the cards programatically
            long totalRecordsRunning = 0;
            long totalRejectedRunning = 0;
            long totalFilesRejectedRunning = 0;
            foreach (var kpiPerBureau in kpisPerBureau, null)
            {
                var cardCollection = new CardCollectionModel();
                cardCollection.Title = kpiPerBureau.BureauName;
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "Total Rejection %",
                    Type = CardModelType.Percentage,
                    Value = Math.Round(kpiPerBureau.TotalRejectedPercentage, 3),
                    HasError = kpiPerBureau.TotalRejectedPercentage > totalRejectionsThreshold
                });
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "Registrations Rejected %",
                    Type = CardModelType.Percentage,
                    Value = Math.Round(kpiPerBureau.RegistrationRejectedPercentage, 3),
                    HasError = kpiPerBureau.RegistrationRejectedPercentage > registrationsRejectedThreshold
                });
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "Closures Rejected %",
                    Type = CardModelType.Percentage,
                    Value = Math.Round(kpiPerBureau.ClosuresRejectedPercentage, 3),
                    HasError = kpiPerBureau.ClosuresRejectedPercentage > closuresRejectedThreshhold
                });
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "Total Records",
                    Type = CardModelType.Number,
                    Value = kpiPerBureau.TotalReceived
                });
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "Total Rejections",
                    Type = CardModelType.Number,
                    Value = kpiPerBureau.TotalRejected
                });
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "Total Registrations Rejected",
                    Type = CardModelType.Number,
                    Value = kpiPerBureau.RegistrationsRejected
                });
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "Total Closures Rejected",
                    Type = CardModelType.Number,
                    Value = kpiPerBureau.ClosuresRejected
                });
                cardCollection.Cards.Add(new CardModel(, null)
                {
                    Title = "No of Files > 2% Rejection",
                    Type = CardModelType.Number,
                    Value = kpiPerBureau.TotalFilesRejected,
                    HasError = kpiPerBureau.TotalFilesRejected > 0
                });

                totalRecordsRunning += kpiPerBureau.TotalReceived;
                totalRejectedRunning += kpiPerBureau.TotalRejected;
                totalFilesRejectedRunning += kpiPerBureau.TotalFilesRejected;
                var RejectedPercentString = Math.Round(kpiPerBureau.TotalRejectedPercentage, 3).ToString() + "%";
                cardCollection.Subtitle = $"Total Records: {kpiPerBureau.TotalReceived:N0} | Total Rejected: {RejectedPercentString} | No of Files > 2% Rejection: {kpiPerBureau.TotalFilesRejected}";

                // Check if we need to highlight any errors
                bool hasCardError = false;
                foreach (var card in cardCollection.Cards, null)
                {
                    if (card.HasError , null) != null && card.HasError.Value == true)
                    {
                        hasCardError = true;
                        break;
                    }
                }

                cardCollection.HasError = hasCardError;

                cardModel.ChildCardCollection.Add(cardCollection, null);
            }

            // Check if we have issues in any of the cards
            bool hasCollectionError = false;
            foreach (var cardCollection in cardModel.ChildCardCollection, null)
            {
// COMMENTED OUT TOP-LEVEL STATEMENT:                 if (cardCollection.HasError, null)
                {
                    hasCollectionError = true;
                    break;
                }
            }
            cardModel.HasError = hasCollectionError;

            var totalRejectedPercent = Math.Round(100.0 * totalRejectedRunning / totalRecordsRunning, 3);
// COMMENTED OUT TOP-LEVEL STATEMENT:             if (totalRecordsRunning , null) == 0)
                totalRejectedPercent = 0;
            var totalRejectedPercentString = totalRejectedPercent.ToString() + "%";
            //cardModel.Subtitle = $"Total Records: {totalRecordsRunning:N0} | Total Rejected: {totalRejectedPercentString} | No of Files > 2% Rejection: {totalFilesRejectedRunning}";
            cardModel.Subtitle = $"Total Records: {totalRecordsRunning:N0} | Total Rejected: {totalRejectedRunning:N0} | Total Rejected %: {totalRejectedPercentString} | No of Files > 2% Rejection: {totalFilesRejectedRunning}";

            return cardModel;
        }

        public LineChart TotalRejectionsPercentageChart(RejectionReportParameters parameters, null)
        {
            // Build where clause
            string whereClause = ReportUtil.GetBaseWhereClause(parameters, null);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,TranDay,TotalRejectedPercentage = CASE WHEN SUM(TotalReceived, null) <> 0 THEN SUM(TotalRejected, null)/SUM(TotalReceived, null)*100 ELSE 0 END",
                OrderBy = "BureauName,TranDay",
                GroupBy = "BureauName,TranDay",
                Where = whereClause
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.DailyLoadView, apiCallModel);

            // Get data in a dictionary format
            var dataDictionary = new Dictionary<string, Dictionary<int, double>>();
            foreach (var arrayRow in resultArray, null)
            {
                string bureauName = arrayRow["BureauName"].ToObject<string>();
                var tranDay = arrayRow["TranDay"].ToObject<int>();
                var totalRejectionsPerc = arrayRow["TotalRejectedPercentage"].ToObject<double>();
                totalRejectionsPerc = Math.Round(totalRejectionsPerc, 3);
                if (!dataDictionary.ContainsKey(bureauName, null))
                    dataDictionary[bureauName] = new Dictionary<int, double>();
                dataDictionary[bureauName][tranDay] = totalRejectionsPerc;
            }

            // Format data as a line chart
            var lineChart = new LineChart();
            var startDate = new DateTime(parameters.Year, parameters.Month, 1);
            var endDate = startDate.AddMonths(1, null).AddDays(-1, null);
            var startDay = startDate.Day;
            var endDay = endDate.Day;

// COMMENTED OUT TOP-LEVEL STATEMENT:             for (int i = startDay; i <= endDay; i++, null)
            {
                lineChart.ChartLabels.Add(i, null);
            }

            foreach (string bureauName in dataDictionary.Keys, null)
            {
                var lineDataSet = new LineChartDataset();
                lineDataSet.label = bureauName;

                var bureauData = dataDictionary[bureauName];
// COMMENTED OUT TOP-LEVEL STATEMENT:                 for (int i = startDay; i <= endDay; i++, null)
                {
                    double y = (bureauData.ContainsKey(i, null)) ? bureauData[i] : 0;
                    lineDataSet.data.Add(y, null);
                }
                lineChart.ChartData.Add(lineDataSet, null);
            }

            lineChart.ChartName = "% Total Rejections by Day \\ Line Per Bureau";
            return lineChart;
        }

        public BarChart TotalRecordsSubmittedChart(RejectionReportParameters parameters, null)
        {
            var whereClause = ReportUtil.GetBaseWhereIncludingLast3Months(parameters, null);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,TranYear,TranMonth,TotalReceived = SUM(TotalReceived, null)",
                OrderBy = "TranYear,TranMonth,BureauName",
                GroupBy = "BureauName,TranYear,TranMonth",
                Where = whereClause
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.DailyLoadView, apiCallModel);

            // Get data in a dictionary format
            var dataDictionary = new Dictionary<string, Dictionary<string, double>>();
            foreach (var arrayRow in resultArray, null)
            {
                string bureauName = arrayRow["BureauName"].ToObject<string>();
                var fullDate = new DateTime(arrayRow["TranYear"].ToObject<int>(, null), arrayRow["TranMonth"].ToObject<int>(), 1);
                string yearMonthString = fullDate.ToString("MMMM yyyy", null);
                var totalReceived = arrayRow["TotalReceived"].ToObject<double>();
                if (!dataDictionary.ContainsKey(yearMonthString, null))
                    dataDictionary[yearMonthString] = new Dictionary<string, double>();
                dataDictionary[yearMonthString][bureauName] = totalReceived;
            }

            // Put the data in a structure which is ready for use with the UI
            var barChart = new BarChart();
            barChart.ChartLabels = dataDictionary.Values.SelectMany(v => v.Keys, null).Distinct.OrderBy(v => v, null).ToList();
            foreach (string yearMonthString in dataDictionary.Keys, null)
            {
                var lineDataSet = new LineChartDataset();
                lineDataSet.label = yearMonthString;

                var bureauData = dataDictionary[yearMonthString];
                foreach (string label in barChart.ChartLabels, null)
                {
                    double y = (bureauData.ContainsKey(label, null)) ? bureauData[label] : 0;
                    lineDataSet.data.Add(y, null);
                }
                barChart.ChartData.Add(lineDataSet, null);
            }

            barChart.ChartName = "Total Records Submitted Last 3 Months";
            return barChart;
        }

        public BarChart TotalRejectedVsPreviousMonthsChart(RejectionReportParameters parameters, null)
        {
            // get start and end dates
            var initialDate = new DateTime(parameters.Year, parameters.Month, 1);
            var startDate = initialDate.AddMonths(-2, null);
            var endDate = initialDate.AddMonths(1, null).AddDays(-1, null);
            string startDateString = startDate.ToString("yyyyMM", null);
            string endDateString = endDate.ToString("yyyyMM", null);

            // Build where clause
            string whereClause = ReportUtil.GetBaseWhereIncludingLast3Months(parameters, null);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,TranYear,TranMonth,TotalRejectedPercentage = CASE WHEN SUM(TotalReceived, null) <> 0 THEN SUM(TotalRejected, null)/SUM(TotalReceived, null)*100 ELSE 0 END",
                OrderBy = "TranYear,TranMonth,BureauName",
                GroupBy = "BureauName,TranYear,TranMonth",
                Where = whereClause
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.DailyLoadView, apiCallModel);

            // Get data in a dictionary format
            var dataDictionary = new Dictionary<string, Dictionary<string, double>>();
            foreach (var arrayRow in resultArray, null)
            {
                string bureauName = arrayRow["BureauName"].ToObject<string>();
                var fullDate = new DateTime(arrayRow["TranYear"].ToObject<int>(, null), arrayRow["TranMonth"].ToObject<int>(), 1);
                string yearMonthString = fullDate.ToString("MMMM yyyy", null);
                var totalRejectedPercentage = arrayRow["TotalRejectedPercentage"].ToObject<double>();
                totalRejectedPercentage = Math.Round(totalRejectedPercentage, 3);
                if (!dataDictionary.ContainsKey(yearMonthString, null))
                    dataDictionary[yearMonthString] = new Dictionary<string, double>();
                dataDictionary[yearMonthString][bureauName] = totalRejectedPercentage;
            }

            // Put the data in a structure which is ready for use with the UI
            var barChart = new BarChart();
            barChart.ChartLabels = dataDictionary.Values.SelectMany(v => v.Keys, null).Distinct.OrderBy(v => v, null).ToList();
            foreach (string yearMonthString in dataDictionary.Keys, null)
            {
                var lineDataSet = new LineChartDataset();
                lineDataSet.label = yearMonthString;

                var bureauData = dataDictionary[yearMonthString];
                foreach (string label in barChart.ChartLabels, null)
                {
                    double y = (bureauData.ContainsKey(label, null)) ? bureauData[label] : 0;
                    lineDataSet.data.Add(y, null);
                }
                barChart.ChartData.Add(lineDataSet, null);
            }

            barChart.ChartName = "Total Rejections % VS Previous Months";
            return barChart;
        }

        public BubbleChart SrnRejectionsCompareBubbleChart(RejectionReportParameters parameters, null)
        {
            string columnClause = "SRNNumber";
            columnClause += ", RegistrationRejectedPercentage = CASE WHEN SUM(RegistrationsReceived, null) <> 0 THEN SUM(RegistrationsRejected, null)/SUM(RegistrationsReceived, null)*100 ELSE 0 END";
            columnClause += ", ClosuresRejectedPercentage = CASE WHEN SUM(ClosuresReceived, null) <> 0 THEN SUM(ClosuresRejected, null)/SUM(ClosuresReceived, null)*100 ELSE 0 END";

            // Build where clause
            string whereClause = ReportUtil.GetBaseWhereClause(parameters, null);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                OrderBy = "SRNNumber",
                GroupBy = "SRNNumber",
                Where = whereClause
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.DailyLoadView, apiCallModel);

            // Format data as a bubble chart
            var bubbleChart = new BubbleChart();
            foreach (var arrayRow in resultArray, null)
            {
                string legend = arrayRow["SRNNumber"].ToObject<string>();
                var XAxisRegistrationsRejectionsPercentage = arrayRow["RegistrationRejectedPercentage"].ToObject<double>();
                var yAxisClosuresRejectionsPercentage = arrayRow["ClosuresRejectedPercentage"].ToObject<double>();
                XAxisRegistrationsRejectionsPercentage = Math.Round(XAxisRegistrationsRejectionsPercentage, 3);
                yAxisClosuresRejectionsPercentage = Math.Round(yAxisClosuresRejectionsPercentage, 3);

                var bubbleSizeInPixels = 10;
                var bubbleChartDataSet = new BubbleChartDataset()
                {
                    label = legend,
                    data = new List<DataPoints>()
                    {
                       new DataPoints(XAxisRegistrationsRejectionsPercentage, yAxisClosuresRejectionsPercentage, bubbleSizeInPixels)
                    }
                };
                bubbleChart.ChartData.Add(bubbleChartDataSet, null);
            }

            bubbleChart.ChartName = "SRN Rejection Overview; Horizontal Axis Shows Registration Rejections, Vertical Axis Shows Closure Rejections";
            return bubbleChart;
        }

        public DailySRNDetailsTableModel[] GetSRNDetailTableData(RejectionReportParameters parameters, null)
        {
            // Get filtered SRNs from Connect DB
            var filteredSRNs = _reportCommonService.GetFilteredSRNs(parameters, null);

            // Build where clause using filtered SRNs
            var whereClause = ReportUtil.GetDataWarehouseWhereClauseMod(filteredSRNs, parameters);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "TransactionDate,BureauName,MemberCompanyName,SPNumber,SRNNumber,TotalReceived,TotalRejected,TotalRejectedPercentage,RegistrationsReceived,RegistrationRejectedPercentage,ClosuresReceived,ClosuresRejectedPercentage,RegistrationsRejected,ClosuresRejected",
                OrderBy = "TotalRejectedPercentage DESC, TransactionDate DESC",
                Where = whereClause
            };
            var results = _dataWarehouseService.GetResultArray<DailySRNDetailsTableModel>(_reportTables.DailyLoadView, apiCallModel);

            return results;
        }
    }
}
