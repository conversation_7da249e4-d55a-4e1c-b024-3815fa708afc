using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.OutOfSLAReportModels;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services.DataWarehouseService;

namespace Sacrra.Membership.Reporting.Services
{
    public class MonthlyOutOfSLAService
    {
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private const int DECIMAL_PLACES = 3;
        private ReportCommonService _reportCommonService;

        public MonthlyOutOfSLAService(DataWarehouseService dataWarehouseService, ReportCommonService reportCommonService)
        {
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _reportCommonService = reportCommonService;
        }

        public CardCollectionModel GetMemberSubmissionAnalysisKpiCards(RejectionReportParameters parameters)
        {
            var columns = @"
	             SRNsSubmitted = COUNT(DISTINCT CASE WHEN IsSRNSubmitted = 'YES' THEN SRNNumber ELSE NULL END)
	            ,SRNsNonSubmission = COUNT(DISTINCT CASE WHEN IsSRNSubmitted = 'NO' THEN SRNNumber ELSE NULL END)
	            ,SRNsOutOfSLA = COUNT(DISTINCT CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END)
	            ,AvgDaysOutOfSLA = ISNULL(AVG(CASE WHEN IsSRNOutOfSLA = 'YES' THEN TotalDaysOutOfSLA ELSE NULL END), 0)
            ";
            // Build where clause
            var whereClause = $"EndOfMonthDate IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += "AND DimDTHReportType = 'Submission Analysis'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columns,
                Where = whereClause
            };
            var memberLevelSubmissionAnalysis = _dataWarehouseService.GetResultArray<MemberLevelSubmissionAnalysis>(_reportTables.MonthlyOutOfSlaView, apiCallModel)[0];

            // SRN’s Out of SLA % - SRNs Out of SLA / Total SRNS Submitted.
            if (memberLevelSubmissionAnalysis.SRNsSubmitted > 0)
            {
                memberLevelSubmissionAnalysis.SRNsOutOfSLAPercentage = 100.0 * memberLevelSubmissionAnalysis.SRNsOutOfSLA / memberLevelSubmissionAnalysis.SRNsSubmitted;
                memberLevelSubmissionAnalysis.SRNsOutOfSLAPercentage = Math.Round(memberLevelSubmissionAnalysis.SRNsOutOfSLAPercentage, DECIMAL_PLACES);
            }

            // SRN Submission Success% - SRN Submitted  / (SRN non submission + SRN Submitted)
            if (memberLevelSubmissionAnalysis.SRNsNonSubmission + memberLevelSubmissionAnalysis.SRNsSubmitted > 0)
            {
                memberLevelSubmissionAnalysis.SRNsSubmissionSuccessPercentage = 100.0 * memberLevelSubmissionAnalysis.SRNsSubmitted / (memberLevelSubmissionAnalysis.SRNsNonSubmission + memberLevelSubmissionAnalysis.SRNsSubmitted);
                memberLevelSubmissionAnalysis.SRNsSubmissionSuccessPercentage = Math.Round(memberLevelSubmissionAnalysis.SRNsSubmissionSuccessPercentage, DECIMAL_PLACES);
            }

            // Member Submission Analysis
            var memberName = parameters.MemberName.Length > 1 ? "Multiple" : parameters.MemberName.Result[0];
            var cardCollectionModel = new CardCollectionModel()
            {
                Title = memberName + ": Submission Analysis",
                Cards = new List<CardModel>()
                {
                  new CardModel()
                     {
                        Title = "Total SRN's Submitted",
                        Value = memberLevelSubmissionAnalysis.SRNsSubmitted,
                        Type = CardModelType.Number
                     },

                  new CardModel()
                    {
                      Title = "SRN's Non Submission",
                      Value = memberLevelSubmissionAnalysis.SRNsNonSubmission,
                      HasError = memberLevelSubmissionAnalysis.SRNsNonSubmission > 0,
                      Type = CardModelType.Number
                    },

                  new CardModel()
                    {
                      Title = "SRNs Out Of SLA",
                      Value = memberLevelSubmissionAnalysis.SRNsOutOfSLA,
                      Type = CardModelType.Number,
                      HasError = memberLevelSubmissionAnalysis.SRNsOutOfSLA > 0
                    },

                  new CardModel()
                    {
                       Title = "SRN's Out of SLA %",
                       Value = memberLevelSubmissionAnalysis.SRNsOutOfSLAPercentage,
                       HasError = memberLevelSubmissionAnalysis.SRNsOutOfSLAPercentage > 0,
                       Type = CardModelType.Percentage
                    },

                  new CardModel()
                    {
                       Title = "SRN Submission Success %",
                       Value = memberLevelSubmissionAnalysis.SRNsSubmissionSuccessPercentage,
                       Type = CardModelType.Percentage
                    },
                }
            };

            foreach (var card in cardCollectionModel.Cards)
            {
                if (card.HasError ?? false == true)
                {
                    cardCollectionModel.HasError = true;
                }
            }

            cardCollectionModel.IsExpanded = false;

            return cardCollectionModel;
        }

        public CardCollectionModel GetMemberBureauAnalysisKpiCards(RejectionReportParameters parameters)
        {
            var columns = @"
              	 BureauName
	            ,SRNsSubmitted = COUNT(DISTINCT CASE WHEN IsSRNSubmitted = 'YES' THEN SRNNumber ELSE NULL END)
	            ,SrnsWithinSLA = COUNT(DISTINCT CASE WHEN IsSRNOutOfSLA = 'NO' THEN SRNNumber ELSE NULL END)
	            ,SrnsOutOfSLA = COUNT(DISTINCT CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END)
	            ,SrnsLoaded = COUNT(DISTINCT CASE WHEN IsSRNLoaded = 'YES' THEN SRNNumber ELSE NULL END)
	            ,SrnsNotLoaded = COUNT(DISTINCT CASE WHEN IsSRNLoaded = 'NO' THEN SRNNumber ELSE NULL END)
	            ,SrnsPendingLoad = COUNT(DISTINCT CASE WHEN IsPending = 'YES' THEN SRNNumber ELSE NULL END)  
            ";

            // Build where clause
            var whereClause = $"EndOfMonthDate IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += " AND DimDTHReportType = 'Bureau Analysis'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columns,
                Where = whereClause,
                GroupBy = "BureauName"
            };
            var outOutSlaItems = _dataWarehouseService.GetResultArray<MemberLevelBureauAnalysis>(_reportTables.MonthlyOutOfSlaView, apiCallModel);

            var memberName = parameters.MemberName.Length > 1 ? "Multiple" : parameters.MemberName.Result[0];

            var cardBureauAnalysis = new CardCollectionModel()
            {
                Title = memberName + ": Bureau Analysis | The bureau score is the combined percentage of out of SLA SRNs and SRNs not loaded",
            };

            // Get list of bureaus
            string[] bureauList = null;
            if (parameters.BureauName.Count() > 0)
            {
                bureauList = parameters.BureauName;
            }
            else
            {
                bureauList = outOutSlaItems.Result.Where(b => b.BureauName != null).Result.Select(o => o.BureauName).Distinct().OrderBy(b => b).ToArray();
            }

            // Member Bureau Analysis
            foreach (var bureauName in bureauList)
            {
                CardCollectionModel memberLevelBureauAnalysisCards = getMemberLevelBureauCards(outOutSlaItems, bureauName);
                cardBureauAnalysis.ChildCardCollection.Add(memberLevelBureauAnalysisCards);
            }

            // Highlight errors
            foreach (var collection in cardBureauAnalysis.ChildCardCollection)
            {
                foreach (var collectionCard in collection.Cards)
                {
                    if (collectionCard.HasError ?? false == true)
                    {
                        cardBureauAnalysis.HasError = true;
                    }
                }
            }

            return cardBureauAnalysis;
        }

        public CardCollectionModel GetSrnSubmissionAnalysisCards(RejectionReportParameters parameters)
        {
            if (parameters.SrnNumber == null || parameters.SrnNumber.Count() != 1)
                return new CardCollectionModel();

            var columns = @"
	             DaysPastSLA = MAX(TotalDaysOutOfSLA)
	            ,MonthsOutOfSLA = MAX(ConsMonthsOSLA)
	            ,YearsOutOfSLA = MAX(MonthsPerYearOSLA)
	            ,MonthsNoSubmit = MAX(ConsMonthsNoSubmit)
	            ,YearsNoSubmit = MAX(MonthsPerYearNoSubmit)
            ";

            // Build where clause
            var whereClause = "";
            whereClause += $"EndOfMonthDate IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columns,
                Where = whereClause
            };
            var outOfSlaItems = _dataWarehouseService.GetResultArray<SRNLevelSubmissionAnalysis>(_reportTables.MonthlyOutOfSlaView, apiCallModel);
            var srnLevelSubmissionAnalysis = new SRNLevelSubmissionAnalysis();

            if (outOfSlaItems.Count() > 0)
            {
                srnLevelSubmissionAnalysis = outOfSlaItems.Result[0];
            }

            // Member Submission Analysis
            var srnNumber = parameters.SrnNumber.Result[0];

            CardCollectionModel collection = new CardCollectionModel()
            {
                Title = $"{srnNumber}: Submission Analysis",
                Cards = new List<CardModel>()
                {
                    new CardModel()
                    {
                        Title = "Days Past SLA",
                        Value = srnLevelSubmissionAnalysis.DaysPastSLA ?? 0,
                        HasError = srnLevelSubmissionAnalysis.DaysPastSLA > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "Consecutive Months OSLA",
                        Value = srnLevelSubmissionAnalysis.MonthsOutOfSLA ?? 0,
                        HasError = srnLevelSubmissionAnalysis.MonthsOutOfSLA > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "OSLA Last 12 Months",
                        Value = srnLevelSubmissionAnalysis.YearsOutOfSLA ?? 0,
                        HasError = srnLevelSubmissionAnalysis.YearsOutOfSLA > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "Consecutive Months No Submit",
                        Value = srnLevelSubmissionAnalysis.MonthsNoSubmit ?? 0,
                        HasError = srnLevelSubmissionAnalysis.MonthsNoSubmit > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "No Submit Last 12 Months",
                        Value = srnLevelSubmissionAnalysis.YearsNoSubmit ?? 0,
                        HasError = srnLevelSubmissionAnalysis.YearsNoSubmit > 0,
                        Type = CardModelType.Number
                    }
                }
            };
            collection.IsExpanded = true;

            return collection;
        }

        public CardCollectionModel GetSrnBureauAnalysisCards(RejectionReportParameters parameters)
        {
            if (parameters.SrnNumber == null || parameters.SrnNumber.Count() != 1)
                return new CardCollectionModel();

            // Build where clause
            var whereClause = "";
            whereClause += $"EndOfMonthDate IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,ChartCalculationValue = MAX(TotalDaysOutOfSLA)",
                Where = whereClause,
                GroupBy = "BureauName"
            };
            var outOfSlaItems = _dataWarehouseService.GetResultArray<ChartDataModel>(_reportTables.MonthlyOutOfSlaView, apiCallModel);

            var srnNumber = parameters.SrnNumber.Result[0];

            CardCollectionModel collection = new CardCollectionModel()
            {
                Title = $"{srnNumber}: Bureau Analysis | The bureau score is the combined percentage of out of SLA SRNs and SRNs not loaded"
            };

            var bureaus = outOfSlaItems.Result.Where(b => b.BureauName != null).Result.Select(b => b.BureauName).Distinct().OrderBy(b => b);
            foreach (var bureau in bureaus)
            {
                var maxDaysOutOfSLA = outOfSlaItems.Result.Where(o => o.BureauName == bureau).Result.Select(o => o.ChartCalculationValue).Max();
                var hasError = maxDaysOutOfSLA > 0;

                var card = new CardModel()
                {
                    Title = $"{bureau} Days past SLA",
                    Value = maxDaysOutOfSLA,
                    HasError = hasError,
                    Type = CardModelType.Number
                };
                collection.Cards.Add(card);

                if (hasError)
                {
                    collection.HasError = true;
                }
            }

            return collection;
        }

        public BarChart GetMemberSrnNonSubmissionVsOutOfSlaChart(RejectionReportParameters parameters)
        {
            var columns = @"
                 FileYear
                ,FileMonth
                ,ChartCalculationValue = COUNT(DISTINCT CASE WHEN IsSRNSubmitted = 'NO' THEN SRNNumber ELSE NULL END)
                ,ChartCalculationValue2 = COUNT(DISTINCT CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END)
            ";

            // We need 3 months of data
            var monthStart = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(-2);
            var monthEnd = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(1).AddDays(-1);

            // Build where clause
            var whereClause = "DimDTHReportType = 'Submission Analysis'";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += $" AND FileDate BETWEEN '{monthStart:yyyy-MM-dd}' AND '{monthEnd:yyyy-MM-dd}'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columns,
                Where = whereClause,
                GroupBy = "FileYear,FileMonth"
            };
            var sourceData = _dataWarehouseService.GetResultArray<ChartDataModel>(_reportTables.MonthlyOutOfSlaView, apiCallModel);

            var chart = new BarChart()
            {
                ChartData = new List<LineChartDataset>()
                {
                    new LineChartDataset() { label = "SRNs Non Submissions" },
                    new LineChartDataset() { label = "SRNs Out Of SLA" }
                }
            };

            var startDate = monthStart;
            while (startDate <= monthEnd)
            {
                var label = startDate.ToString("MMMM yyyy");
                chart.ChartLabels.Add(label);

                // SRN’s Non Submission - DISTINCT COUNT OF SRNs WHERE IsSRNSubmitted = NO
                var srnsNonSubmission = sourceData.Result.Where(o => o.FileYear == startDate.Year && o.FileMonth == startDate.Month).Result.Select(o => o.ChartCalculationValue).Result.FirstOrDefault();
                chart.ChartData.First(d => d.label == "SRNs Non Submissions").data.Add(srnsNonSubmission);

                // SRN’s Out of SLA - DISTINCT COUNT OF SRNs WHERE IsSRNOutOfSLA = YES.
                var srnsOutOfSLA = sourceData.Result.Where(o => o.FileYear == startDate.Year && o.FileMonth == startDate.Month).Result.Select(o => o.ChartCalculationValue2).Result.FirstOrDefault();
                chart.ChartData.First(d => d.label == "SRNs Out Of SLA").data.Add(srnsOutOfSLA);

                startDate = startDate.AddMonths(1);
            }

            chart.ChartName = "Member SRNs Non Submissions / Out Of SLA Last 3 Months";
            return chart;
        }

        public BarChart GetMemberSrnsOutOfSlaPerBureauChart(RejectionReportParameters parameters)
        {
            //We need to unobscure the bureaus
            

            // We need 3 months of data
            var monthStart = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(-2);
            var monthEnd = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(1).AddDays(-1);

            // Build where clause
            var whereClause = "DimDTHReportType = 'Bureau Analysis'";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += $" AND FileDate BETWEEN '{monthStart:yyyy-MM-dd}' AND '{monthEnd:yyyy-MM-dd}'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,FileYear,FileMonth,ChartCalculationValue = COUNT(DISTINCT CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END)",
                Where = whereClause,
                GroupBy = "BureauName,FileYear,FileMonth"
            };
            var sourceData = _dataWarehouseService.GetResultArray<ChartDataModel>(_reportTables.MonthlyOutOfSlaView, apiCallModel);

            var bureaus = sourceData.Result.Select(b => b.BureauName).Distinct().OrderBy(b => b);
            var chart = new BarChart();
            var startDate = monthStart;

            // Put dates on the labels
            while (startDate <= monthEnd)
            {
                var label = startDate.ToString("MMMM yyyy");
                chart.ChartLabels.Add(label);

                startDate = startDate.AddMonths(1);
            }

            foreach (var bureauName in bureaus)
            {
                var bureauDataSet = new LineChartDataset() { label = bureauName };
                chart.ChartData.Add(bureauDataSet);

                startDate = monthStart;
                while (startDate <= monthEnd)
                {
        
                    // SRN’s Out of SLA - DISTINCT COUNT OF SRNs WHERE IsSRNOutOfSLA = YES per bureau
                    var srnsOutOfSLA = sourceData
                        .Result.Where(o => o.FileYear == startDate.Year && o.FileMonth == startDate.Month && o.BureauName == bureauName)
                        .Result.Select(o => o.ChartCalculationValue).Result.FirstOrDefault();
                    bureauDataSet.data.Add(srnsOutOfSLA);

                    startDate = startDate.AddMonths(1);
                }
            }

            chart.ChartName = "Member SRNs Out of SLA By Bureau Last 3 Months";
            return chart;
        }

        public BarChart GetMemberSrnsNotLoadedPerBureauChart(RejectionReportParameters parameters)
        {
            // We need 3 months of data
            var monthStart = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(-2);
            var monthEnd = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(1).AddDays(-1);

            // Build where clause
            var whereClause = "DimDTHReportType = 'Bureau Analysis'";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += $" AND FileDate BETWEEN '{monthStart:yyyy-MM-dd}' AND '{monthEnd:yyyy-MM-dd}'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,FileYear,FileMonth,ChartCalculationValue = COUNT(DISTINCT CASE WHEN IsSRNLoaded = 'NO' THEN SRNNumber ELSE NULL END)",
                Where = whereClause,
                GroupBy = "BureauName,FileYear,FileMonth"
            };
            var sourceData = _dataWarehouseService.GetResultArray<ChartDataModel>(_reportTables.MonthlyOutOfSlaView, apiCallModel);

            var bureaus = sourceData.Result.Select(b => b.BureauName).Distinct().OrderBy(b => b);
            var chart = new BarChart();
            var startDate = monthStart;

            // Put dates on the labels
            while (startDate <= monthEnd)
            {
                var label = startDate.ToString("MMMM yyyy");
                chart.ChartLabels.Add(label);

                startDate = startDate.AddMonths(1);
            }

            foreach (var bureauName in bureaus)
            {
                var bureauDataSet = new LineChartDataset() { label = bureauName };
                chart.ChartData.Add(bureauDataSet);

                startDate = monthStart;
                while (startDate <= monthEnd)
                {
                    var EndOfMonthDate = startDate.ToString("yyyyMM");

                    // SRN’s Not Loaded - DISTINCT COUNT OF SRNs WHERE IsSRNLoaded = NO per bureau
                    var srnsNotLoaded = sourceData
                        .Result.Where(o => o.FileYear == startDate.Year && o.FileMonth == startDate.Month && o.BureauName == bureauName)
                        .Result.Select(o => o.ChartCalculationValue).Result.FirstOrDefault();
                    bureauDataSet.data.Add(srnsNotLoaded);

                    startDate = startDate.AddMonths(1);
                }
            }

            chart.ChartName = "Member SRNs Not Loaded By Bureau Last 3 Months";
            return chart;
        }

        private static CardCollectionModel getMemberLevelBureauCards(MemberLevelBureauAnalysis[] outOutSlaItems, string bureauName)
        {
            var memberLevelBureauAnalysis = outOutSlaItems.Result.FirstOrDefault(o => o.BureauName == bureauName);

            if (memberLevelBureauAnalysis == null)
            {
                memberLevelBureauAnalysis = new MemberLevelBureauAnalysis() { BureauName = bureauName };
            }

            // SRNs Out of SLA % - SRNs Out of SLA / SRN's Submitted
            if (memberLevelBureauAnalysis.SRNsSubmitted > 0)
            {
                memberLevelBureauAnalysis.SrnsOutOfSLAPercentage = 100.0 * memberLevelBureauAnalysis.SrnsOutOfSLA / memberLevelBureauAnalysis.SRNsSubmitted;
                memberLevelBureauAnalysis.SrnsOutOfSLAPercentage = Math.Round(memberLevelBureauAnalysis.SrnsOutOfSLAPercentage, DECIMAL_PLACES);
            }

            // SRNs Not Loaded % - SRNs Not Loaded / SRN's Submitted
            if (memberLevelBureauAnalysis.SRNsSubmitted > 0)
            {
                memberLevelBureauAnalysis.SrnsNotLoadedPercentage = 100.0 * memberLevelBureauAnalysis.SrnsNotLoaded / memberLevelBureauAnalysis.SRNsSubmitted;
                memberLevelBureauAnalysis.SrnsNotLoadedPercentage = Math.Round(memberLevelBureauAnalysis.SrnsNotLoadedPercentage, DECIMAL_PLACES);
            }

            // Bureau Score % - 100 - SRNs Not Loaded % - SRNs Out of SLA %
            memberLevelBureauAnalysis.BureauScorePercentage = 100 - memberLevelBureauAnalysis.SrnsOutOfSLAPercentage - memberLevelBureauAnalysis.SrnsNotLoadedPercentage;

            var bureauKpiCardCollection = new CardCollectionModel()
            {
                Title = memberLevelBureauAnalysis.BureauName,
                Cards = new List<CardModel>()
                    {
                        new CardModel()
                        {
                            Title = "SRN's Out of SLA %",
                            Value = memberLevelBureauAnalysis.SrnsOutOfSLAPercentage,
                            HasError = memberLevelBureauAnalysis.SrnsOutOfSLAPercentage > 0,
                            Type = CardModelType.Percentage
                        },
                        new CardModel()
                        {
                            Title = "SRN's Loaded",
                            Value = memberLevelBureauAnalysis.SrnsLoaded,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "SRN's Not Loaded",
                            Value = memberLevelBureauAnalysis.SrnsNotLoaded,
                            HasError = memberLevelBureauAnalysis.SrnsNotLoaded > 0,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "Bureau Score %",
                            Value = memberLevelBureauAnalysis.BureauScorePercentage,
                            Type = CardModelType.Percentage
                        },
                        new CardModel()
                        {
                            Title = "SRN's Within SLA",
                            Value = memberLevelBureauAnalysis.SrnsWithinSLA,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "SRN's Out of SLA",
                            Value = memberLevelBureauAnalysis.SrnsOutOfSLA,
                            HasError = memberLevelBureauAnalysis.SrnsOutOfSLA > 0,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "SRN's Pending Load",
                            Value = memberLevelBureauAnalysis.SrnsPendingLoad,
                            Type = CardModelType.Number
                        },
                    }
            };
            bureauKpiCardCollection.Subtitle = "Bureau Score " + memberLevelBureauAnalysis.BureauScorePercentage + "%";

            foreach (var card in bureauKpiCardCollection.Cards)
            {
                if (card.HasError ?? false == true)
                {
                    bureauKpiCardCollection.HasError = true;
                }
            }

            return bureauKpiCardCollection;
        }

        public OutOfSlaItem[] GetDetails(RejectionReportParameters parameters, string dimDTHReportType = null)
        {
            // Build column list clause
            string columnClause = "EndOfMonthDate,MemberCompanyName,BureauName,SRNNumber";
            columnClause += ",IsSRNSubmitted,IsSRNLoaded,IsSRNOutOfSLA,IsPending,TotalDaysOutOfSLA";
            columnClause += ",SRNMaxSLAReceiveByDate,DTHPushDate,DTHPullDate";
            columnClause += ",DTHReceivedDate,MonthlyFileLoadDate";
            columnClause += ",ConsMonthsOSLA,MonthsPerYearOSLA,ConsMonthsNoSubmit,MonthsPerYearNoSubmit,DimDTHReportType,SPNumber,OSLAReason";
            
            if(dimDTHReportType == "Submission Analysis")
            {
                parameters.BureauName = Array.Empty<string>();
            }
            // Get filtered SRNs from Connect DB
            var filteredSRNs = parameters.SrnNumber;
            // Build where clause
            var whereClause = ReportUtil.GetDataWarehouseOutOfSLAWhereClause(filteredSRNs, parameters);

            if (!string.IsNullOrEmpty(dimDTHReportType))
                whereClause += $" AND DimDTHReportType = '" + dimDTHReportType + "'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                Where = whereClause
            };
            var result = _dataWarehouseService.GetResultArray<OutOfSlaItem>(_reportTables.MonthlyOutOfSlaView, apiCallModel);

            foreach(var res in result)
            {
                string date = res.EndOfMonthDate.ToString();
                string year = date.Substring(0, 4);
                string month = date.Substring(4, 2);
                string day = date.Substring(6, 2);
                res.EndOfMonthDateAsDate = new DateTime(Convert.ToInt16(year), Convert.ToInt16(month), Convert.ToInt16(day));
            }

            return result;
        }
    }
}
