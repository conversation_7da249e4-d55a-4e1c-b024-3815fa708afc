using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Sacrra.Membership.Reporting.Models.FileSubmissionModels;
using Sacrra.Membership.Database;
using Sacrra.Membership.Business.Helpers;
using System.Security.Claims;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Business.Services;
using Newtonsoft.Json;
using RestSharp;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Database.Enums;
using Microsoft.EntityFrameworkCore;

namespace Sacrra.Membership.Reporting.Services
{
    public class FileReplacementSubmissionService
    {
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private AppDbContext _dbContext;
        private GlobalHelper _globalHelper;

        public FileReplacementSubmissionService(DataWarehouseService dataWarehouseService, AppDbContext dbContext, GlobalHelper globalHelper)
        {
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _dbContext = dbContext;
            _globalHelper = globalHelper;
        }

        public FileSubmissionOutputDTO[] GetFileSubmissions(FileSubmissionInputDTO inputDTO)
        {
            try
            {
                var dailyAndMonthlyFileSubmissionsWhereClause = $"1 = 1";
                DataWarehouseAPIModel dailyAndMonthlyFileSubmissionsApiCallModel;
                var connectReplacementFileList = _dbContext.ReplacementFileSubmissions
                    .Include(x => x.SRN)
                    .ToList();

                inputDTO.FileDateFrom = String.Format("{0:MM/dd/yyyy}", inputDTO.FileDateFrom);
                inputDTO.FileDateTo = String.Format("{0:MM/dd/yyyy}", inputDTO.FileDateTo);

                if (!string.IsNullOrEmpty(inputDTO.FileDateTo)  && !string.IsNullOrEmpty(inputDTO.FileDateFrom))
                {
                    dailyAndMonthlyFileSubmissionsWhereClause += $" AND FileDate BETWEEN '{inputDTO.FileDateFrom}' AND '{inputDTO.FileDateTo}'";
                }

                if (inputDTO.SRNNumber != null)
                {
                    dailyAndMonthlyFileSubmissionsWhereClause += $" AND SRNNumber = '{inputDTO.SRNNumber}'";
                }

                if (inputDTO.SPNumber != null)
                {
                    dailyAndMonthlyFileSubmissionsWhereClause += $" AND SPNumber = '{inputDTO.SPNumber}'";
                }

                if (inputDTO.SRNDisplayName != null)
                {
                    dailyAndMonthlyFileSubmissionsWhereClause += $" AND SRNDisplayName = '{inputDTO.SRNDisplayName}'";
                    connectReplacementFileList.Result.Where(x => x.SRN.TradingName == inputDTO.SRNDisplayName);
                }

                if (inputDTO.FileName != null && inputDTO.FileName != "")
                {
                    dailyAndMonthlyFileSubmissionsWhereClause += $" AND FileName = '{inputDTO.FileName}'";
                    connectReplacementFileList.Result.Where(x => x.ReplacementFileName == inputDTO.FileName);
                }

                if (inputDTO.FileTypeId > 0)
                {
                    var fileType = inputDTO.FileTypeId switch
                    {
                        // Daily
                        1 => "D",
                        // Monthly
                        2 => "M",
                        // Ad-Hoc
                        3 => "A",
                        _ => throw new Exception("File type was not recognized."),
                    };

                    dailyAndMonthlyFileSubmissionsWhereClause += $" AND FileType = '{fileType}'";
                }

                dailyAndMonthlyFileSubmissionsWhereClause += $" AND IsLatest = 1";

                dailyAndMonthlyFileSubmissionsApiCallModel = new DataWarehouseAPIModel()
                {
                    Columns = "FileName, SRNDisplayName, SRNNumber, FileDate, FileType, SPNumber, IsLatest, DWDateCreated",
                    Where = dailyAndMonthlyFileSubmissionsWhereClause
                };

                var dwReplacementFileList = _dataWarehouseService.GetResultArray<FileSubmissionOutputDTO>(_reportTables.DailyAndMonthlyFileSubmissions, dailyAndMonthlyFileSubmissionsApiCallModel);

                foreach (var replacementFile in dwReplacementFileList)
                {
                    var connectReplacementFile = connectReplacementFileList.Find(x => x.ReplacementFileName == replacementFile.FileName);

                    replacementFile.ReplacementFileStatus = connectReplacementFile == null ? "N/A" : EnumHelper.GetEnumIdValuePair<ReplacementFileSubmissionStatuses>(connectReplacementFile.ReplacementFileSubmissionStatusId).Value;
                }

                return dwReplacementFileList;
            } catch (Exception ex)
            {
                throw;
            }
        }
    }
}
