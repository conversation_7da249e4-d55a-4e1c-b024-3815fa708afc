using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.DataWarehouseDTO;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Sacrra.Membership.Reporting.Services
{
    public class IndustryBenchmarkReportService
    {
        private AppDbContext _dbContext;
        public IMapper _mapper { get; }
        private readonly GlobalHelper _globalHelper;
        private readonly DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private readonly ReportCommonService _reportCommonService;

        public IndustryBenchmarkReportService(AppDbContext dbContext, IMapper mapper, GlobalHelper globalHelper,
            DataWarehouseService dataWarehouseService, ReportCommonService reportCommonService)
        {
            _dbContext = dbContext;
            _mapper = mapper;
            _globalHelper = globalHelper;
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _reportCommonService = reportCommonService;
        }
        public IndustryBenchmarkReportOutputFilterDTO GetFilters(User user)
        {

            if (user == null)
                throw new InvalidUserException();

            var algLeaders = _dbContext.Members
                .Include(i => i.Clients)
                    .ThenInclude(x => x.Client)
                .Result.Where(i => i.MembershipTypeId == MembershipTypes.ALGLeader);

            var members = _dbContext.Members
                .Result.Where(i => i.MembershipTypeId == MembershipTypes.FullMember
                || i.MembershipTypeId == MembershipTypes.NonMember
                || i.MembershipTypeId == MembershipTypes.ALGClient);

            var bureaus = _dbContext.Members
                .Result.Where(i => i.MembershipTypeId == MembershipTypes.Bureau);

            if (!_globalHelper.IsInternalSACRRAUser(user))
            {
                if (user.Result.RoleId == UserRoles.Member)
                {
                    members = members.Result.Where(x => (x.MembershipTypeId == MembershipTypes.FullMember || x.MembershipTypeId == MembershipTypes.NonMember)
                                && x.Users.Result.Any(i => i.UserId == user.Id));

                    algLeaders = algLeaders.Result.Where(x => (x.MembershipTypeId == MembershipTypes.ALGLeader)
                                && x.Users.Result.Any(i => i.UserId == user.Id));
                }
                else if(user.Result.RoleId == UserRoles.ALGLeader)
                {
                    members = members.Result.Where(x => x.MembershipTypeId == MembershipTypes.ALGLeader || x.MembershipTypeId == MembershipTypes.ALGClient
                            && x.Users.Result.Any(i => i.UserId == user.Id));

                    algLeaders = algLeaders.Result.Where(x => (x.MembershipTypeId == MembershipTypes.ALGLeader)
                                && x.Users.Result.Any(i => i.UserId == user.Id));
                }
                else if (user.Result.RoleId == UserRoles.Bureau)
                {
                    bureaus = bureaus.Result.Where(x => x.MembershipTypeId == MembershipTypes.Bureau
                            && x.Users.Result.Any(i => i.UserId == user.Id));
                }
            }

            algLeaders = algLeaders.Result.Select(m => new Member
            {
                RegisteredName = m.RegisteredName,
                Clients = m.Clients.Result.Select(x => new ALGClientLeader
                {
                    Client = new Member
                    {
                        RegisteredName = x.Client.RegisteredName
                    }
                }).ToList(),
            });

            members = members.Result.Select(m => new Member
            {
                RegisteredName = m.RegisteredName,
                IndustryClassificationId = m.IndustryClassificationId,
            });

            bureaus = bureaus.Result.Select(m => new Member
            {
                RegisteredName = m.RegisteredName
            });

            return new IndustryBenchmarkReportOutputFilterDTO
            { 
                 AlgLeaders = _mapper.Map<List<ReportFilterAlgLeaderDTO>>(algLeaders).ToList(),
                 Bureaus = PopulateMemberNames(bureaus.ToList()),
                 Members = PopulateMemberNames(members.ToList()),
                 SacrraIndustryClassifications = PopulateIndustryClassifications(members.ToList()),
                 CreditInformationClassifications = PopulateCreditInformationClassifications()
            };
        }

        private List<string> PopulateMemberNames(List<Member> members)
        {
            if (members != null)
            {
                List<string> memberNames = new List<string>();
                foreach (Member member in members)
                {
                    if (member != null)
                        memberNames.Add(member.RegisteredName);
                }

                return memberNames;
            }
            return new List<string>();
        }
        private List<string> PopulateCreditInformationClassifications()
        {
            List<string> classifications = new List<string>();

            classifications = _dbContext.CreditInformationClassifications
                .Result.Select(m => m.Name)
                .ToList();

            return classifications;
        }
        private List<ReportFilterSacrraIndustryClassificationDTO> PopulateIndustryClassifications(List<Member> members)
        {
            List<ReportFilterSacrraIndustryClassificationDTO> industryClassifications = new List<ReportFilterSacrraIndustryClassificationDTO>();

            var groupByIndustryClassification =
                from classification in members
                where classification.IndustryClassificationId > 0
                group classification by classification.IndustryClassificationId into newGroup
                orderby newGroup.Key
                select newGroup;

            foreach (var nameGroup in groupByIndustryClassification)
            {
                industryClassifications.Add(new()
                {
                    ClassificationName = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)nameGroup.Key).Value,
                    Members = members
                        .Result.Where(i => i.IndustryClassificationId == nameGroup.Key)
                        .Result.Select(i => i.RegisteredName)
                        .ToList()
                });
            }
            return industryClassifications;
        }

        public List<IndustryBenchmarkBureauCardDTO> GetIndustryBenchmarkReportCards(IndustryBenchmarkInputReportInputFilterDTO filter)
        {
            var srnNumbers = _reportCommonService.GetFilteredSRNs(filter);
            var apiCallModel = new BenchmarkReportAPICallModel
            {
                SRNs = string.Join(",", srnNumbers),
                Bureau = (filter.Bureau.Count() > 0)? string.Join(",", filter.Bureau.Result.Select(m => m.Replace("'", "''"))) : "All",
                YearFrom = filter.YearFrom,
                MonthFrom = filter.MonthFrom,
                YearTo = filter.YearTo,
                MonthTo = filter.MonthTo,
                IndustryClassification = filter.IndustryClassification,
                ALGLeader = filter.ALGLeader,
                CreditInformationClassification = filter.CreditInformationClassification
            };

            var result = _dataWarehouseService.GetBenchmarkReportData<BenchmarkReportCardModel>(apiCallModel);

            if(result != null)
            {
                if(result.Length > 0)
                {
                    var bureauCards = new List<IndustryBenchmarkBureauCardDTO>();

                    foreach (var bureauResult in result)
                    {
                        var reportCards = new List<IndustryBenchmarkCardDTO>
                        {
                            new()
                            {
                                Header = $"Records Received as a % of { filter.IndustryClassification }",
                                Percentage = bureauResult.MemberRecordsReceivedPercentageOfIndustry
                            },
                            new()
                            {
                                Header = "Records Received % of All Industries",
                                Percentage = bureauResult.MembReceivedPercentageOfAllIndustries
                            },
                            new()
                            {
                                Header = "Member Records Rejected %",
                                Percentage = bureauResult.MemberRecordsRejected
                            },
                            new()
                            {
                                Header = "Industry Records Rejected %", //All total records rejected for industry divided by all industry records received
                                Percentage = bureauResult.IndustryRejectedPercentage
                            }
                        };

                        RoundReportCardDecimals(reportCards);

                        bureauCards.Add(new IndustryBenchmarkBureauCardDTO
                        {
                            BureauName = bureauResult.BureauName,
                            Cards = reportCards
                        });
                    }
                    

                    return bureauCards;
                }
            }

            return new List<IndustryBenchmarkBureauCardDTO>();
        }
        public BarChart GetIndustryRecordsReceivedVsMemberRecordsReceivedCharts(IndustryBenchmarkInputReportInputFilterDTO filter)
        {
            // Build where clause
            string whereClause = GetWhereClause(filter);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "Year,Month,SUM(MemberRecordsReceived) AS [MemberRecordsReceived],SUM(IndustryRecordsReceived) AS [IndustryRecordsReceived]",
                OrderBy = "Year,Month",
                GroupBy = "Year,Month",
                Where = whereClause
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.IndustryBenchmarkRerport, apiCallModel);


            var barChart = new BarChart();
            var memberRecordsReceivedDataSet = new LineChartDataset();
            memberRecordsReceivedDataSet.label = "Member Records Received";

            var industryRecordsReceivedDataSet = new LineChartDataset();
            industryRecordsReceivedDataSet.label = "Industry Records Received";

            foreach (var arrayRow in resultArray)
            {
                var fullDate = new DateTime(arrayRow["Year"].ToObject<int>(), arrayRow["Month"].ToObject<int>(), 1);
                string yearMonthString = fullDate.ToString("MMMM yyyy");

                var totalIndustryRecordsReceived = arrayRow["IndustryRecordsReceived"].ToObject<double>();
                var totalMemberRecordsReceived = arrayRow["MemberRecordsReceived"].ToObject<double>();

                totalIndustryRecordsReceived = Math.Round(totalIndustryRecordsReceived, 3);
                totalMemberRecordsReceived = Math.Round(totalMemberRecordsReceived, 3);

                barChart.ChartLabels.Add(yearMonthString);

                industryRecordsReceivedDataSet.data.Add(totalIndustryRecordsReceived);
                memberRecordsReceivedDataSet.data.Add(totalMemberRecordsReceived);

            }

            barChart.ChartData.Add(industryRecordsReceivedDataSet);
            barChart.ChartData.Add(memberRecordsReceivedDataSet);

            barChart.ChartName = "Total Industry Records Received vs Total Member Records Received by Year and Month";
            return barChart;
        }
        public BarChart GetIndustryRecordsRejectedVsMemberRecordsRejectedCharts(IndustryBenchmarkInputReportInputFilterDTO filter)
        {
            // Build where clause
            string whereClause = GetWhereClause(filter);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "Year,Month,SUM(MemberRecordsRejected) AS [MemberRecordsRejected],SUM(IndustryRecordsRejected) AS [IndustryRecordsRejected]",
                OrderBy = "Year,Month",
                GroupBy = "Year,Month",
                Where = whereClause
            };
            var resultArray = _dataWarehouseService.GetResultJsonArray(_reportTables.IndustryBenchmarkRerport, apiCallModel);

            var barChart = new BarChart();
            var industryRecordsRejectedDataSet = new LineChartDataset();
            industryRecordsRejectedDataSet.label = "Industry Records Rejected";

            var memberRecordsRejectedDataSet = new LineChartDataset();
            memberRecordsRejectedDataSet.label = "Member Records Rejected";

            foreach (var arrayRow in resultArray)
            {
                var fullDate = new DateTime(arrayRow["Year"].ToObject<int>(), arrayRow["Month"].ToObject<int>(), 1);
                string yearMonthString = fullDate.ToString("MMMM yyyy");

                var totalIndustryRecordsRejected = arrayRow["IndustryRecordsRejected"].ToObject<double>();
                var totalMemberRecordsRejected = arrayRow["MemberRecordsRejected"].ToObject<double>();

                totalIndustryRecordsRejected = Math.Round(totalIndustryRecordsRejected, 3);
                totalMemberRecordsRejected = Math.Round(totalMemberRecordsRejected, 3);

                barChart.ChartLabels.Add(yearMonthString);

                industryRecordsRejectedDataSet.data.Add(totalIndustryRecordsRejected);
                memberRecordsRejectedDataSet.data.Add(totalMemberRecordsRejected);
            }
            barChart.ChartData.Add(industryRecordsRejectedDataSet);
            barChart.ChartData.Add(memberRecordsRejectedDataSet);

            barChart.ChartName = "Total Industry Records Rejected vs Total Member Records Rejected by Year and Month";
            return barChart;
        }
        private void RoundReportCardDecimals(List<IndustryBenchmarkCardDTO> reportCards)
        {
            if(reportCards != null)
            {
                foreach(var card in reportCards)
                {
                    if(card.Percentage > 0)
                        card.Percentage = Math.Round(card.Percentage, 3);
                }
            }
        }

        private string GetWhereClause(IndustryBenchmarkInputReportInputFilterDTO filter)
        {
            var whereClause = string.Empty;

            if (filter.Member != null && filter.Member.Count() > 0)
                whereClause += $" CompanyName = '" + filter.Member.Replace("'", "''") + "'";

            if (filter.YearFrom > 0 && filter.YearTo > 0)
                whereClause += $" AND ([Year]*100+[Month]) BETWEEN {filter.YearFrom}{filter.MonthFrom.ToString("00")} AND {filter.YearTo}{filter.MonthTo.ToString("00")}";

            if (filter.Bureau != null && filter.Bureau.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", filter.Bureau.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (filter.ALGLeader != null && filter.ALGLeader.Count() > 0)
                whereClause += $" AND ALGLeader = '" + filter.ALGLeader.Replace("'", "''") + "'";

            if (filter.IndustryClassification != null && filter.IndustryClassification.Count() > 0)
                whereClause += $" AND SACRRAIndustryClassification = '" + filter.IndustryClassification.Replace("'", "''") + "'";

            if (filter.CreditInformationClassification != null && filter.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification = '" + filter.CreditInformationClassification.Replace("'", "''") + "'";

            return whereClause;
        }
    }
}
