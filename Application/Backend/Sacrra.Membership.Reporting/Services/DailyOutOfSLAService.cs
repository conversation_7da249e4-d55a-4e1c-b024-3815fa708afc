using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.OutOfSLAReportModels;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Util;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Sacrra.Membership.Reporting.Services
{
    public class DailyOutOfSLAService
    {
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        private const int DECIMAL_PLACES = 3;
        private ReportCommonService _reportCommonService;

        public DailyOutOfSLAService(DataWarehouseService dataWarehouseService, ReportCommonService reportCommonService)
        {
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _reportCommonService = reportCommonService;
        }
        public CardCollectionModel GetMemberSubmissionAnalysisKpiCards(RejectionReportParameters parameters)
        {
            var columns = @"	 
                 SRNsSubmitted = COUNT(DISTINCT (CASE WHEN IsSRNSubmitted = 'NA' THEN SRNNumber ELSE NULL END))
                ,SRNsNonSubmission = COUNT(DISTINCT (CASE WHEN IsSRNSubmitted = 'NO' THEN SRNNumber ELSE NULL END))
                ,SRNsOutOfSLA = COUNT(DISTINCT (CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END))
                ,AvgDaysOutOfSLA = AVG (CASE WHEN IsSRNOutOfSLA = 'Yes' THEN TotalDaysOutOfSLA ELSE NULL END)
                ,FilesOutOfSLA = COUNT(DISTINCT (CASE WHEN IsFileOutOfSLA = 'YES' THEN DTHFileName ELSE NULL END))
                ,FilesSubmitted = COUNT(DISTINCT (DTHFileName))
            ";

            // Build where clause
            var whereClause = $"YearMonth IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += " AND DimDTHReportType = 'File Submission Analysis'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columns,
                Where = whereClause
            };
            var fileLevelSubmissionAnalysis = _dataWarehouseService.GetResultArray<FileLevelSubmissionAnalysis>(_reportTables.DailyOutOfSlaView, apiCallModel)[0];

            // SRN Submission Success% - SRN Submitted  / (SRN non submission + SRN Submitted)
            if (fileLevelSubmissionAnalysis.SRNsNonSubmission + fileLevelSubmissionAnalysis.SRNsSubmitted > 0)
            {
                fileLevelSubmissionAnalysis.SRNsOutOfSLAPercentage = 100.0 * fileLevelSubmissionAnalysis.SRNsSubmitted / (fileLevelSubmissionAnalysis.SRNsNonSubmission + fileLevelSubmissionAnalysis.SRNsSubmitted);
                fileLevelSubmissionAnalysis.SRNsOutOfSLAPercentage = Math.Round(fileLevelSubmissionAnalysis.SRNsOutOfSLAPercentage, DECIMAL_PLACES);
            }

            // Files Out of SLA % - Files Out of SLA / Files Submitted.
            if (fileLevelSubmissionAnalysis.FilesSubmitted > 0)
            {
                fileLevelSubmissionAnalysis.FilesOutOfSLAPercentage = 100.0 * fileLevelSubmissionAnalysis.FilesOutOfSLA / fileLevelSubmissionAnalysis.FilesSubmitted;
                fileLevelSubmissionAnalysis.FilesOutOfSLAPercentage = Math.Round(fileLevelSubmissionAnalysis.FilesOutOfSLAPercentage, DECIMAL_PLACES);
            }

            // Member Submission Analysis
            var memberName = parameters.MemberName.Length > 1 ? "Multiple" : parameters.MemberName.Result[0];
            var srnCardCollectionModel = new CardCollectionModel()
            {
                Title = "SRN Analysis",
                Cards = new List<CardModel>()
                {
                    new CardModel()
                    {
                        Title = "SRN's Out of SLA %",
                        Value = fileLevelSubmissionAnalysis.SRNsOutOfSLAPercentage,
                        HasError = fileLevelSubmissionAnalysis.SRNsOutOfSLAPercentage > 0,
                        Type = CardModelType.Percentage
                    },
                    new CardModel()
                    {
                        Title = "SRNs Out Of SLA",
                        Value = fileLevelSubmissionAnalysis.SRNsOutOfSLA,
                        Type = CardModelType.Number,
                        HasError = fileLevelSubmissionAnalysis.SRNsOutOfSLA > 0
                    },
                    new CardModel()
                    {
                        Title = "SRN's Submitted",
                        Value = fileLevelSubmissionAnalysis.SRNsSubmitted,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "SRN Avg Days Out of SLA",
                        Value = fileLevelSubmissionAnalysis.AvgDaysOutOfSLA,
                        Type = CardModelType.Number
                    }
                }
            };

            var fileCardCollectionModel = new CardCollectionModel()
            {
                Title = "File Analysis",
                Cards = new List<CardModel>()
                {
                    new CardModel()
                    {
                        Title = "Files OSLA %",
                        Value = fileLevelSubmissionAnalysis.FilesOutOfSLAPercentage,
                        Type = CardModelType.Percentage
                    },
                    new CardModel()
                    {
                        Title = "Files Out Of SLA",
                        Value = fileLevelSubmissionAnalysis.FilesOutOfSLA,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "Files Submitted",
                        Value = fileLevelSubmissionAnalysis.FilesSubmitted,
                        Type = CardModelType.Number
                    },
                }
            };

            var model = new CardCollectionModel() {
                Title = memberName + ": Submission Analysis",
                ChildCardCollection = new List<CardCollectionModel>()
                {
                    srnCardCollectionModel,
                    fileCardCollectionModel
                }
            };

            foreach (var cardCollectionModel in model.ChildCardCollection)
            {
                foreach (var card in cardCollectionModel.Cards)
                {
                    if (card.HasError ?? false == true)
                    {
                        cardCollectionModel.HasError = true;
                    }
                }
            }

            // cardCollectionModel.IsExpanded = true;

            return model;
        }
        public CardCollectionModel GetMemberBureauSRNAnalysisKpiCards(RejectionReportParameters parameters)
        {
            var columns = @"
	             BureauName
	            ,SRNsSubmitted = COUNT(DISTINCT (CASE WHEN IsSRNSubmitted = 'NA' THEN SRNNumber ELSE NULL END))
	            ,SrnsWithinSLA = COUNT(DISTINCT (CASE WHEN IsSRNOutOfSLA = 'NO' THEN SRNNumber ELSE NULL END))
	            ,SrnsOutOfSLA = COUNT(DISTINCT (CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END))
	            ,SrnsLoaded = COUNT(DISTINCT (CASE WHEN IsSRNLoaded = 'YES' THEN SRNNumber ELSE NULL END))
	            ,SrnsNotLoaded = COUNT(DISTINCT (CASE WHEN IsSRNLoaded = 'NO' THEN SRNNumber ELSE NULL END))
            ";

            // Build where clause
            var whereClause = $"YearMonth IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += " AND DimDTHReportType = 'File Bureau Analysis'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columns,
                Where = whereClause,
                GroupBy = "BureauName"
            };
            var outOutSlaItems = _dataWarehouseService.GetResultArray<MemberLevelBureauAnalysis>(_reportTables.DailyOutOfSlaView, apiCallModel);

            // File Bureau Analysis
       
            var memberName = parameters.MemberName.Length > 1 ? "Multiple" : parameters.MemberName.Result[0];

            var cardBureauAnalysis = new CardCollectionModel()
            {
                Title = memberName + ": Bureau SRN Analysis",
            };

            // Get list of bureaus
            string[] bureauList = null;
            if (parameters.BureauName.Count() > 0)
            {
                bureauList = parameters.BureauName;
            }
            else
            {
                bureauList = outOutSlaItems.Result.Where(b => b.BureauName != null).Result.Select(o => o.BureauName).Distinct().OrderBy(b => b).ToArray();
            }

            // Member Bureau Analysis
            foreach (var bureauName in bureauList)
            {
                CardCollectionModel memberLevelBureauAnalysisCards = getMemberLevelBureauCards(outOutSlaItems, bureauName);
                cardBureauAnalysis.ChildCardCollection.Add(memberLevelBureauAnalysisCards);
            }

            // Highlight errors
            foreach (var collection in cardBureauAnalysis.ChildCardCollection)
            {
                foreach (var collectionCard in collection.Cards)
                {
                    if (collectionCard.HasError ?? false == true)
                    {
                        cardBureauAnalysis.HasError = true;
                    }
                }
            }

            return cardBureauAnalysis;
        }

        public CardCollectionModel GetMemberBureauFileAnalysisKpiCards(RejectionReportParameters parameters)
        {
            var columns = @"
	             BureauName
   	            ,FilesSubmitted = COUNT(DISTINCT DTHFileName)
	            ,FilesOutOfSLA = COUNT(DISTINCT (CASE WHEN IsFileOutOfSLA = 'YES' THEN DTHFileName ELSE NULL END))
	            ,FilesLoaded = COUNT(DISTINCT (CASE WHEN IsFileLoaded = 'YES' THEN DTHFileName ELSE NULL END))
	            ,FilesNotLoaded = COUNT(DISTINCT (CASE WHEN IsFileLoaded = 'NO' THEN DTHFileName ELSE NULL END))
            ";

            // Build where clause
            var whereClause = $"YearMonth IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += " AND DimDTHReportType = 'File Bureau Analysis'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columns,
                Where = whereClause,
                GroupBy = "BureauName"
            };
            var outOutSlaItems = _dataWarehouseService.GetResultArray<FileLevelBureauAnalysis>(_reportTables.DailyOutOfSlaView, apiCallModel);

            var memberName = parameters.MemberName.Length > 1 ? "Multiple" : parameters.MemberName.Result[0];

            var cardBureauAnalysis = new CardCollectionModel()
            {
                Title = memberName + ": Bureau File Analysis",
            };

            // Get list of bureaus
            string[] bureauList = null;
            if (parameters.BureauName.Count() > 0)
            {
                bureauList = parameters.BureauName;
            }
            else
            {
                bureauList = outOutSlaItems.Result.Where(b => b.BureauName != null).Result.Select(o => o.BureauName).Distinct().OrderBy(b => b).ToArray();
            }

            // Member Bureau Analysis
            foreach (var bureauName in bureauList)
            {
                CardCollectionModel memberLevelBureauAnalysisCards = getFileLevelBureauCards(outOutSlaItems, bureauName);
                cardBureauAnalysis.ChildCardCollection.Add(memberLevelBureauAnalysisCards);
            }

            // Highlight errors
            foreach (var collection in cardBureauAnalysis.ChildCardCollection)
            {
                foreach (var collectionCard in collection.Cards)
                {
                    if (collectionCard.HasError ?? false == true)
                    {
                        cardBureauAnalysis.HasError = true;
                    }
                }
            }

            return cardBureauAnalysis;
        }

        public CardCollectionModel GetSrnSubmissionAnalysisCards(RejectionReportParameters parameters)
        {
            if (parameters.SrnNumber == null || parameters.SrnNumber.Count() != 1)
                return new CardCollectionModel();

            // Build where clause
            var whereClause = "";
            whereClause += $"YearMonth IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "TotalDaysOutOfSLA,ConsMonthsOSLA,MonthsPerYearOSLA,ConsMonthsNoSubmit,MonthsPerYearNoSubmit",
                Where = whereClause
            };
            var outOfSlaItems = _dataWarehouseService.GetResultArray<OutOfSlaItem>(_reportTables.DailyOutOfSlaView, apiCallModel);

            // Member Submission Analysis
            var srnLevelSubmissionAnalysis = new SRNLevelSubmissionAnalysis();

            // Days Past SLA - Max TotalDaysOutOfSLA for SRN
            srnLevelSubmissionAnalysis.DaysPastSLA = outOfSlaItems.Result.Select(s => s.TotalDaysOutOfSLA).Max();

            // Consecutive Months Out of SLA - Max ConsMonthsOSLA for SRN
            srnLevelSubmissionAnalysis.MonthsOutOfSLA = outOfSlaItems.Result.Select(s => s.ConsMonthsOSLA).Max();

            // OSLA Last 12 Months - Max MonthsPerYearOSLA for SRN
            srnLevelSubmissionAnalysis.YearsOutOfSLA = outOfSlaItems.Result.Select(s => s.MonthsPerYearOSLA).Max();

            // Number of Consecutive Months No Submission - Max ConsMonthsNoSubmit for SRN
            srnLevelSubmissionAnalysis.MonthsNoSubmit = outOfSlaItems.Result.Select(s => s.ConsMonthsNoSubmit).Max();

            // Number of No Submissions Last 12 Months - Max
            srnLevelSubmissionAnalysis.YearsNoSubmit = outOfSlaItems.Result.Select(s => s.MonthsPerYearNoSubmit).Max();

            var member = parameters.MemberName.Result[0];
            var srnNumber = parameters.SrnNumber.Result[0];

            CardCollectionModel collection = new CardCollectionModel()
            {
                Title = $"{member}: {srnNumber}: Submission Analysis",
                Cards = new List<CardModel>()
                {
                    new CardModel()
                    {
                        Title = "Days Past SLA",
                        Value = srnLevelSubmissionAnalysis.DaysPastSLA ?? 0,
                        HasError = srnLevelSubmissionAnalysis.DaysPastSLA > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "Consecutive Months OSLA",
                        Value = srnLevelSubmissionAnalysis.MonthsOutOfSLA ?? 0,
                        HasError = srnLevelSubmissionAnalysis.MonthsOutOfSLA > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "OSLA Last 12 Months",
                        Value = srnLevelSubmissionAnalysis.YearsOutOfSLA ?? 0,
                        HasError = srnLevelSubmissionAnalysis.YearsOutOfSLA > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "Consecutive Months No Submit",
                        Value = srnLevelSubmissionAnalysis.MonthsNoSubmit ?? 0,
                        HasError = srnLevelSubmissionAnalysis.MonthsNoSubmit > 0,
                        Type = CardModelType.Number
                    },
                    new CardModel()
                    {
                        Title = "No Submit Last 12 Months",
                        Value = srnLevelSubmissionAnalysis.YearsNoSubmit ?? 0,
                        HasError = srnLevelSubmissionAnalysis.YearsNoSubmit > 0,
                        Type = CardModelType.Number
                    }
                }
            };
            collection.IsExpanded = true;

            return collection;
        }

        public CardCollectionModel GetSrnBureauAnalysisCards(RejectionReportParameters parameters)
        {
            if (parameters.SrnNumber == null || parameters.SrnNumber.Count() != 1)
                return new CardCollectionModel();

            // Build where clause
            var whereClause = "";
            whereClause += $"YearMonth IN ('{parameters.Year:0000}{parameters.Month:00}')";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,TotalDaysOutOfSLA",
                Where = whereClause
            };
            var outOfSlaItems = _dataWarehouseService.GetResultArray<FileOutOfSlaItem>(_reportTables.DailyOutOfSlaView, apiCallModel);

            var member = parameters.MemberName.Result[0];
            var srnNumber = parameters.SrnNumber.Result[0];

            CardCollectionModel collection = new CardCollectionModel()
            {
                Title = $"{member}: {srnNumber}: Bureau Analysis | The bureau score is the combined percentage of out of SLA SRNs and SRNs not loaded"
            };

            var bureaus = outOfSlaItems.Result.Where(b => b.BureauName != null).Result.Select(b => b.BureauName).Distinct().OrderBy(b => b);
            foreach (var bureau in bureaus)
            {
                var maxDaysOutOfSLA = outOfSlaItems.Result.Where(o => o.BureauName == bureau).Result.Select(o => o.TotalDaysOutOfSLA).Max();
                var hasError = maxDaysOutOfSLA > 0;

                var card = new CardModel()
                {
                    Title = $"{bureau} Days past SLA",
                    Value = maxDaysOutOfSLA,
                    HasError = hasError,
                    Type = CardModelType.Number
                };
                collection.Cards.Add(card);

                if (hasError)
                {
                    collection.HasError = true;
                }
            }

            return collection;
        }

        public BarChart GetMemberSrnsOutOfSlaPerBureauChart(RejectionReportParameters parameters)
        {
            // We need 3 months of data
            var monthStart = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(-2);
            var monthEnd = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(1).AddDays(-1);

            // Build where clause
            var whereClause = "DimDTHReportType = 'File Bureau Analysis'";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += $" AND FileDate BETWEEN '{monthStart:yyyy-MM-dd}' AND '{monthEnd:yyyy-MM-dd}'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,FileYear,FileMonth,ChartCalculationValue = COUNT(DISTINCT (CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END))",
                Where = whereClause,
                GroupBy = "BureauName,FileYear,FileMonth"
            };
            var sourceData = _dataWarehouseService.GetResultArray<ChartDataModel>(_reportTables.DailyOutOfSlaView, apiCallModel);

            var bureaus = sourceData.Result.Select(b => b.BureauName).Distinct().OrderBy(b => b);
            var chart = new BarChart();
            var startDate = monthStart;

            // Put dates on the labels
            while (startDate <= monthEnd)
            {
                var label = startDate.ToString("MMMM yyyy");
                chart.ChartLabels.Add(label);

                startDate = startDate.AddMonths(1);
            }

            foreach (var bureauName in bureaus)
            {
                var bureauDataSet = new LineChartDataset() { label = bureauName };
                chart.ChartData.Add(bureauDataSet);

                startDate = monthStart;
                while (startDate <= monthEnd)
                {
          
                    // SRN’s Out of SLA - DISTINCT COUNT OF SRNs WHERE IsSRNOutOfSLA = YES per bureau
                    var srnsOutOfSlaCountForBureau = sourceData
                        .Result.Where(o => o.FileYear == startDate.Year && o.FileMonth == startDate.Month && o.BureauName == bureauName).Result.Select(o => o.ChartCalculationValue).Result.FirstOrDefault();
                    bureauDataSet.data.Add(srnsOutOfSlaCountForBureau);
                    
                    startDate = startDate.AddMonths(1);
                }
            }

            chart.ChartName = "Member SRNs Out of SLA By Bureau Last 3 Months";
            return chart;
        }

        public BarChart GetMemberFilesNotLoadedPerBureauChart(RejectionReportParameters parameters)
        {
            // We need 3 months of data
            var monthStart = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(-2);
            var monthEnd = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(1).AddDays(-1);

            // Build where clause
            var whereClause = "DimDTHReportType = 'File Bureau Analysis'";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.BureauName != null && parameters.BureauName.Count() > 0)
                whereClause += $" AND BureauName IN (" + string.Join(",", parameters.BureauName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += $" AND FileDate BETWEEN '{monthStart:yyyy-MM-dd}' AND '{monthEnd:yyyy-MM-dd}'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "BureauName,FileYear,FileMonth,ChartCalculationValue = COUNT(DISTINCT (CASE WHEN IsFileLoaded = 'NO' THEN DTHFileName ELSE NULL END))",
                Where = whereClause,
                GroupBy = "BureauName,FileYear,FileMonth"
            };
            var sourceData = _dataWarehouseService.GetResultArray<ChartDataModel>(_reportTables.DailyOutOfSlaView, apiCallModel);

            var bureaus = sourceData.Result.Select(b => b.BureauName).Distinct().OrderBy(b => b);
            var chart = new BarChart();
            var startDate = monthStart;

            // Put dates on the labels
            while (startDate <= monthEnd)
            {
                var label = startDate.ToString("MMMM yyyy");
                chart.ChartLabels.Add(label);

                startDate = startDate.AddMonths(1);
            }

            foreach (var bureauName in bureaus)
            {
                var bureauDataSet = new LineChartDataset() { label = bureauName };
                chart.ChartData.Add(bureauDataSet);

                startDate = monthStart;
                while (startDate <= monthEnd)
                {
                    // Files Not Loaded - DISTINCT COUNT OF DTHFileName WHERE IsFileLoaded = NO per bureau
                    var filesNotLoadedCount = sourceData
                           .Result.Where(o => o.FileYear == startDate.Year && o.FileMonth == startDate.Month && o.BureauName == bureauName).Result.Select(o => o.ChartCalculationValue).Result.FirstOrDefault();
                    bureauDataSet.data.Add(filesNotLoadedCount);

                    startDate = startDate.AddMonths(1);
                }
            }

            chart.ChartName = "Member Files Not Loaded By Bureau Last 3 Months";
            return chart;
        }

        private static CardCollectionModel getMemberLevelBureauCards(MemberLevelBureauAnalysis[] outOutSlaItems, string bureauName)
        {
            var memberLevelBureauAnalysis = outOutSlaItems.First(o => o.BureauName == bureauName);
            
            // SRNs Out of SLA % - SRNs Out of SLA / SRN's Submitted
            if (memberLevelBureauAnalysis.SRNsSubmitted > 0)
            {
                memberLevelBureauAnalysis.SrnsOutOfSLAPercentage = 100.0 * memberLevelBureauAnalysis.SrnsOutOfSLA / memberLevelBureauAnalysis.SRNsSubmitted;
                memberLevelBureauAnalysis.SrnsOutOfSLAPercentage = Math.Round(memberLevelBureauAnalysis.SrnsOutOfSLAPercentage, DECIMAL_PLACES);
            }

            // SRNs Not Loaded % - SRNs Not Loaded / SRN's Submitted
            if (memberLevelBureauAnalysis.SRNsSubmitted > 0)
            {
                memberLevelBureauAnalysis.SrnsNotLoadedPercentage = 100.0 * memberLevelBureauAnalysis.SrnsNotLoaded / memberLevelBureauAnalysis.SRNsSubmitted;
                memberLevelBureauAnalysis.SrnsNotLoadedPercentage = Math.Round(memberLevelBureauAnalysis.SrnsNotLoadedPercentage, DECIMAL_PLACES);
            }

            // Bureau Score % - 100 - SRNs Not Loaded % - SRNs Out of SLA %
            memberLevelBureauAnalysis.BureauScorePercentage = 100 - memberLevelBureauAnalysis.SrnsOutOfSLAPercentage - memberLevelBureauAnalysis.SrnsNotLoadedPercentage;
            memberLevelBureauAnalysis.BureauScorePercentage = Math.Round(memberLevelBureauAnalysis.BureauScorePercentage, DECIMAL_PLACES);

            var bureauKpiCardCollection = new CardCollectionModel()
            {
                Title = memberLevelBureauAnalysis.BureauName,
                Cards = new List<CardModel>()
                    {
                        new CardModel()
                        {
                            Title = "SRN's Submitted",
                            Value = memberLevelBureauAnalysis.SRNsSubmitted,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "SRN's Out of SLA",
                            Value = memberLevelBureauAnalysis.SrnsOutOfSLA,
                            HasError = memberLevelBureauAnalysis.SrnsOutOfSLA > 0,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "SRN's Out of SLA %",
                            Value = memberLevelBureauAnalysis.SrnsOutOfSLAPercentage,
                            HasError = memberLevelBureauAnalysis.SrnsOutOfSLAPercentage > 0,
                            Type = CardModelType.Percentage
                        },
                        new CardModel()
                        {
                            Title = "SRN's Not Loaded",
                            Value = memberLevelBureauAnalysis.SrnsNotLoaded,
                            HasError = memberLevelBureauAnalysis.SrnsNotLoaded > 0,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "SRN's Not Loaded %",
                            Value = memberLevelBureauAnalysis.SrnsNotLoadedPercentage,
                            HasError = memberLevelBureauAnalysis.SrnsNotLoadedPercentage > 0,
                            Type = CardModelType.Percentage
                        },
                        new CardModel()
                        {
                            Title = "Bureau Score %",
                            Value = memberLevelBureauAnalysis.BureauScorePercentage,
                            Type = CardModelType.Percentage
                        }
                    }
            };
            bureauKpiCardCollection.Subtitle = "Bureau Score " + memberLevelBureauAnalysis.BureauScorePercentage + "%";

            foreach (var card in bureauKpiCardCollection.Cards)
            {
                if (card.HasError ?? false == true)
                {
                    bureauKpiCardCollection.HasError = true;
                }
            }

            return bureauKpiCardCollection;
        }

        private static CardCollectionModel getFileLevelBureauCards(FileLevelBureauAnalysis[] outOutSlaItems, string bureauName)
        {
            var memberLevelBureauAnalysis = outOutSlaItems.First(o => o.BureauName == bureauName);

            // Files Out of SLA % - Files Out of SLA / Files Submitted
            if (memberLevelBureauAnalysis.FilesSubmitted > 0)
            {
                memberLevelBureauAnalysis.FilesOutOfSLAPercentage = 100.0 * memberLevelBureauAnalysis.FilesOutOfSLA / memberLevelBureauAnalysis.FilesSubmitted;
                memberLevelBureauAnalysis.FilesOutOfSLAPercentage = Math.Round(memberLevelBureauAnalysis.FilesOutOfSLAPercentage, DECIMAL_PLACES);
            }

            // Files Not Loaded % - Files Not Loaded / Files Submitted
            if (memberLevelBureauAnalysis.FilesSubmitted > 0)
            {
                memberLevelBureauAnalysis.FilesNotLoadedPercentage = 100.0 * memberLevelBureauAnalysis.FilesNotLoaded / memberLevelBureauAnalysis.FilesSubmitted;
                memberLevelBureauAnalysis.FilesNotLoadedPercentage = Math.Round(memberLevelBureauAnalysis.FilesNotLoadedPercentage, DECIMAL_PLACES);
            }

            // Bureau Score % - 100 - Files Not Loaded % - Files Out of SLA %
            memberLevelBureauAnalysis.BureauScorePercentage = 100 - memberLevelBureauAnalysis.FilesOutOfSLAPercentage - memberLevelBureauAnalysis.FilesNotLoadedPercentage;

            var bureauKpiCardCollection = new CardCollectionModel()
            {
                Title = memberLevelBureauAnalysis.BureauName,
                Cards = new List<CardModel>()
                    {
                        new CardModel()
                        {
                            Title = "Files Submitted",
                            Value = memberLevelBureauAnalysis.FilesSubmitted,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "Files Out of SLA",
                            Value = memberLevelBureauAnalysis.FilesOutOfSLA,
                            HasError = memberLevelBureauAnalysis.FilesOutOfSLA > 0,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "Files Out of SLA %",
                            Value = memberLevelBureauAnalysis.FilesOutOfSLAPercentage,
                            HasError = memberLevelBureauAnalysis.FilesOutOfSLAPercentage > 0,
                            Type = CardModelType.Percentage
                        },
                        new CardModel()
                        {
                            Title = "Files Not Loaded",
                            Value = memberLevelBureauAnalysis.FilesNotLoaded,
                            HasError = memberLevelBureauAnalysis.FilesNotLoaded > 0,
                            Type = CardModelType.Number
                        },
                        new CardModel()
                        {
                            Title = "Files Not Loaded %",
                            Value = memberLevelBureauAnalysis.FilesNotLoadedPercentage,
                            HasError = memberLevelBureauAnalysis.FilesNotLoadedPercentage > 0,
                            Type = CardModelType.Percentage
                        },
                        new CardModel()
                        {
                            Title = "Bureau Score %",
                            Value = memberLevelBureauAnalysis.BureauScorePercentage,
                            Type = CardModelType.Percentage
                        }
                    }
            };
            bureauKpiCardCollection.Subtitle = "Bureau Score " + memberLevelBureauAnalysis.BureauScorePercentage + "%";

            foreach (var card in bureauKpiCardCollection.Cards)
            {
                if (card.HasError ?? false == true)
                {
                    bureauKpiCardCollection.HasError = true;
                }
            }

            return bureauKpiCardCollection;
        }

        public FileOutOfSlaItem[] GetDetails(RejectionReportParameters parameters, string dimDTHReportType = null)
        {
            // Build column list clause
            string columnClause = "BureauName,DTHFileName,SRNNumber,MemberCompanyName,MemberTradingName";
            columnClause += ",SPNumber,DTHFileDate,DTHReceivedDate,DTHDailyFileLoadDate,YearMonth,FileDate,FileYear";
            columnClause += ",FileMonth,FileDay,IsSRNSubmitted,IsSRNOutOfSLA,IsFileOutOfSLA,TotalDaysOutOfSLA,IsFileLoaded";
            columnClause += ",IsSRNLoaded,DTHPushDate,DTHPullDate,DimDTHReportType";
            // Build where clause

            // Get filtered SRNs from Connect DB
            var filteredSRNs = parameters.SrnNumber;

            // Build where clause using filtered SRNs
            var whereClause = ReportUtil.GetDataWarehouseOutOfSLAWhereClause(filteredSRNs, parameters);

            if(!string.IsNullOrEmpty(dimDTHReportType))
                whereClause += $" AND DimDTHReportType = '" + dimDTHReportType + "'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = columnClause,
                Where = whereClause
            };
            var result = _dataWarehouseService.GetResultArray<FileOutOfSlaItem>(_reportTables.DailyOutOfSlaView, apiCallModel);

            return result;
        }

        public BarChart GetMemberSrnNonSubmissionVsOutOfSlaChart(RejectionReportParameters parameters)
        {
            // We need 3 months of data
            var monthStart = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(-2);
            var monthEnd = new DateTime(parameters.Year, parameters.Month, 1).AddMonths(1).AddDays(-1);

            // Build where clause
            var whereClause = "DimDTHReportType = 'File Submission Analysis'";

            if (parameters.MemberName != null && parameters.MemberName.Count() > 0)
                whereClause += $" AND MemberCompanyName IN (" + string.Join(",", parameters.MemberName.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.SrnNumber != null && parameters.SrnNumber.Count() > 0)
                whereClause += $" AND SRNNumber IN (" + string.Join(",", parameters.SrnNumber.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Count() > 0)
                whereClause += $" AND CreditInformationClassification IN (" + string.Join(",", parameters.CreditInformationClassification.Result.Select(m => "'" + m.Replace("'", "''") + "'")) + ")";

            whereClause += $" AND FileDate BETWEEN '{monthStart:yyyy-MM-dd}' AND '{monthEnd:yyyy-MM-dd}'";

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "FileYear,FileMonth,ChartCalculationValue = COUNT(DISTINCT (CASE WHEN IsSRNOutOfSLA = 'YES' THEN SRNNumber ELSE NULL END))",
                Where = whereClause,
                GroupBy = "FileYear,FileMonth"
            };
            var sourceData = _dataWarehouseService.GetResultArray<ChartDataModel>(_reportTables.DailyOutOfSlaView, apiCallModel);

            var chart = new BarChart()
            {
                ChartData = new List<LineChartDataset>()
                {
                    new LineChartDataset() { label = "SRNs Out Of SLA" }
                }
            };

            var startDate = monthStart;
            while (startDate <= monthEnd)
            {
                var label = startDate.ToString("MMMM yyyy");
                chart.ChartLabels.Add(label);

                // SRN’s Out of SLA - DISTINCT COUNT OF SRNs WHERE IsSRNOutOfSLA = YES.
                var srnsOutOfSLACount = sourceData
                       .Result.Where(o => o.FileYear == startDate.Year && o.FileMonth == startDate.Month).Result.Select(o => o.ChartCalculationValue).Result.FirstOrDefault();
                chart.ChartData.First(d => d.label == "SRNs Out Of SLA").data.Add(srnsOutOfSLACount);
               
                startDate = startDate.AddMonths(1);
            }

            chart.ChartName = "Member SRNs Out Of SLA Last 3 Months";
            return chart;
        }
    }
}
